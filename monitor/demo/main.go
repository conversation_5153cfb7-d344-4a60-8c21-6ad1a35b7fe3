// http://blog.csdn.net/kongxx/article/details/51668547
package main

import "github.com/shirou/gopsutil/mem"
import (
	"liasica/dora/logger"
	"fmt"
	"time"
	"os/exec"
)

func checkMemory() {
	for {
		time.Sleep(10 * time.Second)
		v, err := mem.VirtualMemory()
		if err == nil {
			fmt.Println(v.Total)
			fmt.Println(v.Available)
			fmt.Println(v.Used)
			fmt.Println(v.Free)
			fmt.Println(v.UsedPercent)
			if v.UsedPercent >= 60 {
				logger.App().Infof("当前内存占用过高，已达：%.2f%%，需要重启电脑", v.UsedPercent)
				fmt.Println("call restart...")
				restart()
			}
		} else {
			logger.App().Errorf("内存读取失败：%v", err)
		}
	}
}
func main() {
	 checkMemory()
}

func restart() {
	args := []string{
		"/r",
		"/f",
		"/t",
		"0",
	}
	exec.Command("shutdown", args...).Run()
}
