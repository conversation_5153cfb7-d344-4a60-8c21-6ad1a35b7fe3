package main

import (
	"dora/bridge"
	"dora/helpers"
	"dora/logger"
	"dora/monitor/processlib"
	"dora/updater/handles"
	"dora/windows"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/hprose/hprose-golang/rpc"
	"github.com/shirou/gopsutil/mem"
)

var updating bool
var curVersion string
var newVersion string

var stub = new(bridge.Stub)
var connectRetry = 0

// needUpdate 判断文件改动
// smap 服务器版本
func needUpdate(smap, lmap map[string]handles.FileVersion) (need []handles.FileVersion) {
	for s, f := range smap {
		m, ok := lmap[s]
		if ok && f.MD5 == m.MD5 {
			fmt.Println(m.MD5)
			continue
		}
		fmt.Println(f)
		need = append(need, f)
	}
	return
}

func checkUpdate(api string) bool {
	result := false
	base, _ := os.Getwd()
	// 获取更新
	if resp, err := http.Get(api + "version.txt"); err == nil {
		defer resp.Body.Close()
		// 读取内容
		if b, err := ioutil.ReadAll(resp.Body); err == nil {
			// 解析数据
			smap := map[string]handles.FileVersion{}
			newVersion, smap = handles.GetVersionMap(api, string(b))
			logger.App().Infof("服务器版本为：%s", newVersion)
			// 比较本地版本
			lb, err := ioutil.ReadFile(filepath.Join(base, "version.txt"))
			var need []handles.FileVersion
			lmap := map[string]handles.FileVersion{}
			sameVer := false
			if err == nil {
				curVersion, lmap = handles.GetVersionMap(api, string(lb))
				sameVer = newVersion == curVersion
				fmt.Println(sameVer)
			} else {
				// 计算本地文件md5
				curVersion = "unknownVersion" + time.Now().String()
				ln := len(base)
				filepath.Walk(base, func(path string, info os.FileInfo, err error) (e error) {
					if !info.IsDir() && !windows.PathHidden(path) {
						rs := []rune(path)
						file := string(rs[ln:])
						m, e := helpers.GenerateHashFileMD5(path)
						if e == nil {
							lmap[file] = handles.FileVersion{File: file, MD5: m, Index: "0"}
						}
					}
					return
				})
			}
			if !sameVer {
				need = needUpdate(smap, lmap)
				logger.App().Infof("需要更新%d个文件", len(need))
				if len(need) > 0 {
					newVer := filepath.Join(base, newVersion)
					os.MkdirAll(newVer, 0755)
					newVerTxt := filepath.Join(newVer, "version.txt")
					out, err := os.Create(newVerTxt)
					if err != nil {
						fmt.Println(newVerTxt)
						panic(err)
					}
					defer out.Close()
					resp, err := http.Get(api + "version.txt")
					if err != nil {
						logger.App().Errorf("文件下载失败 - 网络故障：%v", err)
					} else {
						defer resp.Body.Close()
						_, err = io.Copy(out, resp.Body)
						if err != nil {
							logger.App().Errorf("文件保存失败 - 系统故障：%v,%s", err, newVerTxt)
						} else {
							var wg sync.WaitGroup
							wg.Add(len(need))
							allOk := true
							threadPool := make(chan int, 10)
							for t := 1; t < 11; t++ {
								threadPool <- t
							}
							fn := func(file string, success bool, threadNum int) {
								if !success {
									allOk = false
								}
								wg.Done()
								threadPool <- threadNum
							}
							for _, file := range need {
								work := file
								go func() {
									select {
									case num := <-threadPool:
										go downLoadAndCompare(newVer, work, num, fn)
									}
								}()
							}
							wg.Wait()
							if allOk {
								result = true
							} else {
								logger.App().Errorf("更新失败，本次更新取消")
							}
						}
					}
				}
			}
		}
	}
	return result
}

var start = time.Now()

func downLoadAndCompare(savePath string, file handles.FileVersion, threadNum int, f func(file string, success bool, threadNum int)) {
	logger.App().Infof("第%d个下载协程正在处理文件%s", threadNum, file.File)
	fPath := filepath.Join(savePath, file.File)
	if helpers.FileExist(fPath) {
		md5, e := helpers.GenerateHashFileMD5(fPath)
		if e == nil && md5 == file.MD5 {
			logger.App().Info("热更临时文件夹中已存在文件%s,不再重复下载", fPath)
			f(fPath, true, threadNum)
			return
		} else {
			logger.App().Errorf("热更临时文件夹存在文件，但文件MD5校验失败，%s,本地MD5:%s，服务器MD5：%s", fPath, md5, file.MD5)
			os.Remove(fPath)
		}
	}
	start = time.Now()
	url := file.URL
	headResp, err := http.Head(url)
	if err != nil {
		logger.App().Errorf("文件下载失败 - 网络故障：%v,%s", err, url)
		f("", false, threadNum)
		return
	}

	defer headResp.Body.Close()
	// 判断文件是否存在
	if headResp.StatusCode != 200 {
		logger.App().Errorf("文件下载失败 - 文件不存在：%v\t%s", headResp.StatusCode, url)
		f("", false, threadNum)
		return
	}

	end := strings.LastIndex(fPath, "\\")
	subpath := helpers.SubString(fPath, 0, end)
	os.MkdirAll(subpath, 0755)
	out, err := os.Create(fPath)
	if err != nil {
		logger.App().Error(err)
		panic(err)
	}
	defer out.Close()
	resp, err := http.Get(url)
	if err != nil {
		logger.App().Errorf("文件下载失败 - 网络故障：%v", err)
		f("", false, threadNum)
		return
	}
	defer resp.Body.Close()
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		logger.App().Errorf("文件保存失败 - 系统故障：%v,%s", err, url)
		f("", false, threadNum)
		return
	}
	elapsed := time.Since(start)
	logger.App().Printf("文件下载完成，用时：%s,%s", elapsed, url)

	md5, e := helpers.GenerateHashFileMD5(fPath)
	if e == nil && md5 == file.MD5 {
		f(fPath, true, threadNum)
	} else {
		logger.App().Errorf("文件MD5校验失败，%s,本地MD5:%s，服务器MD5：%s", fPath, md5, file.MD5)
		os.Remove(fPath)
		f("", false, threadNum)
		return
	}
}

//var info = new(bridge.VersionInfo)
var dora_started = false

func main() {
	logger.App().Info("开始启动监控程序")
	client := rpc.NewClient("http://127.0.0.1:9004/")
	client.UseService(&stub)
	defer client.Close()
	// 内存检测
	go checkMemory()
	go checkRestart()

	bootupFlag := true // 启动时，发现新版本即强制更新
	// 更新
	go func() {
		// 首次启动延迟20秒检测更新
		time.Sleep(20 * time.Second)
		for {
			if !updating {
				//version := common.GetVersion(1, 1)
				// 检查更新
				api, err := stub.Api()
				api = api + "dora/"
				// api := "http://dora-terminal.oss-cn-zhangjiakou.aliyuncs.com/dora/"
				logger.App().Info("开始检查更新")
				needUpdate := checkUpdate(api)
				if needUpdate {
					// 暂停检测更新线程 待更新
					idle, _ := stub.Idle()
					logger.App().Infof("检测到有可用更新，当前IDLE状态%t", idle)
					//fmt.Println(idle)
				IDLELOOP:
					for {
						idle, err = stub.Idle()
						logger.App().Infof("当前IDLE状态%t",idle)
						if idle || bootupFlag == true {
							logger.App().Println("Start to bootup update with finding new version...")
							updating = true
							args := []string{"/f", "/im", "dora.exe"}
							err = exec.Command("taskkill", args...).Run()
							args = []string{"/f", "/im", "USBDISKPRINT.exe"}
							exec.Command("taskkill", args...).Run()
							if err == nil {
								fmt.Printf("kill: %v\n", err)
								logger.App().Info("正在更新...")
								dir, _ := os.Getwd()
								oldVer := filepath.Join(dir, curVersion)
								os.MkdirAll(oldVer, 0755)
								newVer := filepath.Join(dir, newVersion)
								pathes := []string{
									"html",
									"dora.exe",
									"VERSION",
									"vendor",
									"version.txt",
								}
								logger.App().Info("开始备份当前版本")
								//备份当前版本
								for i := 0; i < len(pathes); i++ {
									src := pathes[i]
									fmt.Println(src)
									file, err := os.Stat(src)
									if err != nil {
										logger.App().Errorf("备份当前版本时文件/文件夹获取失败%v\n", err)
										//helpers.ShowMessage("文件/文件夹获取失败", fmt.Sprintf("%v\n", err))
										//os.Exit(1)
									}
									if file.IsDir() {
										helpers.CopyDir(src, filepath.Join(oldVer, src))
									} else {
										helpers.CopyFile(src, filepath.Join(oldVer, src))
									}
								}
								logger.App().Info("当前版本备份结束，开始替换新版本")
								// retry to kill the dora.exe in the boot up update process
								args := []string{"/f", "/im", "dora.exe"}
								err = exec.Command("taskkill", args...).Run()
								if err == nil {
									logger.App().Info("retry to kill dora.exe OK")
								} else {
									logger.App().Errorf("retry to kill dora.exe: %v\n", err)
								}

								//拷贝替换新版本
								for i := 0; i < len(pathes); i++ {
									src := pathes[i]
									fmt.Println(src)
									file, err := os.Stat(filepath.Join(newVer, src))
									if err != nil {
										logger.App().Errorf("文件/文件夹获取失败%v\n", err)
										//helpers.ShowMessage("文件/文件夹获取失败", fmt.Sprintf("%v\n", err))
										//updating = false
										//os.Exit(1)
									} else {
										if file.IsDir() {
											helpers.CopyDir(filepath.Join(newVer, src), filepath.Join(dir, src))
										} else {
											helpers.CopyFile(filepath.Join(newVer, src), filepath.Join(dir, src))
										}
									}
								}
								logger.App().Info("更新完成")
							} else {
								logger.App().Errorf("dora.exe关闭失败:%v", err)
							}
							bootupFlag = false
							updating = false
							break IDLELOOP
							// complete the boot up process
						}
						//5s检测一次idle状态
						time.Sleep(5 * time.Second)
					}
				}
			}

			// 5分钟检测一次更新
			time.Sleep(5 * time.Minute)
		}
	}()

	// 进程常驻
	for {
		if !updating {
			checkDora()
			startMonitor()
		}
		time.Sleep(1 * time.Second)
	}

}

func killBrStMonW() {
	args := []string{
		"/f",
		"/im",
		"BrStMonW.exe",
	}
	exec.Command("taskkill", args...).Run()
}

func startMonitor() {
	// 判断程序中是否包含dora.exe
	processes, err := processlib.Processes()
	if err != nil {
		logger.App().Fatalf("获取进程列表失败：%v", err)
		return
	}
	for _, v := range processes {
		p := v.Executable()

		if p == "dora.exe" {
			dora_started = true
		}
		if p == "FoxitProtect.exe" {
			args := []string{
				"/f",
				"/im",
				"FoxitProtect.exe",
			}
			exec.Command("taskkill", args...).Run()
		}
		if p == "monitor.exe" && v.Pid() != os.Getpid() {
			//helpers.ShowMessage("重复运行", "请勿重复运行monitor！\n")
			logger.App().Errorf("monitor重复运行，pid：%v", v.Pid())
			os.Exit(0)
		}
	}
	if !dora_started && !updating {
		logger.App().Info("monitor检测到dora程序当前没有运行")
		dir, err := os.Getwd()
		if err != nil {
		}
		dora := dir + string(os.PathSeparator) + "dora.exe"
		_, err = os.Stat(dora)
		if err != nil {
			helpers.ShowMessage("目录错误", "请将此程序和主程序放到同一目录下！\n"+fmt.Sprintf("%v", err)+"\n"+dora)
			os.Exit(1)
		} else {
			logger.App().Info("正在启动dora")
			err := exec.Command(dora).Run()
			if err != nil {
				logger.App().Errorf("启动dora失败：%v", err)
				dora_started = false
			} else {
				connectRetry = 0
				dora_started = true
			}
		}
	}
}

func checkDora() {
	_, err := stub.Idle()
	if err != nil {
		logger.App().Errorf("第%d次检测通信失败：%v", connectRetry, err)
		connectRetry++
	} else {
		connectRetry = 0
	}
	// 主进程超过30次重连失败则强制重新启动主进程
	if dora_started && connectRetry > 30 {
		logger.App().Errorf("主进程30次通信失败，需要强制重启主进程")
		exec.Command("taskkill", "/f", "/im", "dora.exe").Run()
		dora_started = false
	}

	// 300s后主进程启动失败则重启电脑
	if connectRetry > 300 {
		logger.App().Errorf("程序判定启动失败，开始重启电脑")
		restart()
	}
}

func checkRestart() {
	for {
		// 定时重启计算机
		t := time.Now().Format("15:04:05")
		ts := strings.Split(t, ":")
		h, _ := strconv.Atoi(ts[0])
		for {
			idle, err := stub.Idle()
			if err != nil {
				logger.App().Error("定时重启检测时与Dora通信失败，5秒后重新尝试")
			} else {
				// 每天凌晨3点到5点自动重启一次电脑
				if h >= 2 && h <= 5 && idle {
					os.MkdirAll("restart", 0755)
					d := time.Now().Format("20060102")
					_, err := os.Stat("restart/" + d)
					if os.IsNotExist(err) && !updating {
						fmt.Println("正在重启...")
						os.Create("restart/" + d)
						restart()
					}
				}else {
					logger.App().Info("当前打印机不处于空闲状态或不是有效时间段，不执行重启")
				}
				break
			}
			time.Sleep(5 * time.Second)
		}
		time.Sleep(1 * time.Hour)
	}
}

func checkMemory() {
	jici := 0
	for {
		v, err := mem.VirtualMemory()
		if err == nil {
			if v.UsedPercent >= 95 {
				jici = jici + 1
				logger.App().Infof("当前内存占用过高，已达：%.2f%%，需要重启电脑", v.UsedPercent)
				isPrinting, _ := stub.IsPrinting()
				if isPrinting {
					logger.App().Info("当前打印机正在打印，取消重启操作")
				} else {
					if jici > 6 && !updating {
						restart()
					}
				}
			}
		} else {
			logger.App().Errorf("内存读取失败：%v", err)
		}
		time.Sleep(30 * time.Second)
	}
}

func restart() {
	fmt.Println("开始重启电脑")
	args := []string{
		"/r",
		"/f",
		"/t",
		"0",
	}
	exec.Command("shutdown", args...).Run()
}
