@echo off
echo "delete old version..."
del /a /f dora.exe
del /a /f monitor.exe
rd /s /q html\src
del /a /f html\.babelrc
del /a /f html\.gitignore
del /a /f html\gulpfile.js
del /a /f html\package.json
del /a /f html\webpack.config.js
del /a /f html\webpack.config.prod.js
del /a /f html\yarn.lock
set /p a=<VERSION
echo "compile new version %a% ..."
go build -ldflags "-H windowsgui"
set /p b=<MONITORVERSION
echo "compile monitor %b% ..."
go build -ldflags "-H windowsgui" monitor/monitor.go
echo "release..."
:: cd /d release
go run release/release.go
echo "compoleted, open dir..."
start "" "release\\binaries"

pause