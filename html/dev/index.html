<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>多拉打印</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <!-- Insert this line above script imports  -->
    <script>if (typeof module === 'object') {
        window.module = module;
        module = undefined;
    }</script>

    <!-- normal script imports etc  -->
    <script src="js/jquery-3.2.1.min.js"></script>
    <!--<script src="scripts/vendor.js"></script>-->

    <!-- Insert this line after script imports -->
    <script>if (window.module) module = window.module;</script>
    <style>
        code,
        #message {
            word-wrap: break-word;
            word-break: break-all;
        }
    </style>
</head>
<body>
<div class="container" style="padding-top: 15px;">
    <div class="row">
        <div class="col-md-6" style="width: 50%; padding-right: 15px; border-right: #DEDEDE 1px solid; overflow-y: scroll; height: calc(100vh - 25px);">
            <div class="col-md-12">
                <button data-command="BARCODESCAN" class="btn btn-success send">扫码</button>
                <button data-command="BARCODECANCEL" class="btn btn-danger send">取消扫码</button>
                <button data-command="BARCODESTATUS" class="btn btn-info send">获取状态</button>
            </div>
            <div class="col-md-12" style="margin-top: 15px;">
                <button data-command="TERMINALIDLE" data-idle="idle" class="btn btn-warning send">标记为空闲</button>
                <button data-command="TERMINALIDLE" data-idle="" class="btn btn-danger send">标记为非空闲</button>
                <button data-command="VERSION" data-idle="" class="btn btn-info send">获取版本号</button>
            </div>
            <div class="col-md-12" style="margin-top: 15px;">
                <button class="btn btn-info send" data-command="PRINTERSTATUS">获取打印机状态</button>
            </div>
            <div class="col-md-12" style="margin-top: 15px;">
                <div class="form-group">
                    <label for="files">文件下载</label>
                    <textarea id="files" class="form-control" rows="5"></textarea>
                </div>
                <div id="process-bar"></div>
                <button class="btn-success btn send" data-command="FILEDOWNLOAD">开始下载</button>
            </div>
            <div class="col-md-12" style="margin-top: 15px;">
                <div class="form-group">
                    <label for="printing">打印文件</label>
                    <textarea id="printing" rows="5" class="form-control"></textarea>
                    <div id="example">
                        例子：<code>{"action":"PRINTERPRINTING","disk_file":{"file":"D:\\temp\\test.pdf","copies":1,"duplex":false,"from":2,"to":2}}</code>
                    </div>
                </div>
                <button class="btn btn-success printing send">开始打印</button>
            </div>
            <div class="col-md-12" style="margin-top: 15px;">
                <button class="btn btn-info send" data-command="USBDETECTOR">获取USB存储设备</button>
                <button class="btn btn-danger send" data-command="USBEJECT">移除USB存储设备</button>
            </div>
            <div class="col-md-12" style="margin-top: 15px;">
                <label for="printing">获取文件页数</label>
                <div class="input-group">
                    <input id="filepages" class="form-control" placeholder="D:\temp\test.doc">
                    <span class="input-group-btn">
                        <button class="btn btn-success filepages send" data-command="FILEPAGES">Go!</button>
                    </span>
                </div>
            </div>

            <div class="col-md-12" style="margin-top: 15px;">
                <div class="form-group">
                    <label for="printing">打印本地文件</label>
                    <textarea id="diskprinting" rows="5" class="form-control"></textarea>
                    <div class="example">
                        例子：<code>{"action":"DISKPRINTING","disk_file":{"file":"D:\\Downloads\\apple.pptx","copies":1,"duplex":1,"from":1,"to":1,"pages_per_sheet":1}}</code>
                    </div>
                </div>
                <button class="btn btn-success diskprinting send">开始打印</button>
            </div>
        </div>
        <div class="col-md-6" style="width: 50%; position:absolute; right: 0; top: 15px; overflow-y: scroll; height: calc(100vh - 25px);">
            <span class="glyphicon glyphicon-trash delete" style="width: 20px; height: 20px; position: fixed; right: 15px; top: 20px; z-index: 9999; cursor: pointer;"></span>
            <div id="message" style="height: 100%;"></div>
        </div>
    </div>
</div>
<script>
    (function ($) {
        $.fn.exist = function () {
            if ($(this).length >= 1) {
                return true;
            }
            return false;
        };
    })(jQuery);

    document.addEventListener('liasicaectron-ready', function () {
        liasicaectron.listen(function (message) {
            console.info(message);
            var msg = JSON.parse(message);
            console.info(msg);
            message = '终端收到消息：' + message;
            $('#message').html(message + '<br>' + $('#message').html());
//            if (msg.action == 'FILEDOWNLOAD') {
//                if (msg.status == "downloading") {
//                    var files = msg.files;
//                    for (var i in files) {
//                        var id = i;
//                        var obj = files[i];
//                        if (!$('#' + id).exist()) {
//                            var html = '<div class="progress"> ' +
//                                '<div id="' + id +
//                                '" class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"> ' +
//                                '<span class="text">0%</span></div></div>';
//                            $('#process-bar').append(html);
//                        }
//                        $('#' + id).css('width', obj.progess + '%');
//                        $('#' + id).attr('aria-valuenow', obj.progess);
//                        $('#' + id + ' span').text(obj.progess + '%');
//                    }
//                } else {
//                    setTimeout(function () {
//                        $('#process-bar').fadeOut(function () {
//                            $(this).empty();
//                            $('#process-bar').fadeIn();
//                        });
//                    }, 2000);
//                }
//            }
        });

        $('.delete').click(function () {
            $('#message').text('');
        });

        $('.send').click(function () {
            var command = {
                action: $(this).data('command'),
                detail: ''
            };
            if ($(this).hasClass('printing')) {
                command = JSON.parse($('#printing').val());
            }
            if ($(this).hasClass('diskprinting')) {
                command = JSON.parse($('#diskprinting').val());
            }
            if (command.action == "FILEDOWNLOAD") {
                command.url = $('#files').val();
            }
            if (command.action == "TERMINALIDLE") {
                command.detail = $(this).data('idle')
            }
            if (command.action == "FILEPAGES") {
                command.disk_file = {
                    file: $('#filepages').val(),
                }
            }
            console.info('send', JSON.stringify(command));
            liasicaectron.send(JSON.stringify(command));
        });
    });
</script>
</body>
</html>