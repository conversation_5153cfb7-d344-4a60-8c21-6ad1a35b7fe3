package main

import (
	"dora/bridge"
	"dora/common"
	"dora/helpers"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	//"liasica/dora/logger"
	//"liasica/dora/updater/handles"
	"dora/windows"
	//"archive/zip"
	"bytes"
	//"archive/zip"
	//"strings"
)

const (
	BINARIESDIR    = "release/binaries/"
	MONITORDIR     = "release/binaries/monitor/"
	BINARIESDIRNew = "release/binaries/files/"
)

type gzReader struct {
	z, r io.ReadCloser
}

func (g *gzReader) Read(p []byte) (int, error) {
	return g.z.Read(p)
}

func (g *gzReader) Close() error {
	g.z.Close()
	return g.r.Close()
}

func createBuildDir() {
	os.MkdirAll(filepath.Join(MONITORDIR, common.GetVersion(1, 2)), 0755)
	os.MkdirAll(BINARIESDIRNew, 0755)
}

func createReleaseNew() {
	v := common.GetVersion(1, 1)
	pathes := []string{
		"html",
		"dora.exe",
		"VERSION",
		"vendor",
	}
	b := bytes.Buffer{}
	b.WriteString(v + "\r\n")
	for i := 0; i < len(pathes); i++ {
		src := pathes[i]
		fmt.Println(src)
		file, err := os.Stat(src)
		if err != nil {
			helpers.ShowMessage("文件/文件夹获取失败", fmt.Sprintf("%v\n", err))
			os.Exit(1)
		}

		if file.IsDir() {
			helpers.CopyDir(src, filepath.Join(BINARIESDIRNew, src))
		} else {
			helpers.CopyFile(src, filepath.Join(BINARIESDIRNew, src))
		}
		filepath.Walk(src, func(path string, info os.FileInfo, err error) (e error) {
			if !info.IsDir() && !windows.PathHidden(path) {
				m, e := helpers.GenerateHashFileMD5(path)
				if e == nil {
					b.WriteString(m)
					b.WriteString(" ")
					b.WriteString(path)
					b.WriteString("\r\n")
				}
			}
			return
		})
	}

	err := ioutil.WriteFile(filepath.Join(BINARIESDIRNew, "version.txt"), b.Bytes(), 0755)
	if err != nil {
		log.Fatalf("3\n%v", err)
		panic(err)
	}
}

func monitorRelease() {
	v := common.GetVersion(1, 2)
	path := filepath.Join(MONITORDIR, v, "monitor.exe")
	_, err := helpers.CopyFile("monitor.exe", path)
	if err != nil {
		fmt.Println("error:", err)
		return
	}
	md5, err := helpers.GenerateHashFileMD5(path)
	if err != nil {
		fmt.Println("error:", err)
		return
	}
	c := bridge.VersionInfo{Version: v, FileMD5: md5}
	b, err := json.MarshalIndent(c, "", "    ")
	if err != nil {
		fmt.Println("error:", err)
		return
	}
	ioutil.WriteFile(filepath.Join(MONITORDIR, "monitor.json"), b, 0755)
}

func main() {
	createBuildDir()
	createReleaseNew()
	//createRelease()
	monitorRelease()
}
