package logger

import (
	"dora/helpers"
	"os"
	"time"
	"github.com/sirupsen/logrus"
	"io"
)

// 各日志文件
var (
	appLog     = "app"     // App日志
	msgLog     = "message" // message日志
	staLog     = "status"  // status日志
	barcodeLog = "barcode" // 条码扫描器日志
	printerLog = "printer" // A4打印机日志
)

// 初始化log
var log = logrus.New()

func init() {
	dir, _ := os.Getwd()

	var separator = string(os.PathSeparator)
	path := dir + separator + "logs" + separator
	helpers.GetPath(path)

	barcodeLog = path + barcodeLog + time.Now().Format("20060102") + ".log"
	printerLog = path + printerLog + time.Now().Format("20060102") + ".log"
	appLog = path + appLog + time.Now().Format("20060102") + ".log"
	msgLog = path + msgLog + time.Now().Format("20060102") + ".log"
	staLog = path + staLog + time.Now().Format("20060102") + ".log"

	//log.Formatter = &logrus.JSONFormatter{TimestampFormat: "2006-01-02 15:04:05"}
	log.Formatter = &logrus.TextFormatter{TimestampFormat: "2006-01-02 15:04:05"}
	log.Level = logrus.DebugLevel
}

// App 日志
func App() *logrus.Logger {
	if file, err := os.OpenFile(appLog, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666); err == nil {
		log.Out = io.MultiWriter(file, os.Stdout)
	}
	return log
}

// Status 日志
func Stats() *logrus.Logger {
	if file, err := os.OpenFile(staLog, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666); err == nil {
		log.Out = io.MultiWriter(file, os.Stdout)
	}
	return log
}

// Message 日志
func Message() *logrus.Logger {
	if file, err := os.OpenFile(msgLog, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666); err == nil {
		log.Out = io.MultiWriter(file, os.Stdout)
	}
	return log
}

// Barcode 扫描头日志
func Barcode() *logrus.Logger {
	if file, err := os.OpenFile(barcodeLog, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666); err == nil {
		log.Out = io.MultiWriter(file, os.Stdout)
	}
	return log
}

// Printer A4打印机日志
func Printer() *logrus.Logger {
	if file, err := os.OpenFile(printerLog, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666); err == nil {
		log.Out = io.MultiWriter(file, os.Stdout)
	}
	return log
}

func WithFields(fields logrus.Fields) *logrus.Entry {
	return log.WithFields(fields)
}

// Global logger shortcuts
func Debug(v ...interface{})                 { log.Debug(v...) }
func Debugf(format string, v ...interface{}) { log.Debugf(format, v...) }
func Info(v ...interface{})                  { log.Info(v...) }
func Infof(format string, v ...interface{})  { log.Infof(format, v...) }
func Warn(v ...interface{})                  { log.Warn(v...) }
func Warnf(format string, v ...interface{})  { log.Warnf(format, v...) }
func Error(v ...interface{})                 { log.Error(v...) }
func Errorf(format string, v ...interface{}) { log.Errorf(format, v...) }
func Fatal(v ...interface{})                 { log.Fatal(v...) }
func Fatalf(format string, v ...interface{}) { log.Fatalf(format, v...) }
