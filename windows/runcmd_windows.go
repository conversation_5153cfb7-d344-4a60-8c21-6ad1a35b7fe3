package windows

import (
	"context"
	"os"
	"os/exec"
	"syscall"
	"time"
	"errors"
)

// RunCMD run other windows program with no console window
func RunCMD(name string, arg ...string) ([]byte, error) {
	ctx, _ := context.WithCancel(context.Background())
	cmd := exec.CommandContext(ctx, name, arg...)
	cmd.SysProcAttr = &syscall.SysProcAttr{HideWindow: true}
	cmd.Stderr = os.Stderr
	//cmd.Stdout = os.Stdout
	out, err := cmd.Output()
	return out, err
}
func RunCMDWithTimeout(name string, arg ...string) (re []byte, err error, timeout bool) {
	timeout = false
	ctx, cancel := context.WithCancel(context.Background())
	cmd := exec.CommandContext(ctx, name, arg...)
	cmd.SysProcAttr = &syscall.SysProcAttr{HideWindow: true}
	timer := time.NewTimer(time.Minute * 10)
	execCh := make(chan bool)
	go func() {
		re, err = cmd.Output()
		execCh <- true
	}()
	select {
	case <-timer.C:
		timeout = true
		err = errors.New("RunCMD超时")
		cancel()
		timer.Stop()
	case <-execCh:
		timer.Stop()
	}
	return
}
