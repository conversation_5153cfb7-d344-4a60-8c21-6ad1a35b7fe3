package windows

import (
	"path/filepath"
	"syscall"
	"strings"
	"os"
)

func PathHidden(path string) bool {
	if is<PERSON>idden(path) {
		return true
	}

	// check parent directory
	abs, _ := filepath.Abs(filepath.Dir(path))
	pathes := strings.Split(abs, string(os.PathSeparator))
	if len(pathes) > 0 {
		dir := pathes[0]
		for i := 1; i < len(pathes); i++ {
			dir = dir + string(os.PathSeparator) + pathes[i]
			if isHidden(dir) {
				return true
			}
		}
		return false
	} else {
		return true
	}
}

func isHidden(path string) bool {
	pathunit, _ := syscall.UTF16PtrFromString(path)
	attrs, _ := syscall.GetFileAttributes(pathunit)
	if attrs == syscall.FILE_ATTRIBUTE_HIDDEN {
		return true
	}
	return false
}
