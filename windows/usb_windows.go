package windows

import (
    "bufio"
    "bytes"
    "strings"
    "os"
    "fmt"
    "github.com/json-iterator/go"
    "path/filepath"
    "strconv"
)

// EjectUsb remove all usb drives
func EjectUsb(path string) (string, error) {
    ret, err := RunCMD(filepath.Join(path, "GetUsbDrives.exe"), "eject")
    if err != nil {
        return "", err
    }
    return string(ret), nil
}

// UsbDrives get all usb drives for windows readable
// bool - has got drive
func GetUsbDrives(path string, timeout int) (bool, map[string]string) {
    ret, err := RunCMD(filepath.Join(path, "GetUsbDrives.exe"), strconv.Itoa(timeout))
    if err != nil {
        return false, nil
    }
    got := false
    _drives := map[string]string{}
    drives := map[string]string{}

    jsoniter.Unmarshal(ret, &_drives)

    got = _drives["result"] == "true"
    for k, v := range _drives {
        if k != "result" {
            drives[k] = v
        }
    }
    return got, drives
}

func getLetter() (error, bool, []string) {
    args := []string{
        "logicaldisk",
        "where",
        "drivetype=2",
        "get",
        "deviceid",
    }
    out, err := RunCMD("wmic", args...)
    if err != nil {
        return err, false, nil
    }

    var drives []string
    driveMap := make(map[string]bool)

    // read out bytes
    s := bufio.NewScanner(bytes.NewReader(out))
    for s.Scan() {
        line := s.Text()
        // get drive root path
        if strings.Contains(line, ":") {
            rootPath := strings.TrimSpace(line) + string(os.PathSeparator)
            driveMap[rootPath] = true
        }
    }

    // get drive map for all drives
    for k := range driveMap {
        f, err := os.Open(k)
        defer f.Close()
        if err != nil {
            return err, false, nil
        }
        drives = append(drives, k)
    }
    return nil, len(drives) > 0, drives
}

func UsbDiskDrive() {
    out, _ := RunCMD("wmic", "diskdrive", "where", "interfacetype='usb'", "get", "PNPDeviceID,deviceid,CAPTION")
    s := string(out)
    for _, v := range strings.Split(s, " ") {
        fmt.Println("v: " + strings.Trim(v, " "))
    }
}
