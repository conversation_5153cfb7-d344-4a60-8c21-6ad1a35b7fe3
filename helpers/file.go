package helpers

import (
	"fmt"
	"os"

	"github.com/qiniu/api.v7/auth/qbox"
	"github.com/qiniu/api.v7/storage"
)

func FileExist(file string) bool {
	if _, err := os.Stat(file); err != nil && os.IsNotExist(err) {
		return false
	}
	return true
}

const (
	QINIU_ACCESS_KEY    = "MnA5mjR5ENbr_JN07MeiNEDnW3AvW_XmbQ__kspR"
	QINIU_SECRET_KEY    = "VhvOtceE2j4JKq2-ItyIU6ANUl8qPdeSBjBdqAb5"
	QINIU_SECRET_BUCKET = "duola-secret"
)

func FileExistAndNormal(file, filePath string) bool {
	if !FileExist(filePath) {
		return false
	} else {
		//通过API获取云端HASH值
		mac := qbox.NewMac(QINIU_ACCESS_KEY, QINIU_SECRET_KEY)
		cfg := storage.Config{
			// 是否使用https域名进行资源管理
			UseHTTPS: false,
		}
		// 指定空间所在的区域，如果不指定将自动探测
		// 如果没有特殊需求，默认不需要指定
		//cfg.Zone=&storage.ZoneHuabei
		bucketManager := storage.NewBucketManager(mac, &cfg)
		fileInfo, sErr := bucketManager.Stat(QINIU_SECRET_BUCKET, file)
		if sErr != nil {
			fmt.Println(sErr)
			return false
		}
		qiniuHash := fileInfo.Hash
		localHash, err := GetHash4QiNiu(filePath)
		if err != nil {
			os.Remove(filePath)
			return false
		}
		if localHash == qiniuHash {
			return true
		} else {
			os.Remove(file)
			return false
		}
	}
}
