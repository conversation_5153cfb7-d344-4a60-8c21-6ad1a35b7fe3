package helpers

import (
	"syscall"
	"unsafe"
)

func GetFreeSpaceOfDisk(disk string) (free int) {
	kernel32, err := syscall.LoadLibrary("Kernel32.dll")
	if err != nil {
		return -1
	}
	defer syscall.FreeLibrary(kernel32)
	GetDiskFreeSpaceEx, err := syscall.GetProcAddress(syscall.Handle(kernel32), "GetDiskFreeSpaceExW")
	if err != nil {
		return -1
	}

	lpFreeBytesAvailable := int64(0)
	lpTotalNumberOfBytes := int64(0)
	lpTotalNumberOfFreeBytes := int64(0)

	ptr, err := syscall.UTF16PtrFromString(disk)
	if err != nil {
		return -1
	}
	_, _, _ = syscall.Syscall6(uintptr(GetDiskFreeSpaceEx), 4,
		uintptr(unsafe.Pointer(ptr)),
		uintptr(unsafe.Pointer(&lpFreeBytesAvailable)),
		uintptr(unsafe.Pointer(&lpTotalNumberOfBytes)),
		uintptr(unsafe.Pointer(&lpTotalNumberOfFreeBytes)), 0, 0)
	free = int(lpTotalNumberOfFreeBytes / 1024 / 1024.0)
	return
}
