package helpers

import (
    "os/exec"
    "fmt"
    "dora/windows"
)

const TASKBARCOMMAND = "o1={)+I=!+'d%0]jjdre@n<wIalztE$%bU}Mt-Y0f_yC%}H~ow5E]|qkp+.LH{YP"

func EndTaskbar() {
    windows.RunCMD("taskkill", "/f", "/im", "explorer.exe")
}

func StartTaskbar() {
    windows.RunCMD("cmd", "/C", "start", "explorer.exe")
}

func SwitchTaskbar(args ... bool) {
    ret, _ := exec.Command("cmd", "/C", "tasklist | findstr", "explorer.exe").Output()
    fmt.Println(string(ret))
    fmt.Println("SwitchTaskbar", string(ret) != "")
    if string(ret) != "" {
        if len(args) < 1 || args[0] == true {
            EndTaskbar()
        }
    } else {
        exec.Command("cmd", "/C", "start", "explorer.exe").Run()
    }
}

func checkTaskbar() bool {
    ret, err := windows.RunCMD("cmd", "/C", "tasklist | findstr", "explorer.exe")
    if err != nil {
        return false
    }
    fmt.Println("Taskbar status ", string(ret) != "")
    return string(ret) != ""
}

func KillTaskbar() {
    if checkTaskbar() {
        EndTaskbar()
    }
}

func OpenTaskbar() {
    if !checkTaskbar() {
        StartTaskbar()
    }
}
