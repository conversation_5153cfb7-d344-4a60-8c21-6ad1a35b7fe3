package win32control

// #define WIN32_LEAN_AND_MEAN
// #define NOCRYPT
// #define NOGDI
// #define NOSERVICE
// #define NOMCX
// #define NOIME
// #include <windows.h>
import "C"

import (
    "runtime"
    "dora/logger"
    "time"
)

const (
    ALT      = 1 << iota
    CONTROL
    SHIFT
    WIN
    NOREPEAT uint = 0x4000
)

type Hotkey struct {
    ModKey   C.UINT
    Vk       C.UINT
    Callback Callback
}

type Hotkeys struct {
    hotkeys map[C.int]*Hotkey
    id      C.int
}

type Callback func(id int)

func New() *Hotkeys {
    return &Hotkeys{
        hotkeys: make(map[C.int]*Hotkey),
        id:      0,
    }
}

func (h *Hotkeys) Start() {
    go h.run()
}

func (h *Hotkeys) BindHotkey(ModKey, Vk uint, callback Callback) int {
    h.hotkeys[h.id] = &Hotkey{
        ModKey:   C.UINT(ModKey),
        Vk:       C.UINT(Vk),
        Callback: callback,
    }
    old := h.id
    h.id = h.id+1

    return int(old)
}

func (h *Hotkeys) run() {
    runtime.LockOSThread()

    for id, hk := range h.hotkeys {
        if C.RegisterHotKey(nil, id, hk.ModKey, hk.Vk) == 0 {
            //panic("register hotkey failed")
            logger.App().Error("hotkey %v -> %v register failed", hk.ModKey, hk.Vk)
        }
        time.Sleep(time.Millisecond*50)
    }

    for {
        var msg C.MSG
        C.GetMessage((*C.struct_tagMSG)(&msg), nil, 0, 0)
        if msg.message == C.WM_HOTKEY {
            id := C.int(msg.wParam)
            hk, ok := h.hotkeys[id]
            if ok {
                go hk.Callback(int(id))
            }
        }
        time.Sleep(time.Millisecond*250)
    }
}
