// None GUI for windows
// go build -ldflags "-H windowsgui"
// version 1.8.5
package main

import (
	"dora/bridge"
	"dora/common"
	"dora/components/browser"
	"dora/helpers"
	"dora/logger"
	"dora/win32control"
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/go-ini/ini"
	"github.com/pkg/errors"
)

var (
	kernel32         = syscall.MustLoadDLL("kernel32.dll")
	procSetStdHandle = kernel32.MustFindProc("SetStdHandle")
)

func SetStdHandle(stdhandle int32, handle syscall.Handle) error {
	r0, _, e1 := syscall.Syscall(procSetStdHandle.Addr(), 2, uintptr(stdhandle), uintptr(handle), 0)
	if r0 == 0 {
		if e1 != 0 {
			return error(e1)
		}
		return syscall.EINVAL
	}
	return nil
}

func catchCrash(f *os.File) {
	err := SetStdHandle(syscall.STD_ERROR_HANDLE, syscall.Handle(f.Fd()))
	if err != nil {
		logger.App().Fatalf("流创建失败：%v", err)
	}
}

func crashFile() *os.File {
	file := filepath.Join("logs", "crash"+time.Now().Format("20060102")+".log")
	_, err := os.Stat(file)
	var f *os.File
	if err != nil {
		if os.IsNotExist(err) {
			f, err = os.Create(file)
			if err != nil {
				logger.App().Fatalf("崩溃文件创建失败：%v", err)
			}
		} else {
			logger.App().Fatalf("崩溃文件读取失败：%v", err)
		}
	} else {
		f, err = os.OpenFile(file, os.O_APPEND, 0666)
		if err != nil {
			logger.App().Fatalf("崩溃文件读取失败：%v", err)
		}
	}
	f.WriteString("\n\n\n" + time.Now().Format("2006-01-02 15:04:05") + "\n")
	return f
}

func main() {
	go func() {
		f := crashFile()
		for {
			catchCrash(f)
			time.Sleep(time.Millisecond*500)
		}
	}()

	delerr := exec.Command("cmd", "/C", "del", "*.zip").Run()
	if delerr != nil {
		logger.App().Errorf("zip文件删除失败：%v", delerr)
	}

	// 初始化设备与配置
	devs := &bridge.Support{
		Terminal: &bridge.Terminal{
			Mode:      "pro",
			URL:       "http://127.0.0.1:4000",
			UpdateURL: "http://dora-terminal.oss-cn-zhangjiakou.aliyuncs.com/",
		},
		BarCode: &bridge.BarCode{
			PortName: "COM8",
			BaudRate: "115200",
			TimeOut:  "20",
		},
		Printer: &bridge.Printer{
			NormalName: "Brother HL-5590DN Printer",
			IPAddress:  "***************",
			Tray:       "3",
		},
		File: &bridge.Trapper{
			Path:         `D:\temp`,
			TimeLoop:     "200",
			LastModified: "168", // 一周 168个小时
			Retry:        "3",
			RetryTime:    "5",
		},
		UsbDetector:     bridge.DefaultUSBDetector,
		DiskFileSupport: new(bridge.DiskFileSupport),
	}

	inifile := filepath.Join("conf", "app.ini")
	cfg, _err := ini.Load(inifile)
	cover := ""

	if _err == nil {
		cover = "cover"
		cfg.Section("Terminal").MapTo(devs.Terminal)
		cfg.Section("BarCode").MapTo(devs.BarCode)
		cfg.Section("Printer").MapTo(devs.Printer)
		cfg.Section("File").MapTo(devs.File)
		fmt.Println("ini configure exists")
	}

	// 从注册表中初始化配置
	devs.ConfWithDefaults(cover)
	// 删除配置文件
	os.Remove(inifile)

	// ----------------> Business logic <----------------
	// Parse flags
	flag.Parse()

	go http.ListenAndServe(":4000", http.FileServer(http.Dir("html/dist")))
	go http.ListenAndServe(":4001", http.FileServer(http.Dir("html/dev")))

	// Get base dir path
	var err error
	dir, _ := os.Getwd()
	var p = dir

	// Create astilectron
	var a *liasicaectron.Astilectron
	options := liasicaectron.Options{
		AppName:            "DoraPrinter",
		AppIconDefaultPath: p + "/vendor/dora.png",
		AppIconDarwinPath:  p + "/vendor/dora.icns",
		BaseDirectoryPath:  p,
	}
	if a, err = liasicaectron.New(options); err != nil {
		logger.App().Fatal(errors.Wrap(err, "creating new astilectron failed"))
	}
	defer a.Close()
	a.HandleSignals()

	// Start
	if err = a.Start(); err != nil {
		logger.App().Fatal(errors.Wrap(err, "starting failed"))
	}

	// 更新monitor
	go func() {
		for {
			api := devs.Terminal.UpdateURL + "monitor/"
			info := new(bridge.VersionInfo)
			//fmt.Println(api + "monitor.json")
			logger.App().Info("检查monitor更新中...")
			resp, err := http.Get(api + "monitor.json")
			if err != nil {
				logger.App().Errorf("获取更新文件失败：%v %s", err, api+"monitor.json")
			} else {
				version := common.GetVersion(1, 2)
				defer resp.Body.Close()
				a, _ := ioutil.ReadAll(resp.Body)
				err = json.NewDecoder(strings.NewReader(string(a))).Decode(&info)
				if err != nil {
					logger.App().Errorf("json解析失败：%v\n%s", err, string(a))
				} else {
					logger.App().Infof("monitor服务器版本：%s", info.Version)
					if version != info.Version {
						file := "monitor." + info.Version + ".exe"
						_, err := os.Stat(file)
						if os.IsNotExist(err) {
							// 下载文件
							logger.App().Info("monitor当前版本 " + version + "，检测到更新的版本：" + info.Version)
							// 下载文件
							fmt.Println(api + info.Version + "/monitor.exe")
							resp, err = http.Get(api + info.Version + "/monitor.exe")
							if err != nil {
								logger.App().Errorf("下载monitor更新失败：%v", err)
							}
							defer resp.Body.Close()
							a, _ = ioutil.ReadAll(resp.Body)
							err = ioutil.WriteFile(file, a, 0755)
							if err != nil {
								logger.App().Errorf("下载monitor更新失败：%v", err)
							}
							logger.App().Infof("下载成功:%s", file)
						}
						// 校验文件
						if !helpers.VerifyHashMD5(file, info.FileMD5) {
							// 校验失败，删除包
							os.Remove(file)
							logger.App().Error("monitor文件校验失败，已删除")
						}
						args := []string{
							"/f",
							"/im",
							"monitor.exe",
						}
						err = exec.Command("taskkill", args...).Run()
						if err == nil || fmt.Sprintf("%v", err) == "exit status 128" {
							if os.Rename("monitor.exe", "monitor.back.exe") == nil {
								if os.Rename(file, "monitor.exe") != nil {
									os.Rename("monitor.back.exe", "monitor.exe")
								} else {
									os.Remove("monitor.back.exe")
									// 写入文件
									logger.App().Info("更新完成")
									f, _ := os.Create("MONITORVERSION")
									defer f.Close()
									f.WriteString(info.Version)
								}
							}
							// 启动monitor
							err = exec.Command("monitor.exe").Run()
							if err != nil {
								logger.App().Errorf("monitor启动失败：%v", err)
							}
						} else {
							logger.App().Errorf("monitor关闭失败: %v", err)
						}
					}
				}
			}
			time.Sleep(10 * time.Minute)
		}
	}()

	// Create window
	var w *liasicaectron.Window
	winOptions := &liasicaectron.WindowOptions{
		Center:        liasicaectron.PtrBool(true),
		Height:        liasicaectron.PtrInt(900),
		Width:         liasicaectron.PtrInt(1400),
		TitleBarStyle: liasicaectron.TitleBarStyleHidden,
		Frame:         liasicaectron.PtrBool(false),
	}
	if devs.Terminal.Mode != "dev" {
		winOptions.AlwaysOnTop = liasicaectron.PtrBool(true)
		winOptions.Frame = liasicaectron.PtrBool(false)
		winOptions.Fullscreen = liasicaectron.PtrBool(true)
	}
	if w, err = a.NewWindow(devs.Terminal.URL+"?timestamp="+strconv.Itoa(int(time.Now().Unix())), winOptions); err != nil {
		logger.App().Fatal(errors.Wrap(err, "new window failed"))
	}

	// 创建窗口
	if err = w.Create(); err != nil {
		logger.App().Error(errors.Wrap(err, "creating window failed"))
	}

	go func() {
		// 按键绑定
		hotkeys := win32control.New()
		fmt.Println("按键绑定: CTRL + ALT + X")
		hotkeys.BindHotkey(win32control.ALT|win32control.CONTROL, 'X', func(id int) {
			logger.App().Info("触发按键：CTRL + ALT + X")
			helpers.SwitchTaskbar(false)
			exec.Command("taskkill", "/f", "/im", "monitor.exe").Run()
			exec.Command("taskkill", "/f", "/im", "dora.exe").Run()
		})

		fmt.Println("按键绑定: CTRL + ALT + O")
		hotkeys.BindHotkey(win32control.ALT|win32control.CONTROL, 'O', func(id int) {
			logger.App().Info("触发按键：CTRL + ALT + O")
			helpers.OpenTaskbar()
		})

		fmt.Println("按键绑定: CTRL + ALT + K")
		hotkeys.BindHotkey(win32control.ALT|win32control.CONTROL, 'K', func(id int) {
			logger.App().Info("触发按键：CTRL + ALT + K")
			helpers.KillTaskbar()
		})

		fmt.Println("按键绑定: CTRL + ALT + C")
		hotkeys.BindHotkey(win32control.ALT|win32control.CONTROL, 'C', func(id int) {
			logger.App().Info("触发案件：CTRL + ALT + C")
			w.OpenDevTools()
		})
		hotkeys.Start()
		select {}
	}()

	if devs.Terminal.Mode == "dev" {
		// 打开控制台[调试使用]
		w.OpenDevTools()
		// 最大化
		//w.Maximize()
	} else {
		// 结束桌面
		helpers.EndTaskbar()
	}

	devs.Printer.AcroRd32Path = bridge.ReadAcroRd32Path()
	// 文件临时目录
	devs.DiskFileSupport.TempPath = devs.File.Path
	// 创建临时目录
	_, err = helpers.GetPath(devs.File.Path)
	if err != nil {
		logger.App().Fatalf("临时目录创建失败：%v", err)
		os.Exit(1)
	}

	// 创建临时目录可访问url -> http://127.0.0.1:4002
	//fileServer := http.FileServer(http.Dir(devs.File.Path))
	//go http.ListenAndServe(":4002", fileServer)
	go func() {
		http.Handle("/", new(bridge.HttpHandler))
		http.ListenAndServe(":4002", nil)
	}()


	// 检查打印机
	devs.Printer.Exists(func(s string) {
		w.Send(s)
	})
	// 打开条码设备
	//devs.BarCode.Open(func(s string) {
	//	w.Send(s)
	//})

	go devs.File.RemoveFiles()
	go devs.Printer.GetStatus(w, false)
	go devs.UsbDetector.AutoDetector(func(s string) {
		w.Send(s)
	})
	//15分钟没收到前端消息则判定为与前端失联
	timer := time.NewTimer(time.Minute * 15)
	go func() {
		for {
			select {
			case <-timer.C:
				logger.App().Error("已超过15分钟没有收到前端消息，即将重启")
				a.Close()
				exec.Command("taskkill", "/f", "/im", "dora.exe").Run()
			default:
			}
			time.Sleep(time.Second * 5)
		}
	}()

	w.On(liasicaectron.EventNameWindowEventMessage, func(e liasicaectron.Event) (deleteListener bool) {
		var m string
		e.Message.Unmarshal(&m)
		var rm = fmt.Sprintf("底层收到消息：%s", m)
		logger.App().Infof(rm)
		timer.Reset(time.Minute * 15)
		// 解析
		go bridge.Parse(m, devs, w)
		return
	})

	// Send message
	if err = w.Send("{\"action\":\"NOTIFY\",\"detail\":\"Ready\"}"); err != nil {
		logger.App().Fatalf("初始化失败：%v", err)
	}
	w.Send("{\"action\":\"TIMEOUT\",\"timeout\":" + devs.BarCode.TimeOut + "}")
	// 启动RPC
	go bridge.OpenRPC(devs)
	// 删除历史log（七天前的）
	logPath := "logs"
	files, _ := ioutil.ReadDir(logPath)
	for _, f := range files {
		if float64(time.Now().Unix()-f.ModTime().Unix()) > 604800 {
			os.Remove(logPath + string(os.PathSeparator) + f.Name())
		}
	}
	go func() {
		//go fun the network connection check
		r1 := helpers.NewReacher()
		strurl := devs.Terminal.UpdateURL
		domainAddress := getDomainnameFromServerURL(strurl)
		ra, err := net.ResolveIPAddr("ip4:icmp", domainAddress)
		if err != nil {
			// Send message
			if err = w.Send("{\"action\":\"NETWORKCONNECTION\",\"detail\":\"failed\"}"); err != nil {
				logger.App().Fatalf("发送消息错误：%v", err)
			}
		}
		r1.AddIP(ra.String())
	}()
	// Blocking pattern
	a.Wait()
}

//http://doraterminal.elephdesign.com:8123/  for example from bridge.TerminalConf.UpdateURL
func getDomainnameFromServerURL(rawurl string) string {
	u, err := url.Parse(rawurl)
	if err == nil {
		return u.Host
	}
	return "127.0.0.1"
}
