﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using System.Runtime;
using System.Runtime.InteropServices;

namespace GetPageCount
{
    class Program
    {
        static void Main(string[] args)
        {
            try
            {
                if (args.Length < 1)
                {
                    Console.WriteLine("{\"result\":\"0\",\"errcode\":100001\",\"errmsg\":\"The number of paraments is not correct, 参数个数不对，长度小于1!\"}");
                }
                else
                {
                    string inputFile = @args[0];
                    // int pagecount = USBDISKPRINTLIB.USBDISKPRINTUTIL.GetTotalPages(inputFile);
                    // {\"result”:”0”,"errcode":10036”,”errmsg":"错误信息!"}   //错误代码，根据代码规则查询错误信息。
                    string convertNpagecount = USBDISKPRINTLIB.USBDISKPRINTUTIL.GetTotalPages(inputFile);
                    Console.WriteLine(convertNpagecount);
                }
            }
            catch ( Exception ex)
            {
                // 针对COM类的exception 直接取其ErrorCode
                int code = 100002;
                var w32ex = ex as Win32Exception;
                var wcomex = ex as COMException;
                if (wcomex != null)
                {
                    //根据code来判别错误
                    code = wcomex.ErrorCode;
                }
                if (w32ex != null)
                {
                    //根据code来判别错误
                    code  = w32ex.ErrorCode;
                }
               
                Console.WriteLine("{\"result\":\"0\",\"errcode\":" + code.ToString()  + "\",\"errmsg\":\"" + ex.Message + "\"}");
            }

        }
    }
}

