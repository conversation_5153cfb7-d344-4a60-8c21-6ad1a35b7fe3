﻿<Window x:Class="UDiskPrintDemo.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:UDiskPrintDemo"
        mc:Ignorable="d"
        Title="MainWindow" Height="350" Width="525">
    <Grid>
        <Button x:Name="btnGetPageCount" Content="GetPageCount" HorizontalAlignment="Left" Margin="307,92,0,0" VerticalAlignment="Top" Width="106" Click="btnGetPageCount_Click"/>
        <TextBox x:Name="textBox" HorizontalAlignment="Left" Height="23" Margin="120,45,0,0" TextWrapping="Wrap" Text="1" VerticalAlignment="Top" Width="53"/>
        <Label x:Name="label" Content="From n To m:" HorizontalAlignment="Left" Margin="29,45,0,0" VerticalAlignment="Top"/>
        <Label x:Name="label_Copy" Content="Copies:" HorizontalAlignment="Left" Margin="29,88,0,0" VerticalAlignment="Top"/>
        <TextBox x:Name="tbCopies" HorizontalAlignment="Left" Height="23" Margin="120,88,0,0" TextWrapping="Wrap" Text="1" VerticalAlignment="Top" Width="120"/>
        <Label x:Name="label_Copy1" Content="Duplex:" HorizontalAlignment="Left" Margin="29,125,0,0" VerticalAlignment="Top"/>
        <TextBox x:Name="tbTopage" HorizontalAlignment="Left" Height="23" Margin="187,45,0,0" TextWrapping="Wrap" Text="2" VerticalAlignment="Top" Width="53"/>
        <Button x:Name="btnPrint" Content="Print" HorizontalAlignment="Left" Margin="307,125,0,0" VerticalAlignment="Top" Width="106" Click="btnPrint_Click"/>
        <ComboBox x:Name="cmbDuplex" HorizontalAlignment="Left" Margin="120,125,0,0" VerticalAlignment="Top" Width="120"/>

    </Grid>
</Window>
