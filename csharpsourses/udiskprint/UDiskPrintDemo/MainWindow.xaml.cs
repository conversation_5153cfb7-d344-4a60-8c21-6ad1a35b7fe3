﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.IO;

namespace UDiskPrintDemo
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void btnGetPageCount_Click(object sender, RoutedEventArgs e)
        {
            
            USBDISKPRINTLIB.USBDISKPRINT usbdiskprint = new USBDISKPRINTLIB.USBDISKPRINT(); 
        }

        private void btnPrint_Click(object sender, RoutedEventArgs e)
        {

        }
    }
}
