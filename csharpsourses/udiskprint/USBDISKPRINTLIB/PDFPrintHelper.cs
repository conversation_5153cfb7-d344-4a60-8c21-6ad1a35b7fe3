﻿using iTextSharp.text;
using iTextSharp.text.pdf;
using System;
using System.IO;

namespace PDFPrinter
{
    class PDFPrintHelper
    {
        public static void ExtractPages(string sourcePDFpath, string outputPDFpath, int startPage, int endPage, int copies)
        {
            PdfReader reader = null;
            Document sourceDocument = null;
            PdfCopy pdfCopyProvider = null;
            PdfImportedPage importedPage = null;
            reader = new PdfReader(sourcePDFpath);
            sourceDocument = new Document(reader.GetPageSizeWithRotation(startPage));
            pdfCopyProvider = new PdfCopy(sourceDocument, new FileStream(outputPDFpath, FileMode.Create));
            sourceDocument.Open();

            for (int c = 0; c < copies; c++)
            {
                for (int i = startPage; i <= endPage; i++)
                {
                    importedPage = pdfCopyProvider.GetImportedPage(reader, i);
                    pdfCopyProvider.AddPage(importedPage);
                }
            }

            sourceDocument.Close();
            reader.Close();
        }
        public static int GetPageCount(string sourcePDFpath)
        {
            PdfReader reader = null;
            Document sourceDocument = null;
            PdfCopy pdfCopyProvider = null;
            PdfImportedPage importedPage = null;
            reader = new PdfReader(sourcePDFpath);
            int pagecount = reader.NumberOfPages;
            reader.Close();
            return pagecount;
        }
    }
}
