﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.InteropServices;


namespace USBDISKPRINTLIB
{ 
    public class USBDISKPRINTUTIL  
    { 
        public static string GetTotalPages(string inputFile, string savedTempFileFolder)
        {
            PrintLib printutil = new PrintLib();

            return printutil.GetPageCount(inputFile, savedTempFileFolder);
        }

        public static int PrintFile(string inputFile, string PrinterDriverName, int frompage, int topage, int copies, int duplex)
        {
            PrintLib printutil = new PrintLib();

            return printutil.PrintdocFunc(inputFile, PrinterDriverName, frompage, topage, copies, duplex);
        }

    }
}
