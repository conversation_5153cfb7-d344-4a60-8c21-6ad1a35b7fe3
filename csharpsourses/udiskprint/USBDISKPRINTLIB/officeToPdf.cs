﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using Microsoft.Office;


    public class officeToPdf 
    {
        public void ToPdf(string path, string sPath)
        {
            string tempPath = System.IO.Path.GetTempPath();

            //   if (Directory.Exists(tempPath))
            //   {
            //       Directory.CreateDirectory(tempPath);
            //   }
            object tempFileName = path;
            object savePath = sPath;
            FileInfo fi = new FileInfo(path);
            string astdt = fi.Extension;
            object strFileName = fi.Name;
            object flg = false;
            object oMissing = System.Reflection.Missing.Value;
            switch (astdt.ToLower())
            {
                case ".doc":
                case ".docx":
                    astdt = "pdf";
                    Microsoft.Office.Interop.Word._Application oWord;
                    Microsoft.Office.Interop.Word._Document oDoc;
                    oWord = new Microsoft.Office.Interop.Word.Application();
                    //oWord.Visible = true; 
                    oDoc = oWord.Documents.Open(ref tempFileName,
                    ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing,
                    ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing,
                    ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing);
                    try
                    {
                        // if (!Directory.Exists(savePath.ToString()))
                        // {
                        //     Directory.CreateDirectory(savePath.ToString());
                        // }
                        savePath = sPath;//+ strFileName + "." + astdt;
                        object format = Microsoft.Office.Interop.Word.WdSaveFormat.wdFormatPDF;
                        oDoc.SaveAs(ref savePath, ref format,
                            ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing,
                            ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing,
                            ref oMissing, ref oMissing, ref oMissing, ref oMissing);
                        oDoc.Close(ref flg, ref oMissing, ref oMissing);
                        oWord.Quit(ref oMissing, ref oMissing, ref oMissing);
                    }
                    catch (Exception ex)
                    {
                        oDoc.Close(ref flg, ref oMissing, ref oMissing);
                        oWord.Quit(ref oMissing, ref oMissing, ref oMissing);
                        throw (ex);
                    }
                    break;
                case ".xls":
                case ".xlsx":
                    astdt = "pdf";
                    Microsoft.Office.Interop.Excel._Application oExcel;
                    oExcel = new Microsoft.Office.Interop.Excel.Application();
                    Microsoft.Office.Interop.Excel.Workbook book = oExcel.Workbooks.Open(tempFileName.ToString(),
                    oMissing, true, oMissing, oMissing, oMissing,
                    oMissing, oMissing, oMissing, true, oMissing,
                    oMissing, oMissing, oMissing, oMissing);
                    try
                    {
                        //oExcel.Visible = true; 
                        Microsoft.Office.Interop.Excel.Sheets m_objSheets = (Microsoft.Office.Interop.Excel.Sheets)book.Worksheets;
                        Microsoft.Office.Interop.Excel.Worksheet m_objSheet = (Microsoft.Office.Interop.Excel.Worksheet)(m_objSheets.get_Item(1));

                        Microsoft.Office.Interop.Excel.Range range = m_objSheet.get_Range("A1", oMissing);

                        range = range.get_Resize(1, 1);
                        object objValue = range.get_Value(oMissing);

                        if (objValue == null)
                        {
                            m_objSheet.Cells[1, 1] = " ";
                        }

                        //if (!Directory.Exists(savePath.ToString()))
                        //{
                        //    Directory.CreateDirectory(savePath.ToString());
                        //}
                        savePath = sPath;// +strFileName + "." + astdt;
                        book.ExportAsFixedFormat(Microsoft.Office.Interop.Excel.XlFixedFormatType.xlTypePDF,
                                savePath, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing);
                        book.Close(flg, oMissing, oMissing);
                        oExcel.Quit();
                    }
                    catch (Exception ex)
                    {
                        book.Close(flg, oMissing, oMissing);
                        oExcel.Quit();
                        throw (ex);
                    }
                    break;
                case ".ppt":
                case ".pptx":
                    //astdt = "pdf";
                    //Microsoft.Office.Interop.PowerPoint.Application ppApp = new Microsoft.Office.Interop.PowerPoint.Application();
                    //try
                    //{
                    //    Microsoft.Office.Interop.PowerPoint.Presentation presentation = ppApp.Presentations.Open(tempFileName.ToString(),
                    //                                    Microsoft.Office.Core.MsoTriState.msoTrue, Microsoft.Office.Core.MsoTriState.msoFalse,
                    //                                    Microsoft.Office.Core.MsoTriState.msoFalse);
                    //    if (presentation.Slides.Count < 1)
                    //    {
                    //        presentation.Slides.Add(1, Microsoft.Office.Interop.PowerPoint.PpSlideLayout.ppLayoutBlank);
                    //    }
                    //    if (ppApp.Presentations.Count < 0)
                    //    {
                    //        ppApp.Quit();
                    //        return;
                    //    }
                    //    if (ppApp.Presentations[ppApp.Presentations.Count].Slides.Count < 1)
                    //    {
                    //        return;
                    //    }

                    //    //if (!Directory.Exists(savePath.ToString()))
                    //    //{
                    //    //    Directory.CreateDirectory(savePath.ToString());
                    //    //}
                    //    savePath = sPath;// +strFileName + "." + astdt;
                    //    ppApp.Presentations[ppApp.Presentations.Count].SaveAs(savePath.ToString(), Microsoft.Office.Interop.PowerPoint.PpSaveAsFileType.ppSaveAsPDF, Microsoft.Office.Core.MsoTriState.msoTrue);
                    //    ppApp.Presentations[ppApp.Presentations.Count].Close();
                    //    ppApp.Quit();
                    //}
                    //catch (Exception ex)
                    //{
                    //    ppApp.Presentations[ppApp.Presentations.Count].Close();
                    //    ppApp.Quit();
                    //    throw (ex);
                    //}
                    break;
                default:
                    break;
            }

    }
}