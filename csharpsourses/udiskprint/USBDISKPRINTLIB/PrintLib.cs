﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks; 
using System.Diagnostics;
using System.IO;
using Microsoft.Office;
using Microsoft.Office.Interop;
using UDiskPrintProject;
using DocumentFormat.OpenXml.Packaging;

namespace USBDISKPRINTLIB
{
    class DocFileInfo
    {
        public string filePath = "";
        public int pageCount = 0;
    }
    class PrintLib
    {
        /// nDuplexSetting - One of the following standard settings: 
        /// 1 = None 
        /// 2 = Duplex on long edge (book)
        /// 3 = Duplex on short edge (legal) 
        /// 
        public int PrintdocFunc(string inputFile, string PrinterDriverName, int frompage, int topage, int copies, int duplex)
        {
            int returnvalue = 1;
            object flg = false;
            if (File.Exists(inputFile) == true)
            {
                try
                {
                    // 获取默认打印机
                    StringBuilder dp = new StringBuilder(256);
                    int size = dp.Capacity;
                    string defaultPrinter = "";
                    if (Printer.GetDefaultPrinter(dp, ref size))
                    {
                        defaultPrinter = dp.ToString().Trim();
                    }
                    else
                    {
                        SPC.Common.DLog.Write(String.Format("Failed get default printer. Size: {0}", size), SPC.Common.DLog.LogMessageType.Info);
                        throw new Exception(String.Format("Failed get default printer. Size: {0}", size));
                    }
                    // 设置默认打印机
                    if (defaultPrinter != PrinterDriverName)
                    {
                        Printer.SetDefaultPrinter(PrinterDriverName);
                    }

                    FileInfo fi = new FileInfo(inputFile);
                    string astdt = fi.Extension;
                    object strFileName = fi.Name;
                    object oMissing = System.Reflection.Missing.Value;
                    object oMissing1 = System.Reflection.Missing.Value;
                    // all the files need to move the current folder's temp path
                    string tempfolder = System.AppDomain.CurrentDomain.BaseDirectory + "tempfolder\\";
                    DuplexPrinter.DuplexSettings ds = new DuplexPrinter.DuplexSettings();
                    Guid mguid = Guid.NewGuid();
                    if (astdt.ToLower() == ".rtf")
                    {
                        string destFileName = tempfolder + mguid.ToString() + "_" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".doc";
                        fi.MoveTo(destFileName);
                        FileInfo fidest = fi;
                        astdt = ".doc";
                        fi = fidest;
                    }
                    switch (astdt.ToLower())
                    {
                        case ".doc":
                        case ".docx":
                        case ".docm":
                        case ".dot":
                        case ".dotx":
                        case ".dotm":
                            Microsoft.Office.Interop.Word.Application wordInstance = new Microsoft.Office.Interop.Word.Application();
                            object fileObject = fi.FullName;
                            object pagefrom = oMissing;//int.Parse(frompage);
                            object nRange = Microsoft.Office.Interop.Word.WdPrintOutRange.wdPrintRangeOfPages;
                            object pageto = oMissing;// int.Parse(topage);
                            object pages = frompage + "," + topage;
                            object ManulDuplexPrint = oMissing;// false;              
                            string errormsg = "";
                              ds.SetPrinterDuplex(PrinterDriverName, duplex, out errormsg); 
                            object ncopies = copies;
                            try
                            {
                                Microsoft.Office.Interop.Word.Document doc = wordInstance.Documents.Open(ref fileObject, true, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing);
                                object objNothing = oMissing;
                                var intpages = doc.ComputeStatistics(Microsoft.Office.Interop.Word.WdStatistic.wdStatisticPages, ref objNothing);

                                doc.Activate();
                                doc.PrintOut(oMissing, oMissing,
                                    // Range
                                    nRange, oMissing,
                                    // from n to m
                                    pagefrom, pageto, oMissing,
                                    //copies
                                    ncopies, pages, oMissing, oMissing, oMissing, oMissing,
                                    // ManulDuplexPrint
                                    ManulDuplexPrint, oMissing, oMissing, oMissing, oMissing);
                                doc.Close(ref flg, ref oMissing, ref oMissing);
                                wordInstance.Quit(ref oMissing, ref oMissing, ref oMissing);
                            }
                            catch( Exception ex)
                            {
                                wordInstance.Quit(ref oMissing, ref oMissing, ref oMissing);
                                wordInstance = null;
                                SPC.Common.DLog.Write(ex.ToString(), SPC.Common.DLog.LogMessageType.Error);
                                throw ex;
                            }

                            break;

                        case ".xls":
                        case ".xlsx":
                        case ".xlsm":

                        case ".xlt":
                        case ".xltx":
                        case ".xltm":

                        case ".xla":
                        case ".xlam":
                        case ".xlsb":

                            Microsoft.Office.Interop.Excel.Application excelInstance = new Microsoft.Office.Interop.Excel.Application();
                            //wordFile.FullName = filename;
                            string xlsfileObject = fi.FullName;
                            object xlspagefrom = frompage;
                            object xlsnRange = Microsoft.Office.Interop.Word.WdPrintOutRange.wdPrintRangeOfPages;
                            object xlspageto = topage;
                            object xlspages = frompage + "," + topage;
                            object xlsManulDuplexPrint = oMissing;// false; 
                             ds.SetPrinterDuplex(PrinterDriverName, duplex, out errormsg); 
                            object xlsncopies = copies;
                            Microsoft.Office.Interop.Excel.Workbook xlsworkbook = excelInstance.Workbooks.Open(xlsfileObject, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing);
                            var sheetPages = xlsworkbook.Sheets.Count;
                            xlsworkbook.Activate();
                            xlsworkbook.Sheets.PrintOutEx(xlspagefrom, xlspageto, xlsncopies, oMissing, PrinterDriverName,
                                oMissing, oMissing, oMissing, oMissing);
                            xlsworkbook.Close(flg, oMissing, oMissing);
                            excelInstance.Quit();

                            break;

                        case ".ppt":
                        case ".pptx":
                        case ".pot":
                        case ".pps":
                        case ".ppa":
                        case ".potx":
                        case ".ppsx":
                        case ".ppam":
                        case ".pptm":
                        case ".potm":
                        case ".ppsm":

                            Microsoft.Office.Interop.PowerPoint.Application pptInstance = new Microsoft.Office.Interop.PowerPoint.Application();
                            string pptfileObject = fi.FullName;
                            int pptpagefrom = frompage;
                            object pptnRange = Microsoft.Office.Interop.Word.WdPrintOutRange.wdPrintRangeOfPages;
                            int pptpageto = topage;
                            object pptpages = frompage + "," + topage;
                            object pptManulDuplexPrint = oMissing;// false;
                            ds.SetPrinterDuplex(PrinterDriverName, duplex, out errormsg); 
                            int pptncopies = copies;
                            Microsoft.Office.Interop.PowerPoint.Presentation ppts = pptInstance.Presentations.Open(pptfileObject, Microsoft.Office.Core.MsoTriState.msoFalse, Microsoft.Office.Core.MsoTriState.msoFalse, Microsoft.Office.Core.MsoTriState.msoFalse);
                            var slidesPages = ppts.Slides.Count;

                            ppts.PrintOut(pptpagefrom, pptpageto, "", pptncopies, Microsoft.Office.Core.MsoTriState.msoFalse);
                            ppts.Close();
                            pptInstance.Presentations[pptInstance.Presentations.Count].Close();
                            pptInstance.Quit();

                            break;

                        case ".pdf":
                            int startPage = frompage;
                            int endPage = topage;
                            copies = copies == 0 ? 1 : copies;
                            ds.SetPrinterDuplex(PrinterDriverName, duplex, out errormsg); 
                            string ext = Path.GetExtension(inputFile);
                            string fileName = Path.GetFileNameWithoutExtension(inputFile);
                            SPC.Common.DLog.Write(inputFile, SPC.Common.DLog.LogMessageType.Info);
                            string fileDir = Path.GetDirectoryName(inputFile);
                            string outFile = fileDir + @"\" + fileName + "_" + startPage.ToString() + "_" + endPage.ToString() + "_" + copies.ToString() + ext; 

                            // 处理文件
                            PDFPrinter.PDFPrintHelper.ExtractPages(inputFile, outFile, startPage, endPage, copies);

                            // 打印文件
                            Printer.printerName = PrinterDriverName;
                            Printer.fileName = Path.GetFileName(outFile);
                            Printer.PrintPDF(outFile);
                            break;
                        default:
                            SPC.Common.DLog.Write("The file format is not supported: " + inputFile, SPC.Common.DLog.LogMessageType.Info);
                            returnvalue = -1;
                            break;
                    }
                }
                catch( Exception ex)
                {
                    SPC.Common.DLog.Write(ex.ToString(), SPC.Common.DLog.LogMessageType.Error);
                    throw ex;
                }
            }
            else
            {
                returnvalue = 200001;
                SPC.Common.DLog.Write("File not exits" + returnvalue.ToString() + ": " + inputFile, SPC.Common.DLog.LogMessageType.Info);
            }
            return returnvalue;
        }

        public string  GetPageCount(string inputFile,string savedTempFileFolder)
        {
            DocFileInfo docinfo = new DocFileInfo();
            int returnvalue = 0;
            if (File.Exists(inputFile) == true)
            {
                try
                {
                    FileInfo fi = new FileInfo(inputFile);
                    string astdt = fi.Extension;
                    object strFileName = fi.Name;
                    object oMissing = System.Reflection.Missing.Value;
                    // all the files need to move the current folder's temp path
                    //string tempfolder = System.AppDomain.CurrentDomain.BaseDirectory + "tempfolder\\";
                    string tempfolder = savedTempFileFolder + "\\";
                    DuplexPrinter.DuplexSettings ds = new DuplexPrinter.DuplexSettings();
                  Guid mguid = Guid.NewGuid();

                    object flg = false;

                    string destFileNameWNoExt = tempfolder + mguid.ToString() + "_" + DateTime.Now.ToString("yyyyMMddHHmmssfff");
                    if (astdt.ToLower() == ".rtf")
                    {
                        string  destFileName = destFileNameWNoExt + ".doc";
                        fi.MoveTo(destFileName);
                        FileInfo fidest = fi;
                        astdt = ".doc";
                        fi = fidest;
                    }
                    switch (astdt.ToLower())
                    {
                        case ".doc":
                        case ".dot":
                            // convert documents and return the page count 

                            SPC.Common.DLog.Write("Start to convert word doc via ole com:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                            SPC.Common.DLog.Write("Starting to convert word (doc to docx etc.) via ole com:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                            Microsoft.Office.Interop.Word._Application oWord ;
                            Microsoft.Office.Interop.Word._Document oDoc ;
                            oWord = new Microsoft.Office.Interop.Word.Application();
                            object tempFileName = fi.FullName;
                            string savePathdir = tempfolder;
                            object savePath =   destFileNameWNoExt + ".docx"; // attch the file extension
                                                                              //oWord.Visible = true; 
                            try
                            {
                                oDoc = oWord.Documents.Open(ref tempFileName,
                                ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing,
                                ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing,
                                ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing);
                                try
                                {
                                    if (!Directory.Exists(savePathdir.ToString()))
                                    {
                                        Directory.CreateDirectory(savePathdir.ToString());
                                    }
                                    object format = Microsoft.Office.Interop.Word.WdSaveFormat.wdFormatDocumentDefault;
                                    oDoc.SaveAs(ref savePath, ref format,
                                        ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing,
                                        ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing,
                                        ref oMissing, ref oMissing, ref oMissing, ref oMissing);
                                    oDoc.Close(ref flg, ref oMissing, ref oMissing);
                                    oWord.Quit(ref oMissing, ref oMissing, ref oMissing);
                                }
                                catch (Exception ex)
                                {
                                    oDoc.Close(ref flg, ref oMissing, ref oMissing);
                                    oWord.Quit(ref oMissing, ref oMissing, ref oMissing);
                                    SPC.Common.DLog.Write(ex.ToString(), SPC.Common.DLog.LogMessageType.Error);
                                    throw (ex);
                                }

                                SPC.Common.DLog.Write("completed converting word (doc to docx etc.) via office ole com:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                                SPC.Common.DLog.Write("Getting the page count of word via open xml sdk:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                                using (WordprocessingDocument document = WordprocessingDocument.Open(savePath.ToString(), false))
                                {
                                    int pageCount = int.Parse(document.ExtendedFilePropertiesPart.Properties.Pages.Text);
                                    returnvalue = pageCount;
                                }
                                SPC.Common.DLog.Write("completed the page count  of word via open xml sdk:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                                docinfo.filePath = savePath.ToString();
                                docinfo.pageCount = returnvalue;
                            }
                            catch( Exception ex)
                            {
                                oWord.Quit(ref oMissing, ref oMissing, ref oMissing);
                                oWord = null;
                                SPC.Common.DLog.Write(ex.ToString(), SPC.Common.DLog.LogMessageType.Error);
                                throw ex;
                            }
                            break;

                        case ".docx":
                        case ".dotx":
                        case ".docm":
                        case ".dotm":
                            SPC.Common.DLog.Write("Getting the page count of word via open xml sdk:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                            using (WordprocessingDocument document = WordprocessingDocument.Open(fi.FullName, false))
                            {
                                int pageCount = int.Parse(document.ExtendedFilePropertiesPart.Properties.Pages.Text);
                                returnvalue = pageCount;
                            }
                            SPC.Common.DLog.Write("completed the page count  of word via open xml sdk:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                            docinfo.filePath = fi.FullName.ToString();
                            docinfo.pageCount = returnvalue;
                            break;
                        case ".xls":
                        case ".xlt":
                        case ".xla":
                        case ".xlsb":
                            tempFileName = fi.FullName;
                            string savePathdirxlsx = tempfolder;
                            object savePathxlsx = destFileNameWNoExt + ".xlsx"; // attch the file extension
                            Microsoft.Office.Interop.Excel._Application oExcel; 
                            oExcel = new Microsoft.Office.Interop.Excel.Application();
                            Microsoft.Office.Interop.Excel.Workbook book = oExcel.Workbooks.Open(tempFileName.ToString(),
                            oMissing, true, oMissing, oMissing, oMissing,
                            oMissing, oMissing, oMissing, true, oMissing,
                            oMissing, oMissing, oMissing, oMissing);
                            try
                            {

                                if (!Directory.Exists(savePathdirxlsx.ToString()))
                                {
                                    Directory.CreateDirectory(savePathdirxlsx.ToString());
                                }
                                object format = Microsoft.Office.Interop.Excel.XlFileFormat.xlWorkbookDefault;
                                SPC.Common.DLog.Write("Start to convert excel doc via ole com:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                                SPC.Common.DLog.Write("Starting to convert excel (xls to xlsx etc.) via ole com:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                                book.SaveAs(savePathxlsx, format, oMissing, oMissing, oMissing, oMissing, Microsoft.Office.Interop.Excel.XlSaveAsAccessMode.xlNoChange, oMissing, oMissing, oMissing, oMissing, oMissing);
                                book.Close(flg, oMissing, oMissing);
                                oExcel.Quit();
                            }
                            catch (Exception ex)
                            {
                                SPC.Common.DLog.Write(ex.ToString(), SPC.Common.DLog.LogMessageType.Error);
                                book.Close(flg, oMissing, oMissing);
                                oExcel.Quit();
                                throw (ex);
                            }
                            SPC.Common.DLog.Write("completed converting excel (xls to xlsx etc.) via office ole com:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);

                            SPC.Common.DLog.Write("Getting the page count of excel via open xml sdk:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                            using (SpreadsheetDocument exceldocument = SpreadsheetDocument.Open(savePathxlsx.ToString(), false))
                            {
                                int pageCount = exceldocument.WorkbookPart.Workbook.Sheets.Count();
                                SPC.Common.DLog.Write("pageCount= " + pageCount, SPC.Common.DLog.LogMessageType.Info);
                                returnvalue = pageCount;
                            }
                            SPC.Common.DLog.Write("completed the page count of excel  via open xml sdk:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                            docinfo.filePath = savePathxlsx.ToString();
                            docinfo.pageCount = returnvalue;
                            break;
                        case ".xltx":
                        case ".xlsm":
                        case ".xltm":
                        case ".xlam":
                        case ".xlsx":
                            SPC.Common.DLog.Write("Getting the page count of excel via open xml sdk:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                            using (SpreadsheetDocument exceldocument = SpreadsheetDocument.Open(fi.FullName, false))
                            {
                                int pageCount = exceldocument.WorkbookPart.Workbook.Sheets.Count();
                                SPC.Common.DLog.Write("pageCount= " + pageCount, SPC.Common.DLog.LogMessageType.Info);

                                returnvalue = pageCount;
                            }
                            SPC.Common.DLog.Write("completed the page count of excel  via open xml sdk:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);

                            docinfo.filePath = fi.FullName;
                            docinfo.pageCount = returnvalue;

                            break;

                        case ".ppt":
                        case ".pot":
                        case ".pps":
                        case ".ppa":

                            string savePathdirpptx = tempfolder;
                            object savePathpptx = destFileNameWNoExt + ".pptx"; // attch the file extension
                            tempFileName = fi.FullName;

                            Microsoft.Office.Interop.PowerPoint.Application ppApp = new Microsoft.Office.Interop.PowerPoint.Application();
                            try
                            {
                                Microsoft.Office.Interop.PowerPoint.Presentation presentation = ppApp.Presentations.Open(tempFileName.ToString(),
                                                                Microsoft.Office.Core.MsoTriState.msoTrue, Microsoft.Office.Core.MsoTriState.msoFalse,
                                                                Microsoft.Office.Core.MsoTriState.msoFalse);
                                if (presentation.Slides.Count < 1)
                                {
                                    presentation.Slides.Add(1, Microsoft.Office.Interop.PowerPoint.PpSlideLayout.ppLayoutBlank);
                                }
                                if (ppApp.Presentations.Count < 0)
                                {
                                    ppApp.Quit(); 
                                }
                                if (ppApp.Presentations[ppApp.Presentations.Count].Slides.Count < 1)
                                { 
                                }

                                if (!Directory.Exists(savePathdirpptx.ToString()))
                                {
                                    Directory.CreateDirectory(savePathdirpptx.ToString());
                                }
                                savePath = savePathpptx;// +strFileName + "." + astdt;

                                SPC.Common.DLog.Write("Start to convert pptx (ppt to pptx etc.) via ole com:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                                SPC.Common.DLog.Write("Starting to convert pptx (ppt to pptx etc.) via ole com:", SPC.Common.DLog.LogMessageType.Info);

                                ppApp.Presentations[ppApp.Presentations.Count].SaveAs(savePath.ToString(), Microsoft.Office.Interop.PowerPoint.PpSaveAsFileType.ppSaveAsOpenXMLPresentation, Microsoft.Office.Core.MsoTriState.msoTrue);
                                ppApp.Presentations[ppApp.Presentations.Count].Close();
                                ppApp.Quit();
                            }
                            catch (Exception ex)
                            {
                                SPC.Common.DLog.Write(ex.ToString(), SPC.Common.DLog.LogMessageType.Error);
                                ppApp.Presentations[ppApp.Presentations.Count].Close();
                                ppApp.Quit();
                                throw (ex);
                            }
                            SPC.Common.DLog.Write("completed converting  pptx (ppt to pptx etc.)  via office ole com:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info); 
                            SPC.Common.DLog.Write("Getting the page count of ppt via open xml sdk:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                            using (PresentationDocument pptdoc =
         PresentationDocument.Open(savePathpptx.ToString(), false))
                            {
                                // Get the presentation part of the document.
                                PresentationPart presentationPart = pptdoc.PresentationPart;
                                int slidesCount = presentationPart.SlideParts.Count();
                                SPC.Common.DLog.Write("slidesCount =" + slidesCount, SPC.Common.DLog.LogMessageType.Info);
                                returnvalue = slidesCount;
                            }
                            SPC.Common.DLog.Write("completed the page count of ppt  via open xml sdk:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                            docinfo.filePath = savePathpptx.ToString();
                            docinfo.pageCount = returnvalue;

                            break;

                        case ".pptx":
                        case ".potx":
                        case ".ppsx":
                        case ".ppam":
                        case ".pptm":
                        case ".potm":
                        case ".ppsm":
                            SPC.Common.DLog.Write("Getting the page count of ppt via open xml sdk:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);
                            using (PresentationDocument pptdoc =
         PresentationDocument.Open(fi.FullName, false))
                            {
                                // Get the presentation part of the document.
                                PresentationPart presentationPart = pptdoc.PresentationPart;
                                int slidesCount = presentationPart.SlideParts.Count();
                                SPC.Common.DLog.Write("slidesCount = " + slidesCount.ToString(), SPC.Common.DLog.LogMessageType.Info);
                                returnvalue = slidesCount;
                            }
                            SPC.Common.DLog.Write("completed the page count of ppt  via open xml sdk:" + DateTime.Now.ToString(), SPC.Common.DLog.LogMessageType.Info);

                            docinfo.filePath = fi.FullName;
                            docinfo.pageCount = returnvalue;

                            break;

                        case ".pdf":
                         
                            string ext = Path.GetExtension(inputFile);
                            string fileName = Path.GetFileNameWithoutExtension(inputFile);
                            SPC.Common.DLog.Write(inputFile, SPC.Common.DLog.LogMessageType.Info);
                            string fileDir = Path.GetDirectoryName(inputFile);
                            // 处理文件
                            returnvalue = PDFPrinter.PDFPrintHelper.GetPageCount(inputFile);

                            docinfo.filePath = inputFile;
                            docinfo.pageCount = returnvalue;

                            break;

                        default:
                            SPC.Common.DLog.Write("The file format is not supported: " + inputFile, SPC.Common.DLog.LogMessageType.Info);
                            returnvalue = -1;
                            break;
                    }
                }
                catch (Exception ex)
                {
                    SPC.Common.DLog.Write(ex.ToString(), SPC.Common.DLog.LogMessageType.Error);
                    throw ex;                     
                }
                finally
                {
                    GC.Collect();
                }
            }
            else
            {
                returnvalue = 200001;
                SPC.Common.DLog.Write("File not exits: " + inputFile, SPC.Common.DLog.LogMessageType.Info);
            }
            SPC.Common.DLog.Write(JsonHelper.Convert2Json(docinfo), SPC.Common.DLog.LogMessageType.Info);
            return JsonHelper.Convert2Json( docinfo);
        }
    }
}


 