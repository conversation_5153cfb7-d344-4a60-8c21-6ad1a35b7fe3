﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Drawing.Printing;
using System.IO;

namespace USBDiskPrintDemo
{
    public partial class FormUDiskPrint : Form
    {
        public FormUDiskPrint()
        {
            InitializeComponent();
        }
        //public   USBDISKPRINTLIB.USBDISKPRINT uprint = new USBDISKPRINTLIB.USBDISKPRINT();
        string sDefault = "";
        private void btnGetPageCount_Click(object sender, EventArgs e)
        {

            OpenFileDialog ofd = new OpenFileDialog();
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                SPC.Common.DLog.Write("Strting to get the total pages...", SPC.Common.DLog.LogMessageType.Info);
                Console.WriteLine("Strting to get the total pages...: " + DateTime.Now.ToString("yyyyMMddHHmmssfff"));
                tbPageCount.Text = USBDISKPRINTLIB.USBDISKPRINTUTIL.GetTotalPages(ofd.FileName,this.tbTempFolder.Text.Trim()).ToString();
                SPC.Common.DLog.Write("Ending to get the total pages...", SPC.Common.DLog.LogMessageType.Info);
                Console.WriteLine("Ending to get the total pages...: " + DateTime.Now.ToString("yyyyMMddHHmmssfff"));
            }
        }
        private void btmprintdocs_Click(object sender, EventArgs e)
        {
            int nOrientation = 1;
            if(rbPortrait.Checked  ==true)
            {
                nOrientation = 1;
            }
            else
            {
                nOrientation = 2;
            }
            OpenFileDialog ofd = new OpenFileDialog();
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                USBDISKPRINTLIB.USBDISKPRINTUTIL.PrintFile(ofd.FileName, sDefault, int.Parse(tbFrom.Text), int.Parse(tbTo.Text), int.Parse(tbcopies.Text), cmbDuplex.SelectedIndex + 1);
            }
        }

        private void FormUDiskPrint_Load(object sender, EventArgs e)
        {
            PrintDocument print = new PrintDocument();
            sDefault = print.PrinterSettings.PrinterName;//默认打印机名
            this.lblPrinter.Text = sDefault;
        } 
    }
}
