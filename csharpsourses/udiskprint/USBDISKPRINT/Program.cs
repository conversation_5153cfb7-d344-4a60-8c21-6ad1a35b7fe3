﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks; 
using System.ComponentModel;
using System.Runtime.InteropServices;

namespace USBDISKPRINT
{
    class Program
    {
        static void Main(string[] args)
        {
            try
            {
               
                    string funcCallIndex = @args[0];
                if (funcCallIndex == "1")
                {
                    if (args.Length < 3)
                    {
                        Console.WriteLine("{\"result\":\"0\",\"errcode\":100001\",\"errmsg\":\"The number of paraments is not correct to get page count, 总页数接口，参数个数不对，长度小于3!\"}");
                    }
                    else
                    {
                        string inputFile = @args[1];
                        string savedTempFileFolder = @args[2];
                        // int pagecount = USBDISKPRINTLIB.USBDISKPRINTUTIL.GetTotalPages(inputFile);
                        // {\"result”:”0”,"errcode":10036”,”errmsg":"错误信息!"}   //错误代码，根据代码规则查询错误信息。
                        string convertNpagecount = USBDISKPRINTLIB.USBDISKPRINTUTIL.GetTotalPages(inputFile, savedTempFileFolder);
                        Console.WriteLine(convertNpagecount);
                    }
                }
                else
                {
                    if (args.Length < 7)
                    {
                        Console.WriteLine("{\"result\":\"0\",\"errcode\":100002\",\"errmsg\":\"The number of paraments is not correct for print, 调打印库接口，参数个数不对，长度小于7!\"}");
                    }
                    else
                    {
                        string inputFile = @args[1];
                        string PrinterDriverName = @args[2];
                        int frompage = int.Parse(@args[3]);
                        int topage = int.Parse(@args[4]);
                        int copies = int.Parse(@args[5]);
                        int duplex = int.Parse(@args[6]); 
                        int returnvalue = USBDISKPRINTLIB.USBDISKPRINTUTIL.PrintFile(inputFile, PrinterDriverName, frompage, topage, copies, duplex);
                        Console.WriteLine("{\"result\":\"" + returnvalue + "\",\"errcode\":0\",\"errmsg\":\"Printed Successfully!\"}");
                    }
                }
            }
            catch ( Exception ex)
            {
                // 针对COM类的exception 直接取其ErrorCode
                int code = 100002;
                var w32ex = ex as Win32Exception;
                var wcomex = ex as COMException;
                if (wcomex != null)
                {
                    //根据code来判别错误
                    code = wcomex.ErrorCode;
                }
                if (w32ex != null)
                {
                    //根据code来判别错误
                    code = w32ex.ErrorCode;
                }
                Console.WriteLine("{\"result\":\"0\",\"errcode\":" + code.ToString() + "\",\"errmsg\":\"" + ex.Message + "\"}");
            }
        }
    }
}
