﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <!--日志配置部分-->
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net"/>
  </configSections>
  <!--站点日志配置部分-->
  <log4net>
    <root>
      <priority value="ALL"/>
      <appender-ref ref="RollingFileAppender"/>
    </root>
    <appender name="TraceAppender" type="log4net.Appender.TraceAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger [%property{NDC}] - %message%newline"/>
      </layout>
    </appender>
    <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger [%property{NDC}] - %message%newline"/>
      </layout>
    </appender>
    <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="logs\\"/>
      <appendToFile value="true"/>
      <maxSizeRollBackups value="10"/>
      <maximumFileSize value="2000KB"/>
      <rollingStyle value="Date"/>
      <staticLogFileName value="false"/>
      <DatePattern value="yyyyMMdd&quot;.log&quot;" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger [%property{NDC}] - %message%newline"/>
      </layout>
    </appender>
    <appender name="InfoAppender" type="log4net.Appender.RollingFileAppender+MinimalLock">
      <param name="LockingModel" type="log4net.Appender.FileAppender+MinimalLock" />
      <param name="File" value="logs\\" />
      <param name="AppendToFile" value="true" />
      <param name="MaxSizeRollBackups" value="10" />
      <param name="StaticLogFileName" value="false" />
      <param name="DatePattern" value="yyyyMMdd&quot;.log&quot;" />
      <param name="RollingStyle" value="Date" />
      <layout type="log4net.Layout.PatternLayout">
        <!--param name="ConversionPattern" value="&lt;HR COLOR=red&gt;%n日志时间：%d [%t] &lt;BR&gt;%n日志级别：%-5p &lt;BR&gt;%n日 志 类：%c [%x] &lt;BR&gt;%n%m &lt;BR&gt;%n &lt;HR Size=1&gt;" /-->
        <param name="ConversionPattern"  value="%nLogtime: %d [%t] %n Log Level: %-5p %n Log Class: %c [%x] %n%m %n"></param>
      </layout>
    </appender>
  </log4net>
</configuration>
