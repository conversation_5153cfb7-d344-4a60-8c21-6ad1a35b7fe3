using System;
using System.Data;
using System.Configuration;
using System.Web;

namespace SPC.PrintJob
{ 
    /// <summary>
    /// Summary description for JobStatus
    /// </summary>
    public enum JobStatus
    {
      // Job Process States
    JPS_READY			= 0,
	JPS_PRINTED			= 1,
	JPS_PRINTED_DELETED	= 2,
    JPS_SYSTEM_DELETE	= 3,
	JPS_SYSTEM_DELETED	= 4,
	JPS_USER_DELETE		= 5,
	JPS_USER_DELETED	= 6,
	JPS_SPOOLING		= 7,
	JPS_USER_LOCK		= 8
    } 
}