
    using System;
    using System.Collections.Generic;
    using System.Text;
    using System.Web.Mail; 
    namespace SPC.Common
    {
        public class SendMail
        {
            public void sendTxtMail(string from, string pass, string to, string subject, System.Web.Mail.MailPriority priority, string body, string smtpServer, System.Collections.ArrayList files)
            {
                MailMessage msg = new MailMessage();
                msg.From = from;
                msg.To = to;
                msg.Subject = subject;
                msg.Priority = priority;
                msg.BodyFormat = MailFormat.Text;
                msg.Body = body;
                msg.Fields.Add("http://schemas.microsoft.com/cdo/configuration/smtpauthenticate", "1"); //basic authentication
                msg.Fields.Add("http://schemas.microsoft.com/cdo/configuration/sendusername", from.Substring(0, from.IndexOf("@"))); //set your username here
                msg.Fields.Add("http://schemas.microsoft.com/cdo/configuration/sendpassword", pass); //set your password here
                for (int i = 0; i < files.Count; i++)
                {
                    if (System.IO.File.Exists(files[i].ToString()))
                    {
                        msg.Attachments.Add(new MailAttachment(files[i].ToString()));
                    }
                }
                SmtpMail.SmtpServer = smtpServer; 
                SmtpMail.Send(msg); 
            }
            public void sendHtmlMail(string from, string pass, string to, string subject, System.Web.Mail.MailPriority priority, string body, string smtpServer, System.Collections.ArrayList files)
            {
                MailMessage msg = new MailMessage();
                msg.From = from;
                msg.To = to;
                msg.Subject = subject;
                msg.Priority = priority;
                msg.BodyFormat = MailFormat.Html;
                msg.Body = body;
                msg.Fields.Add("http://schemas.microsoft.com/cdo/configuration/smtpauthenticate", "1"); //basic authentication
                msg.Fields.Add("http://schemas.microsoft.com/cdo/configuration/sendusername", from.Substring(0, from.IndexOf("@"))); //set your username here
                msg.Fields.Add("http://schemas.microsoft.com/cdo/configuration/sendpassword", pass); //set your password here
                for (int i = 0; i < files.Count; i++)
                {
                    if (System.IO.File.Exists(files[i].ToString()))
                    {
                        msg.Attachments.Add(new MailAttachment(files[i].ToString()));
                    }
                }
                SmtpMail.SmtpServer = smtpServer;
                SmtpMail.Send(msg);
            }

            public static void SendSMTPEMail(string strSmtpServer, string strFrom, string strFromPass, string strto, string strSubject, string strBody)
            {
                System.Net.Mail.SmtpClient client = new System.Net.Mail.SmtpClient(strSmtpServer);
                client.UseDefaultCredentials = false;
                client.Credentials =
                new System.Net.NetworkCredential(strFrom, strFromPass);
                client.DeliveryMethod = System.Net.Mail.SmtpDeliveryMethod.Network;

                System.Net.Mail.MailMessage message =
                new System.Net.Mail.MailMessage(strFrom, strto, strSubject, strBody);
                message.BodyEncoding = System.Text.Encoding.UTF8;
                message.IsBodyHtml = true;
                client.Send(message);
            }
            public static void SendMail_Txt(string from, string pass, string to, string subject, System.Web.Mail.MailPriority priority, string body, string smtpServer, System.Collections.ArrayList files)
            {
                System.Web.Mail.MailMessage msg = new MailMessage();
                msg.From = from;
                msg.To = to;
                msg.Subject = subject;
                msg.Priority = priority;
                msg.BodyFormat = MailFormat.Html;
                msg.Body = body;
                msg.Fields.Add("http://schemas.microsoft.com/cdo/configuration/smtpauthenticate", "1"); //basic authentication
                msg.Fields.Add("http://schemas.microsoft.com/cdo/configuration/sendusername", from); //set your username here
                msg.Fields.Add("http://schemas.microsoft.com/cdo/configuration/sendpassword", pass); //set your password here
                for (int i = 0; i < files.Count; i++)
                {
                    if (System.IO.File.Exists(files[i].ToString()))
                    {
                        msg.Attachments.Add(new MailAttachment(files[i].ToString()));
                    }
                }
                SmtpMail.SmtpServer = smtpServer;
                SmtpMail.Send(msg);
            }
        }
    }
