﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using System.Windows.Forms;

/* read or write XML via XMLpath */

namespace SPC.Common
{
    public class AppAppConfigReadWriteEx
    {
        public static string AppConfig()
        { 
            return System.IO.Path.Combine(Application.StartupPath, "xxx.exe.config");
        }
        public static string AppConfig(string strExXmlPath)
        {
            return strExXmlPath;
        }

       

        public static string GetValue (string appKey)
        {
            XmlDocument xDoc = new XmlDocument();
            try
            {
                xDoc.Load(AppAppConfigReadWriteEx.AppConfig());
                XmlNode xNode = xDoc.SelectSingleNode("//appSettings");
                XmlElement xElem = (XmlElement)xNode.SelectSingleNode("//add[@key='" + appKey + "']");
                if (xElem != null)
                    return xElem.GetAttribute("value");
                else
                    return "";
            }
            catch
            {
                return "";
            }
        }
        public static string GetValueEx(string appKey, string strAppconfigPath)
        {
            XmlDocument xDoc = new XmlDocument();
            try
            {
                xDoc.Load(strAppconfigPath);
                XmlNode xNode = xDoc.SelectSingleNode("//appSettings");
                XmlElement xElem = (XmlElement)xNode.SelectSingleNode("//add[@key='" + appKey + "']");
                if (xElem != null)
                    return xElem.GetAttribute("value");
                else
                    return "";
            }
            catch
            {
                return "";
            }
        }
        public static string GetValueEx1(string appKey ,string strnodeName,string strAppconfigPath)
        {
            XmlDocument xDoc = new XmlDocument();
            try
            {
                xDoc.Load(AppAppConfigReadWriteEx.AppConfig(strAppconfigPath));
                XmlNode xNode = xDoc.SelectSingleNode("//" + strnodeName);
                XmlElement xElem = (XmlElement)xNode;
                if (xElem != null)
                    return xElem.InnerText;
                else
                    return "";
            }
            catch
            {
                return "";
            }
        }

        public static void SetValue(string AppKey, string AppValue)
        {
            XmlDocument xDoc = new XmlDocument();
            xDoc.Load(AppAppConfigReadWriteEx.AppConfig());
            XmlNode xNode = xDoc.SelectSingleNode("//appSettings");
            XmlElement xElem1 = (XmlElement)xNode.SelectSingleNode("//add[@key='" + AppKey + "']");

            if (xElem1 != null)
            {
                xElem1.SetAttribute("value", AppValue);
            }
            else
            {
                XmlElement xElem2 = xDoc.CreateElement("add");
                xElem2.SetAttribute("key", AppKey);
                xElem2.SetAttribute("value", AppValue);
                xNode.AppendChild(xElem2);
            }
            xDoc.Save(AppAppConfigReadWriteEx.AppConfig());
        }

        public static void SetValueEx(string AppKey, string AppValue, string strAppconfigPath)
        {
            XmlDocument xDoc = new XmlDocument();
            xDoc.Load( strAppconfigPath );
            XmlNode xNode = xDoc.SelectSingleNode("//appSettings");
            XmlElement xElem1 = (XmlElement)xNode.SelectSingleNode("//add[@key='" + AppKey + "']");

            if (xElem1 != null)
            {
                xElem1.SetAttribute("value", AppValue);
            }
            else
            {
                XmlElement xElem2 = xDoc.CreateElement("add");
                xElem2.SetAttribute("key", AppKey);
                xElem2.SetAttribute("value", AppValue);
                xNode.AppendChild(xElem2);
            }
            xDoc.Save(strAppconfigPath);
        }

        public static void SetValueEx1(string AppKey,string strnodeName, string AppValue, string strAppconfigPath)
        {
            XmlDocument xDoc = new XmlDocument();
            xDoc.Load(strAppconfigPath);
            XmlNode xNode = xDoc.SelectSingleNode("//" + strnodeName);
            XmlElement xElem = (XmlElement)xNode;
            if (xElem != null)
                xElem.GetElementsByTagName("value").Item(0).InnerText = AppValue;
            xDoc.Save(strAppconfigPath);
        }
    } 
}
