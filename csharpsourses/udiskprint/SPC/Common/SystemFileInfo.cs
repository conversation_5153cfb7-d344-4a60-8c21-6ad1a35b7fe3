﻿using System;
using System.Collections.Generic; 
using System.Text;

using System.IO;

namespace SPC.Common
{
    /// <summary>
    /// 处理有关文件目录遍历的相关方法
    /// </summary>
    public class SystemFileInfoUtils
    {
        //用于存放指定目录下的所有目录集合
        private IList<DirectoryInfo> directoryList = new List<DirectoryInfo>();
        public IList<DirectoryInfo> DirectoryList
        {
            get { return directoryList; }
            set { directoryList = value; }
        }

        //用于存放指定目录下的所有文件集合
        private IList<FileInfo> fileInfoList = new List<FileInfo>();
        public IList<FileInfo> FileInfoList
        {
            get { return fileInfoList; }
            set { fileInfoList = value; }
        }


        #region 递归遍历指定目录下的所有目录
        /// <summary>
        /// 递归遍历指定目录下的所有目录
        /// </summary>
        /// <param name="di">指定目录下的目录集合</param>
        public void GetAllDirectory(DirectoryInfo[] di)
        {
            foreach (DirectoryInfo var in di)
            {
                DirectoryList.Add(var);                                  //添加进集合
                GetAllDirectory(var.GetDirectories());             //递归调用
            }
        }
        #endregion

        #region 根据指定目录获得目录下的文件集合
        /// <summary>
        /// 根据指定目录获得目录下的文件集合
        /// </summary>
        /// <param name="strPath">指定目录</param>
        /// <returns>该目录下的所有文件集合</returns>
        public void GetAllFileInfo(string strPath)
        {
            DirectoryInfo directoryInfo = new DirectoryInfo(strPath);        //获得指定文件夹
            fileInfoList = directoryInfo.GetFiles();                                   //获得该文件夹下的所有文件集合
        }
        #endregion

        #region 根据指定目录获得目录下的文件集合
        /// <summary>
        /// 根据指定目录获得目录下的文件集合
        /// </summary>
        /// <param name="strPath">指定目录</param>
        /// <returns>该目录以及所有子目录下的所有文件集合</returns>
        public void GetAllSystemFilesInfo(string strPath)
        {
            //DirectoryInfo directoryInfo = new DirectoryInfo(strPath);        //获得指定文件夹

            GetAllDirectory(new DirectoryInfo(strPath).GetDirectories());

            foreach (var di in DirectoryList)
            {
                DirectoryInfo directoryInfo = di;
                //获得该文件夹下的所有文件集合
                foreach (FileInfo fi in directoryInfo.GetFiles())
                {
                    fileInfoList.Add(fi);
                }
            }
        }
        #endregion

    }
}
