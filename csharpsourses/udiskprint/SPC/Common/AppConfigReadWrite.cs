﻿using System;
using System.Collections.Generic; 
using System.Text;

using System.Xml;
using System.Configuration;

namespace SPC.Common
{
    public class AppConfigReadWrite
    {
        public static string ReadConfig(string strkey)
        {
            List<string> list = new List<string>();
            ExeConfigurationFileMap file = new ExeConfigurationFileMap();
            file.ExeConfigFilename = System.Windows.Forms.Application.ExecutablePath + ".config";
            Configuration config = System.Configuration.ConfigurationManager.OpenMappedExeConfiguration(file, ConfigurationUserLevel.None);

            var myApp = (AppSettingsSection)config.GetSection("appSettings");
            return myApp.Settings[strkey].Value;

        }
        public static string ReadConfigEx(string strkey, string strFullFileName)
        {
            List<string> list = new List<string>();
            ExeConfigurationFileMap file = new ExeConfigurationFileMap();
            file.ExeConfigFilename = strFullFileName;
            Configuration config = System.Configuration.ConfigurationManager.OpenMappedExeConfiguration(file, ConfigurationUserLevel.None);

            var myApp = (AppSettingsSection)config.GetSection("appSettings");
            return myApp.Settings[strkey].Value;

        }
        public static string ReadConfigEx1(string strkey, string strFullFileName, string strSection)
        {
            List<string> list = new List<string>();
            ExeConfigurationFileMap file = new ExeConfigurationFileMap();
            file.ExeConfigFilename = strFullFileName;
            Configuration config = System.Configuration.ConfigurationManager.OpenMappedExeConfiguration(file, ConfigurationUserLevel.None);

            var myApp = (AppSettingsSection)config.GetSection(strSection);
            return myApp.Settings[strkey].Value;

        }

        public static string WriteConfig(string strkey, string val)
        {
            ExeConfigurationFileMap file = new ExeConfigurationFileMap();
            file.ExeConfigFilename = System.Windows.Forms.Application.ExecutablePath + ".config";
            Configuration config = System.Configuration.ConfigurationManager.OpenMappedExeConfiguration(file, ConfigurationUserLevel.None);

            var myApp = (AppSettingsSection)config.GetSection("appSettings");
            myApp.Settings[strkey].Value = val;
            config.Save();
            return val;
        }
        public static string WriteConfigEx(string strkey, string val,string strFullFileName)
        {
            ExeConfigurationFileMap file = new ExeConfigurationFileMap();
            file.ExeConfigFilename = strFullFileName;
            Configuration config = System.Configuration.ConfigurationManager.OpenMappedExeConfiguration(file, ConfigurationUserLevel.None);

            var myApp = (AppSettingsSection)config.GetSection("appSettings");
            myApp.Settings[strkey].Value = val;
            config.Save();
            return val;
        }
    }
}
