﻿using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.Win32;

namespace SPC.Common
{
    /* 
读注册表：

            RegistryHelper rh = new RegistryHelper();

            string portName = rh.GetRegistryData(Registry.LocalMachine, "SOFTWARE\\TagReceiver\\Params\\SerialPort", "PortName");
写注册表：

            RegistryHelper rh = new RegistryHelper();
            rh.SetRegistryData(Registry.LocalMachine, "SOFTWARE\\TagReceiver\\Params\\SerialPort", "PortName", portName);
     
     */
    public static class RegistryHelper
    {
        /// <summary>
        /// 读取指定名称的注册表的值
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static string GetRegistryData(RegistryKey root, string subkey, string name) 
        { 
            string registData = ""; 
            RegistryKey myKey = root.OpenSubKey(subkey, true);
            if (myKey != null)
            {
                registData = myKey.GetValue(name).ToString();
            }

            return registData; 
        } 

        /// <summary>
        /// 向注册表中写数据
        /// </summary>
        /// <param name="name"></param>
        /// <param name="tovalue"></param> 
        public static void SetRegistryData(RegistryKey root, string subkey, string name, string value) 
        { 
            RegistryKey aimdir = root.CreateSubKey(subkey); 
            aimdir.SetValue(name, value); 
        } 

        /// <summary>
        /// 删除注册表中指定的注册表项
        /// </summary>
        /// <param name="name"></param>
        public static void DeleteRegist(RegistryKey root, string subkey, string name) 
        { 
            string[] subkeyNames;
            RegistryKey myKey = root.OpenSubKey(subkey, true);
            subkeyNames = myKey.GetSubKeyNames();
            foreach (string aimKey in subkeyNames) 
            { 
                if(aimKey == name)
                    myKey.DeleteSubKeyTree(name); 
            } 
        } 

        /// <summary>
        /// 判断指定注册表项是否存在
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static bool IsRegistryExist(RegistryKey root, string subkey, string name) 
        { 
            bool _exit = false; 
            string[] subkeyNames; 
            RegistryKey myKey = root.OpenSubKey(subkey, true);
            subkeyNames = myKey.GetSubKeyNames(); 
            foreach(string keyName in subkeyNames) 
            { 
                if(keyName == name) 
                { 
                    _exit = true; 
                    return _exit; 
                } 
            } 

            return _exit; 
        } 
    }
}