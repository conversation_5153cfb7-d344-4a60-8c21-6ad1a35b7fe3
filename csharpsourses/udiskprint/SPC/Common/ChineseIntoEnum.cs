﻿using System;
using System.Collections.Generic;
using System.Text;

namespace SPC.Common
{
    public class ChineseIntoEnum
    {
        public static int GetSize(string size)
        {
            int result = -1;
            switch (size)
            {
                case "A3": result = 8;
                    break;
                case "A4": result = 9;
                    break;
                default: result = -1;
                    break;
            }
            return result;
        }

        public static int GetMedia(string media)
        {
            int result = 0;
            switch (media)
            {
                case "白纸": result = 1;
                    break;
                case "硫酸纸": result = 2;
                    break;
                default: result = 0;
                    break;
            }
            return result;
        }

        public static int GetColor(string color)
        {
            int result = 0;
            switch (color)
            {
                case "黑白": result = 1;
                    break;
                case "彩色": result = 2;
                    break;
                default: result = 0;
                    break;
            }
            return result;
        }
    }
}
