using System;
using System.IO;
using System.Text;
using System.Configuration;

namespace SPC.Common
{
    public class SaveFileLog
    {
        private static string fileName = "installDB.log";// System.Configuration.ConfigurationSettings.AppSettings["LogFileName"];
        private static readonly string logStatus = "true";//System.Configuration.ConfigurationSettings.AppSettings["LogStatus"];
        private static          string szLogPath = "C:\\";

        public static void ResetLogFile()
        {
            try
            {
                string szLogFile = szLogPath + fileName;
                System.IO.DirectoryInfo di = new DirectoryInfo(szLogPath);
                if (di.Exists != true)
                    di.Create();                
                FileStream fs = new FileStream(@szLogFile, FileMode.Append);
                StreamWriter sw = new StreamWriter(fs);
                sw.WriteLine("");
                sw.WriteLine("- LOG RESTARTED -----------------------------------------------");
                sw.WriteLine(string.Format("[{0,-16}]",
                    DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")));
                sw.WriteLine("---------------------------------------------------------------");
                sw.Close();
                fs.Close();
            }
            catch (Exception e)
            {
                throw new Exception(e.Message);
            }
        }

        public static void SetLogFilePath(string szPath)
        {
            szLogPath = szPath;
        }

        public static void SetFileName(string szfileName)
        {
            fileName = szfileName;
        }
 
        /// <summary>
        /// Log file
        /// </summary>
        /// <param name="fileName">fileName</param>
        /// <param name="ProcessDate">ProcessDate</param>
        /// <param name="resCode">resCode</param>
        /// <param name="Type">Type</param>
        public static void SaveLogToFile(string resultType, string processModel, string resCode)
        {
            if (logStatus == "true")
            {
                try
                {
                    string szLogFile = szLogPath + fileName;
                    System.IO.DirectoryInfo di = new DirectoryInfo(szLogPath);
                    if (di.Exists != true)
                        di.Create();
                    FileStream fs = new FileStream(@szLogFile, FileMode.Append);
                    StreamWriter sw = new StreamWriter(fs);
                    sw.WriteLine(string.Format("[{0,-16}]{1,-2}{2,-10}{3,-8}", 
                        DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), 
                        resultType, 
                        processModel, 
                        resCode));
                    sw.Close();
                    fs.Close();
                }
                catch (Exception e)
                {
                    throw new Exception(e.Message);
                }
            }
        }
    }
}
