﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Reflection;
using System.Windows.Forms;

namespace SPC.Common
{
    public static class SingleInstance
    {
        private static string runFlagFullname = null;

        //初始化程序运行标志，如果设置成功，返回true，已经设置返回false，设置失败将抛出异常， 
        public static bool InitRunFlag()
        {
            if (File.Exists(RunFlag))
            {
                return false;
            }
            using (FileStream fs = new FileStream(RunFlag, FileMode.Create))
            {
            }
            return true;
        }

        //释放初始化程序运行标志，如果释放失败将抛出异常， 
        public static void DisposeRunFlag()
        {
            SPC.Common.DLog.Write("DisposeRunFlag is running", SPC.Common.DLog.LogMessageType.Info);
            if (File.Exists(RunFlag))
            {
                File.Delete(RunFlag);
            }
        }

        //获取或设置程序运行标志，必须符合Windows文件命名规范， 
        public static string RunFlag
        {
            get
            {
                if (runFlagFullname == null)
                {
                    string assemblyFullName = Assembly.GetEntryAssembly().FullName + ".flag";
                    string path = Application.StartupPath;// ;Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
                    runFlagFullname = Path.Combine(path, assemblyFullName);
                }
                SPC.Common.DLog.Write(runFlagFullname, SPC.Common.DLog.LogMessageType.Info);
                return runFlagFullname;
            }
            set
            {
                runFlagFullname = value;
            }
        }
    }
}
