﻿using System;
using System.Collections.Generic;
using System.Text;

namespace SPC.Common
{
    public class NetUtil
    {

        public static int L = 0;
        public enum NetStatus
        {
            unknown                  = 0,
            connecting               = 1,
            destinationUnreachable   = 2,
            connectionTimeout        = 3,
            unsolveHost              = 4,
            destinationInvalid       = 5,
            connectionSuccess        = 6
        };
        /// <summary>
        /// 进行DOS命令拼IP，并返回结果
        /// </summary>
        /// <param name="strip"></param>
        /// <returns></returns>
        public static string cmdIP(string strip)
        {
            System.Diagnostics.Process p = new System.Diagnostics.Process();
            p.StartInfo.FileName = "cmd.exe";
            p.StartInfo.UseShellExecute = false;
            p.StartInfo.RedirectStandardInput = true;
            p.StartInfo.RedirectStandardOutput = true;
            p.StartInfo.RedirectStandardError = true;
            p.StartInfo.CreateNoWindow = true;
            string pingstr;
            p.Start();
            p.StandardInput.WriteLine("ping -n 1 " + strip);
            p.StandardInput.WriteLine("exit");
            string stre = p.StandardOutput.ReadToEnd();
            if (stre.IndexOf("(0%loss)") != -1)
            {
                L = 1;
                pingstr = "连接中...";
            }
            else if (stre.IndexOf("Destination host unreachable.") != -1)
            {
                L = 2;
                pingstr = "连接超时";
                //pingstr = "无法达到目的的主机";
            }
            else if (stre.IndexOf("Request timed out.") != -1)
            {
                L = 3;
                pingstr = "连接超时";
            }
            else if (stre.IndexOf("Unknown host") != -1)
            {
                L = 4;
                pingstr = "连接超时";
                //pingstr = "无法解析主机";
            }
            else if (stre.IndexOf("Destination specified is invalid.") != -1)
            {
                L = 5;
                pingstr = "连接超时";
                //pingstr = "指定的目的地是无效的";
            }
            else
            {
                L = 6;
                pingstr = "连接正常";
            }
            p.Close();
            return pingstr;
        }
    }

}
