//------------------------------------------------------------------------------
/// <copyright from='2007' to='2008' company='CCP Systems'>
///    Copyright(c) CCP Systems                
/// </copyright>                                                                
//------------------------------------------------------------------------------
/*******************************************************************************/
/*      FileName: EnumIntoName.cs     	                                   */
/*      Function: EnumIntoName                                               */
/*        Author: <PERSON>                                                */
/*          Date: 2007-09-04                                                   */
/*******************************************************************************/

using System;
using System.Collections.Generic;
using System.Text;

namespace SPC.Common
{
    public class EnumIntoName
    {
        
        public static string GetSize(int state)
        {
            string result = "";
            switch (state)
            {
                case 8:
                    result = "A3";
                    break;
                case 9:
                    result = "A4";
                    break;
                case 11:
                    result = "A5";
                    break;
                case 12:
                    result = "B4(JIS)";
                    break;
                case 13:
                    result = "B5(JIS)";
                    break;
                case 1:
                    result = "Letter";
                    break;
                case 3:
                    result = "Tabloid";
                    break;
                case 5:
                    result = "Legal";
                    break;
                case 43:
                    result = "Japanese Postcard";
                    break;
                case 93:
                    result = "16 Kai(18.4 x 26 cm)";
                    break;
                case 94:
                    result = "32 Kai(13 x 18.4 cm)";
                    break;
                case 95:
                    result = "Big 32 Kai(14 x 20.3 cm)";
                    break;

                default:
                    result = "";
                    break;
            }
            return result;
        }

        // Color
        public static string GetColor(int state)
        {
            string result = "";
            switch (state)
            {
                case 1:
                    result = "B/W";
                    break;
                case 2:
                    result = "Color";
                    break;
                
                default:
                    result = "";
                    break;
            }
            return result;
        }

        // Get User Type
        public static string GetUserType(int state)
        {
            string result = "";
            switch (state)
            {
                case 0:
                    result = "Normal Tenant";
                    break;
                case 1:
                    result = "VIP Tenant";
                    break;

                default:
                    result = "";
                    break;
            }
            return result;
        }

        public static string GetOperatorType(int type)
        {
            string result = string.Empty;
            switch (type)
            {
                case 0:
                    result = "Operator";
                    break;
                case 1:
                    result = "Supervisor";
                    break;
            }
            return result;
        }

        // Event
        public static string GetEvent(int state)
        {
            string result = "";
            switch (state)
            {
                case 0:
                    result = "Recharge";
                    break;
                case 1:
                    result = "Print";
                    break;
                case 2:
                    result = "Refund";
                    break;
                case 3:
                    result = "Cancel Card";
                    break;

                default:
                    result = "";
                    break;
            }
            return result;
        }
    }
}