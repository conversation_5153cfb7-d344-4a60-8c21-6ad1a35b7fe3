using System;
using System.Collections.Generic;
using System.Text;
using System.Configuration;

namespace SPC.CopyJob
{
public class InitCopySerialPort
{
    // Methods
    public SerialPortInitParameter IniCopyControlBoxCOM()
    {
        SerialPortInitParameter parameter = new SerialPortInitParameter();
        parameter.COMport = ConfigurationManager.AppSettings["CopyServicesCOM"];
        parameter.InitString = ConfigurationManager.AppSettings["CopyServicesCOMInit"];
        parameter.BaudRate = int.Parse(parameter.InitString.Split(new char[] { ',' })[0]);
        string str = parameter.InitString.Split(new char[] { ',' })[1];
        if (str != null)
        {
            if (!(str == "n"))
            {
                if (str == "o")
                {
                    parameter.Parity = 1;
                    goto Label_00F7;
                }
                if (str == "e")
                {
                    parameter.Parity = 2;
                    goto Label_00F7;
                }
                if (str == "m")
                {
                    parameter.Parity = 3;
                    goto Label_00F7;
                }
                if (str == "s")
                {
                    parameter.Parity = 4;
                    goto Label_00F7;
                }
            }
            else
            {
                parameter.Parity = 0;
                goto Label_00F7;
            }
        }
        parameter.Parity = 0;
    Label_00F7:;
        parameter.ByteSize = byte.Parse(parameter.InitString.Split(new char[] { ',' })[2]);
        parameter.StopBits = byte.Parse(parameter.InitString.Split(new char[] { ',' })[3]);
        return parameter;
    }
}
}
