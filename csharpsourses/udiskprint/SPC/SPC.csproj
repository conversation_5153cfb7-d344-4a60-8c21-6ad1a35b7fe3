﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{F089B56F-DBB7-4EC9-92CA-3445F17C0AB9}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SPC</RootNamespace>
    <AssemblyName>SPC</AssemblyName>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <PublishUrl>http://localhost/SPC/</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>spcsignkey.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="log4net, Version=1.2.10.0, Culture=neutral, PublicKeyToken=1b44e1d426115821">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>.\log4net.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\AppConfigReadWrite.cs" />
    <Compile Include="Common\CastUtil.cs" />
    <Compile Include="Common\Chinese2PinYin.cs" />
    <Compile Include="Common\ChineseIntoEnum.cs" />
    <Compile Include="Common\AppAppConfigReadWriteEx.cs" />
    <Compile Include="Common\RegistryHelper.cs" />
    <Compile Include="Common\CreateExcel.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Common\DataCrypto.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Common\EnumIntoChinese.cs" />
    <Compile Include="Common\EnumIntoName.cs" />
    <Compile Include="Common\ExportToExcelMgt.cs" />
    <Compile Include="Common\FileTransfer.cs" />
    <Compile Include="Common\FTPCommand.cs" />
    <Compile Include="Common\JScript.cs" />
    <Compile Include="Common\Log4net.cs" />
    <Compile Include="Common\MessageBox.cs" />
    <Compile Include="Common\NetUtil.cs" />
    <Compile Include="Common\PageValidate.cs" />
    <Compile Include="Common\SaveFileLog.cs" />
    <Compile Include="Common\SingleInstance.cs" />
    <Compile Include="Common\CSVUtil.cs" />
    <Compile Include="Common\MD5.cs" />
    <Compile Include="Common\DESEncrypt.cs" />
    <Compile Include="Common\DLog.cs" />
    <Compile Include="Common\INI.cs" />
    <Compile Include="Common\SendMail.cs" />
    <Compile Include="Common\SerializerUtilty.cs" />
    <Compile Include="Common\SystemFileInfo.cs" />
    <Compile Include="CopyJob\CopyJobServicesControlbox.cs" />
    <Compile Include="CopyJob\CopyJobServicesOpenAPI.cs" />
    <Compile Include="CopyJob\CopyServices.cs" />
    <Compile Include="CopyJob\CopyServicesType.cs" />
    <Compile Include="CopyJob\InitCopySerialPort.cs" />
    <Compile Include="CopyJob\SerialPortInitParameter.cs" />
    <Compile Include="PrintJob\JobStatus.cs" />
    <Compile Include="PrintJob\PrinterSettings.cs" />
    <Compile Include="PrintJob\PrinterStatus.cs" />
    <Compile Include="PrintJob\PrintJobService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="log4net.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="log4netconfig.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="spcsignkey.snk" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>