﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Drawing.Printing;
using System.Diagnostics;
using System.IO;
using Microsoft.Office;
using Microsoft.Office.Interop; 

namespace UDiskPrintProject
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {

            try
            {
                FolderBrowserDialog fbd = new FolderBrowserDialog();
               if( fbd.ShowDialog() == DialogResult.OK)
                {
                    //遍历文件
                    DirectoryInfo di = new DirectoryInfo(fbd.SelectedPath);
                    foreach (FileInfo NextFile in di.GetFiles())
                    {                        
                        officeToPdf op = new officeToPdf();
                        op.ToPdf(NextFile.FullName,"c:\\" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".pdf");
                    }
                }
              
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);

            }

        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            // Allow the user to select a file.
            OpenFileDialog ofd = new OpenFileDialog();
            if (DialogResult.OK == ofd.ShowDialog(this))
            {
                // Allow the user to select a printer.
                PrintDialog pd = new PrintDialog();
                pd.PrinterSettings = new PrinterSettings();
                pd.PrinterSettings.PrinterName = "M132fwN";
                if (DialogResult.OK == pd.ShowDialog(this))
                {
                    // Print the file to the printer.
                    RawPrinterHelper.SendFileToPrinter(pd.PrinterSettings.PrinterName, ofd.FileName);
                }
            }
        }

        private void btmprintdocs_Click(object sender, EventArgs e)
        {
            string filename = "‪D:/testdocs/test1.docx";
            string printer = "M132fwN";
             
            OpenFileDialog ofd = new OpenFileDialog();
            string PrinterName = "BR5590DN";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                FileInfo fi = new FileInfo(ofd.FileName); 
                string astdt = fi.Extension;
                object strFileName = fi.Name;
                object oMissing = System.Reflection.Missing.Value;

                if (astdt.ToLower() == ".rtf")
                {
                    string destFileName = fi.FullName + "_" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".doc";
                    FileInfo fidest =  fi.CopyTo(destFileName, true);
                    astdt = ".doc";
                    fi = fidest;
                }

                switch (astdt.ToLower())
                    { 
                    case ".doc":
                    case ".docx": 
                    case ".dot":
                    case ".dotx":
                    case ".docm":
                    case ".dotm":
                        Microsoft.Office.Interop.Word.Application wordInstance = new Microsoft.Office.Interop.Word.Application();
                       
                        //wordFile.FullName = filename;
                        object fileObject = fi.FullName;

                        object pagefrom = oMissing;//int.Parse(tbFrom.Text.Trim());
                        object nRange = Microsoft.Office.Interop.Word.WdPrintOutRange.wdPrintRangeOfPages;
                        object pageto = oMissing;// int.Parse(tbTo.Text.Trim());
                        object pages = tbFrom.Text.Trim() + "," + tbTo.Text.Trim();
                        object ManulDuplexPrint = oMissing;// false;
                        if (cmbDuplex.Text.ToLower() == "simplex")
                        {
                            //ManulDuplexPrint = false;
                            DuplexPrinter.DuplexSettings ds = new DuplexPrinter.DuplexSettings();
                            string errormsg = "";
                            ds.SetPrinterDuplex(PrinterName, 1, out errormsg);
                        }
                        else
                        {
                            //ManulDuplexPrint = true;
                            DuplexPrinter.DuplexSettings ds = new DuplexPrinter.DuplexSettings();
                            string errormsg = "";
                            ds.SetPrinterDuplex(PrinterName, 2, out errormsg);
                        }
                        object ncopies = int.Parse(tbcopies.Text.Trim());
                        Microsoft.Office.Interop.Word.Document doc = wordInstance.Documents.Open(ref fileObject, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing, ref oMissing);
                        object objNothing = oMissing;
                        var intpages = doc.ComputeStatistics(Microsoft.Office.Interop.Word.WdStatistic.wdStatisticPages, ref objNothing);

                        doc.Activate();
                        doc.PrintOut(oMissing, oMissing,
                            // Range
                            nRange, oMissing,
                            // from n to m
                            pagefrom, pageto, oMissing,
                            //copies
                            ncopies, pages, oMissing, oMissing, oMissing, oMissing,
                            // ManulDuplexPrint
                            ManulDuplexPrint, oMissing, oMissing, oMissing, oMissing);
                        break;

                    case ".xls":
                    case ".xlsx":
                    case ".xlt":
                    case ".xla": 
                    case ".xltx":
                    case ".xlsm":
                    case ".xltm":
                    case ".xlam":
                    case ".xlsb":

                        Microsoft.Office.Interop.Excel.Application excelInstance = new Microsoft.Office.Interop.Excel.Application();                    
                        //wordFile.FullName = filename;
                        string xlsfileObject = fi.FullName; 
                        object xlspagefrom = int.Parse(tbFrom.Text.Trim());
                        object xlsnRange = Microsoft.Office.Interop.Word.WdPrintOutRange.wdPrintRangeOfPages;
                        object xlspageto =  int.Parse(tbTo.Text.Trim());
                        object xlspages = tbFrom.Text.Trim() + "," + tbTo.Text.Trim();
                        object xlsManulDuplexPrint = oMissing;// false;
                        if (cmbDuplex.Text.ToLower() == "simplex")
                        {
                            //ManulDuplexPrint = false;
                            DuplexPrinter.DuplexSettings ds = new DuplexPrinter.DuplexSettings();
                            string errormsg = "";
                            ds.SetPrinterDuplex(PrinterName, 1, out errormsg);
                        }
                        else
                        {
                            //ManulDuplexPrint = true;
                            DuplexPrinter.DuplexSettings ds = new DuplexPrinter.DuplexSettings();
                            string errormsg = "";
                            ds.SetPrinterDuplex(PrinterName, 2, out errormsg);
                        }

                        object xlsncopies = int.Parse(tbcopies.Text.Trim());

                        Microsoft.Office.Interop.Excel.Workbook  xlsworkbook = excelInstance.Workbooks.Open(  xlsfileObject, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing, oMissing);

                        var sheetPages = xlsworkbook.Sheets.Count;

                        xlsworkbook.Activate();
                        xlsworkbook.Sheets.PrintOutEx(xlspagefrom, xlspageto, xlsncopies, oMissing, PrinterName,
                            oMissing, oMissing, oMissing, oMissing);

                        break;

                    case ".ppt":
                    case ".pptx":
                    case ".pot":
                    case ".pps":
                    case ".ppa": 
                    case ".potx":
                    case ".ppsx":
                    case ".ppam":
                    case ".pptm":
                    case ".potm":
                    case ".ppsm":

                        Microsoft.Office.Interop.PowerPoint.Application pptInstance = new Microsoft.Office.Interop.PowerPoint.Application();

                        //wordFile.FullName = filename;
                        string pptfileObject = fi.FullName;

                        int pptpagefrom = int.Parse(tbFrom.Text.Trim());
                        object pptnRange = Microsoft.Office.Interop.Word.WdPrintOutRange.wdPrintRangeOfPages;
                        int pptpageto = int.Parse(tbTo.Text.Trim());
                        object pptpages = tbFrom.Text.Trim() + "," + tbTo.Text.Trim();
                        object pptManulDuplexPrint = oMissing;// false;
                        if (cmbDuplex.Text.ToLower() == "simplex")
                        {
                            //ManulDuplexPrint = false;
                            DuplexPrinter.DuplexSettings ds = new DuplexPrinter.DuplexSettings();
                            string errormsg = "";
                            ds.SetPrinterDuplex(PrinterName, 1, out errormsg);
                        }
                        else
                        {
                            //ManulDuplexPrint = true;
                            DuplexPrinter.DuplexSettings ds = new DuplexPrinter.DuplexSettings();
                            string errormsg = "";
                            ds.SetPrinterDuplex(PrinterName, 2, out errormsg);
                        }

                        int pptncopies = int.Parse(tbcopies.Text.Trim());

                        Microsoft.Office.Interop.PowerPoint.Presentation ppts = pptInstance.Presentations.Open(pptfileObject, Microsoft.Office.Core.MsoTriState.msoFalse , Microsoft.Office.Core.MsoTriState.msoFalse, Microsoft.Office.Core.MsoTriState.msoFalse);
                        //ppts.Activate();

                        var slidesPages = ppts.Slides.Count;
                        
                        ppts.PrintOut(pptpagefrom, pptpageto, "", pptncopies, Microsoft.Office.Core.MsoTriState.msoFalse);

                        break;
                    case ".pdf":

                        string inputFile = fi.FullName; 
                        string printerName = PrinterName;


                        int startPage = int.Parse(tbFrom.Text.Trim());
                        int endPage = int.Parse(tbTo.Text.Trim());
                         


                        int copies = int.Parse(tbcopies.Text.Trim());

                        copies = copies == 0 ? 1 : copies;

                        if (cmbDuplex.Text.ToLower() == "simplex")
                        {
                            //ManulDuplexPrint = false;
                            DuplexPrinter.DuplexSettings ds = new DuplexPrinter.DuplexSettings();
                            string errormsg = "";
                            ds.SetPrinterDuplex(PrinterName, 1, out errormsg);
                        }
                        else
                        {
                            //ManulDuplexPrint = true;
                            DuplexPrinter.DuplexSettings ds = new DuplexPrinter.DuplexSettings();
                            string errormsg = "";
                            ds.SetPrinterDuplex(PrinterName, 2, out errormsg);
                        } 

                        // 获取默认打印机
                        StringBuilder dp = new StringBuilder(256);
                        int size = dp.Capacity;
                        string defaultPrinter = "";
                        if (Printer.GetDefaultPrinter(dp, ref size))
                        {
                            defaultPrinter = dp.ToString().Trim();
                        }
                        else
                        {
                            throw new Exception(String.Format("Failed get default printer. Size: {0}", size));
                        }


                        // 设置默认打印机
                        if (defaultPrinter != printerName)
                        {
                            Printer.SetDefaultPrinter(printerName);
                        }
                        //Console.WriteLine(printerName);
                        string ext = Path.GetExtension(inputFile);
                        string fileName = Path.GetFileNameWithoutExtension(inputFile);
                        Console.WriteLine(inputFile);
                        string fileDir = Path.GetDirectoryName(inputFile);
                        string outFile = fileDir + @"\" + fileName + "_" + startPage.ToString() + "_" + endPage.ToString() + "_" + copies.ToString() + ext;

                        if (ext != ".pdf")
                        {
                            throw new Exception("文件格式错误");
                        }

                        // 处理文件
                        PDFPrinter.PDFPrintHelper.ExtractPages(inputFile, outFile, startPage, endPage, copies);

                        // 打印文件
                        Printer.printerName = printerName;
                        Printer.fileName = Path.GetFileName(outFile);

                        Printer.PrintPDF(outFile);


                        break;
                         
                    default:

                        break;
                }
            }
        }
    }
}
