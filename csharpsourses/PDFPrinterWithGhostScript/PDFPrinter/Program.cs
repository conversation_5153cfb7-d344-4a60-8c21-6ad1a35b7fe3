﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PDFPrinter
{
    class Program
    {
        static void Main(string[] args)
        {
            //string printerName = "Brother HL-5590DN Printer";
            //string inputFile = @"C:\temp_s\test_out.pdf";
            //string copies = "2";
            //bool duplex = true;
            if (args.Length < 3)
            {
                Console.WriteLine("Error of args, at least 3 args like: {pdffile} {printername} {duplex}");
            }
            else
            {
                string inputFile = @args[0];
                string printerName = @args[1];
                //string copies = args[2];
                bool duplex = args[2] == "duplex";
                SendToPrinter send = new SendToPrinter();
                send.inputFile = inputFile;
                send.duplex = duplex;
                //send.copies = copies;
                send.printerName = printerName;
                send.send();
                Console.Write(inputFile + " " + printerName + " " + duplex + "\n");
            }
        }
    }
}
