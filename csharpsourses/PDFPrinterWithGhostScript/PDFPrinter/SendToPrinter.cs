﻿using Ghostscript.NET.Processor;
using System.Collections.Generic;

namespace PDFPrinter
{
    public class SendToPrinter
    {
        public string printerName;
        public string inputFile;
        public string copies = "1";
        public bool duplex;

        /// <summary>
        /// http://stackoverflow.com/questions/23396480/ghostscript-duplex-print-same-side-twice-when-numcopies-is-bigger-than-1
        /// </summary>
        public void send()
        {
            // YOU NEED TO HAVE ADMINISTRATOR RIGHTS TO RUN THIS CODE

            using (GhostscriptProcessor processor = new GhostscriptProcessor())
            {
                List<string> switches = new List<string>();
                switches.Add("-empty");
                switches.Add("-dPrinted");
                switches.Add("-dBATCH");
                switches.Add("-dQUIET");
                switches.Add("-dNOPAUSE");
                switches.Add("-dNOSAFER");
                if (duplex)
                {
                    switches.Add("-dDuplex");
                    switches.Add("-dTumble=true");
                }
                switches.Add("-dNumCopies=" + copies);
                switches.Add("-IgnoreNumCopies=true");
                switches.Add("-sDEVICE=mswinpr2");
                switches.Add("-sOutputFile=%printer%" + printerName);

                //switches.Add("-sDEVICE=ljet4");
                //switches.Add("-sOutputFile=\"\\\\spool\\" + printerName + "\"");

                switches.Add("-sPAPERSIZE=a4");
                switches.Add("-dFIXEDMEDIA");
                switches.Add("-dPDFFitPage");
                //switches.Add("-dNOTRANSPARENCY");
                switches.Add("-dNOINTERPOLATE");
                switches.Add("-dPDFSETTINGS=/screen");
                switches.Add("-dCOLORSCREEN=0");
                switches.Add("-c \"30000000 setvmthreshold\"");

                //switches.Add("-dDownsampleColorImages=true");
                //switches.Add("-dDownsampleGrayImages=true");
                //switches.Add("-dDownsampleMonoImages=true");
                //switches.Add("-dColorImageResolution=72");
                //switches.Add("-dGrayImageResolution=72");
                //switches.Add("-dMonoImageResolution=72");
                //switches.Add("-dCompatibilityLevel=1.4");

                switches.Add("-f");
                switches.Add(inputFile);
                processor.StartProcessing(switches.ToArray(), null);
            }
        }
    }
}
