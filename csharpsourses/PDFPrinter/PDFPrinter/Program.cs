﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace PDFPrinter
{
    class Program
    {
        [DllImport("kernel32.dll")]
        static extern IntPtr GetConsoleWindow();

        [DllImport("user32.dll")]
        static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        /// <summary>
        /// 命令格式为：文件路径,打印机名,开始页,结束页,单双面
        /// 单双面为：1单面，2长边双面，3短边双面
        /// </summary>
        /// <param name="args"></param>
        static void Main(string[] args)
        {
            IntPtr hWnd = GetConsoleWindow();

            if (hWnd != IntPtr.Zero)
            {
                ShowWindow(hWnd, 0);
            }

            //args = new string[] { @"C:\temp_s\test.pdf", @"Brother HL-5590DN Printer", "1", "3", "2", "1" };
            if (args.Length < 6)
            {
                Console.WriteLine("Error of args, at least 6 args like: {pdffile} {printername} {startpage} {endpage} {copies} {duplex}");
            }
            else
            {
                string inputFile = @args[0];
                string printerName = @args[1];

                int startPage = int.Parse(args[2]);
                int endPage = int.Parse(args[3]);
                int copies = int.Parse(args[4]);
                copies = copies == 0 ? 1 : copies;

                int duplex = int.Parse(args[5] == "" ? "1" : args[5]);
                duplex = duplex <= 1 ? 1 : duplex;

                // 获取默认打印机
                StringBuilder dp = new StringBuilder(256);
                int size = dp.Capacity;
                string defaultPrinter = "";
                if (Printer.GetDefaultPrinter(dp, ref size))
                {
                    defaultPrinter = dp.ToString().Trim();
                }
                else
                {
                    throw new Exception(String.Format("Failed get default printer. Size: {0}", size));
                }


                // 设置默认打印机
                if (defaultPrinter != printerName)
                {
                    Printer.SetDefaultPrinter(printerName);
                }
                //Console.WriteLine(printerName);

                // 设置打印机单双面
                string duplexOut;
                short n;
                DuplexSettings dsetting = new DuplexSettings();
                n = dsetting.GetPrinterDuplex(printerName, out duplexOut);

                if (n != duplex)
                {
                    bool duplexSetting = dsetting.SetPrinterDuplex(printerName, duplex, out duplexOut);
                    if (!duplexSetting)
                    {
                        throw new Exception("打印机单双面设置失败");
                    }
                }

                string ext = Path.GetExtension(inputFile);
                string fileName = Path.GetFileNameWithoutExtension(inputFile);
                Console.WriteLine(inputFile);
                string fileDir = Path.GetDirectoryName(inputFile);
                string outFile = fileDir + @"\" + fileName + "_" + startPage.ToString() + "_" + endPage.ToString() + "_" + copies.ToString() + ext;

                if (ext != ".pdf")
                {
                    throw new Exception("文件格式错误");
                }

                // 处理文件
                PDFHandler.ExtractPages(inputFile, outFile, startPage, endPage, copies, duplex);

                // 打印文件
                Printer.printerName = printerName;
                Printer.fileName = Path.GetFileName(outFile);

                Printer.PrintPDF(outFile);
            }
        }
    }
}
