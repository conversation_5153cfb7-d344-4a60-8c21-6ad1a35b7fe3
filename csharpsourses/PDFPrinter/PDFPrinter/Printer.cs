﻿using Microsoft.Win32;
using System;
using System.Diagnostics;
using System.Linq;
using System.Management;
using System.Runtime.InteropServices;
using System.Text;

namespace PDFPrinter
{
    class Printer
    {
        public static string printerName;
        public static string fileName;
        static System.Timers.Timer aTimer = new System.Timers.Timer();
        static bool printDone;

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool SetDefaultPrinter(string Name);

        [DllImport("winspool.drv", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool GetDefaultPrinter(StringBuilder pszBuffer, ref int size);


        public static Boolean PrintPDF(string file)
        {
            try
            {
                Process proc = new Process();
                proc.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
                proc.StartInfo.Verb = "print";

                //Define location of adobe reader/command line
                //switches to launch adobe in "print" mode
                var adobe = Registry.LocalMachine.OpenSubKey("Software").OpenSubKey("Microsoft").OpenSubKey("Windows").OpenSubKey("CurrentVersion").OpenSubKey("App Paths").OpenSubKey("AcroRd32.exe");
                proc.StartInfo.FileName = adobe.GetValue("").ToString();
                proc.StartInfo.Arguments = String.Format(@"/p /h {0}", file);
                proc.StartInfo.UseShellExecute = false;
                proc.StartInfo.CreateNoWindow = true;

                proc.Start();
                proc.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
                
                if (proc.HasExited == false)
                {
                    Stopwatch sw = new Stopwatch();
                    sw.Start();
                    while (!printDone)
                    {
                        PrintJob();
                        if (sw.ElapsedMilliseconds > 300000)
                        {
                            throw new TimeoutException();
                        }
                    }
                }

                proc.EnableRaisingEvents = true;

                proc.Close();
                KillAdobe("AcroRd32");
                return true;
            }
            catch (Exception e)
            {
                throw new Exception(e.Message.ToString());
            }
        }

        public static void PrintJob()
        {
            string searchQuery = "SELECT * FROM Win32_PrintJob";

            ManagementObjectSearcher searchPrintJobs = new ManagementObjectSearcher(searchQuery);
            ManagementObjectCollection prntJobCollection = searchPrintJobs.Get();

            printDone = true;
            int tasks = 0;

            foreach (ManagementObject prntJob in prntJobCollection)
            {
                System.String jobName = prntJob.Properties["Name"].Value.ToString();
                //Job name would be of the format [Printer name], [Job ID]
                char[] splitArr = new char[1];
                splitArr[0] = Convert.ToChar(",");
                string prnterName = jobName.Split(splitArr)[0];
                string docName = prntJob.Properties["Document"].Value.ToString();


                if (String.Compare(prnterName, printerName, true) == 0 && docName != null && docName == fileName)
                {
                    tasks++;

                    int lastPages = int.Parse(prntJob.Properties["TotalPages"].Value.ToString());
                    int printedPages = int.Parse(prntJob.Properties["PagesPrinted"].Value.ToString());

                    if (lastPages != 0 || printedPages <= 0)
                    {
                        printDone = false;
                    }
                }
            }
            
            if (tasks == 0)
            {
                printDone = false;
            }
        }

        //For whatever reason, sometimes adobe likes to be a stage 5 clinger.
        //So here we kill it with fire.
        private static bool KillAdobe(string name)
        {
            foreach (Process clsProcess in Process.GetProcesses().Where(
                         clsProcess => clsProcess.ProcessName.StartsWith(name)))
            {
                clsProcess.Kill();
                return true;
            }
            return false;
        }
    }
}
