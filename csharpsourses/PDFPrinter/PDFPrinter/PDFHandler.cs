﻿using iTextSharp.text;
using iTextSharp.text.pdf;
using System;
using System.IO;

namespace PDFPrinter
{
    class PDFHandler
    {
        public static void ExtractPages(string sourcePDFpath, string outputPDFpath, int startPage, int endPage, int copies, int duplex)
        {
            PdfReader reader = null;
            Document sourceDocument = null;
            PdfCopy pdfCopyProvider = null;
            PdfImportedPage importedPage = null;
            reader = new PdfReader(sourcePDFpath);
            sourceDocument = new Document(reader.GetPageSizeWithRotation(startPage));
            pdfCopyProvider = new PdfCopy(sourceDocument, new FileStream(outputPDFpath, FileMode.Create));
            sourceDocument.Open();

            for (int c = 0; c < copies; c++)
            {
                for (int i = startPage; i <= endPage; i++)
                {
                    importedPage = pdfCopyProvider.GetImportedPage(reader, i);
                    pdfCopyProvider.AddPage(importedPage);
                }

                if (duplex > 1 && (endPage - startPage + 1) % 2 != 0)
                {
                    pdfCopyProvider.AddPage(reader.GetPageSize(1), reader.GetPageRotation(1));
                }
            }

            sourceDocument.Close();
            reader.Close();
        }
    }
}
