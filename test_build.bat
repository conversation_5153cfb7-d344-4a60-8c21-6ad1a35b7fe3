@echo off
echo Testing build with CG<PERSON> disabled...
echo.

echo Setting CGO_ENABLED=0
set CGO_ENABLED=0

echo.
echo Checking Go environment:
go env CGO_ENABLED
go env GOOS
go env GOARCH

echo.
echo Building project...
go build -ldflags "-<PERSON> windowsgui"

if %errorlevel% equ 0 (
    echo.
    echo ✅ Build successful!
    echo Generated executable: dora.exe
    if exist dora.exe (
        echo File size: 
        dir dora.exe | findstr dora.exe
    )
) else (
    echo.
    echo ❌ Build failed with error code: %errorlevel%
)

echo.
pause
