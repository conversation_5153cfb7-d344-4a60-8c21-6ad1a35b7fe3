package serial

import (
    "errors"
    "time"
)

const DefaultSize = 8 // Default value for Config.Size

type StopBits byte
type Parity byte

const (
    Stop1     StopBits = 1
    Stop1Half StopBits = 15
    Stop2     StopBits = 2
)

const (
    ParityNone  Parity = 'N'
    ParityOdd   Parity = 'O'
    ParityEven  Parity = 'E'
    ParityMark  Parity = 'M' // parity bit is always 1
    ParitySpace Parity = 'S' // parity bit is always 0
)

type Config struct {
    Name        string
    Baud        int
    ReadTimeout time.Duration // Total timeout
    Size        byte
    Parity      Parity
    StopBits    StopBits
}

var (
    ErrBadSize     = errors.New("unsupported serial data size")
    ErrBadStopBits = errors.New("unsupported stop bit setting")
    ErrBadParity   = errors.New("unsupported parity setting")
)

func OpenPort(c *Config) (*Port, error) {
    size, par, stop := c.Size, c.Parity, c.StopBits
    if size == 0 {
        size = DefaultSize
    }
    if par == 0 {
        par = ParityNone
    }
    if stop == 0 {
        stop = Stop1
    }
    return openPort(c.Name, c.Baud, size, par, stop, c.ReadTimeout)
}

func posixTimeoutValues(readTimeout time.Duration) (vmin uint8, vtime uint8) {
    const MAXUINT8 = 1<<8 - 1 // 255
    var minBytesToRead uint8 = 1
    var readTimeoutInDeci int64
    if readTimeout > 0 {
        minBytesToRead = 0
        readTimeoutInDeci = (readTimeout.Nanoseconds() / 1e6 / 100)
        if readTimeoutInDeci < 1 {
            readTimeoutInDeci = 1
        } else if readTimeoutInDeci > MAXUINT8 {
            readTimeoutInDeci = MAXUINT8
        }
    }
    return minBytesToRead, uint8(readTimeoutInDeci)
}