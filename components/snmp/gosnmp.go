package snmp

import (
    "fmt"
    "io/ioutil"
    "log"
    "math/big"
    "math/rand"
    "net"
    "strconv"
    "time"
)

const (
    MaxOids = 60
    baseOid = ".1.3.6.1.2.1"

    // Java SNMP uses 50, snmp-net uses 10
    defaultMaxRepetitions = 50
)

// GoSNMP represents GoSNMP library state
type GoSNMP struct {
    Conn           net.Conn
    Target         string
    Port           uint16
    Community      string
    Version        SnmpVersion
    Timeout        time.Duration
    Retries        int
    Logger         Logger
    loggingEnabled bool
    MaxOids        int
    MaxRepetitions uint8
    NonRepeaters   int
    requestID      uint32
    random         *rand.Rand
    rxBuf          *[rxBufSize]byte // has to be pointer due to https://github.com/golang/go/issues/11728
    // MsgFlags is an SNMPV3 MsgFlags
    MsgFlags SnmpV3MsgFlags
    // SecurityModel is an SNMPV3 Security Model
    SecurityModel SnmpV3SecurityModel

    // SecurityParameters is an SNMPV3 Security Model paramaters struct
    SecurityParameters SnmpV3SecurityParameters

    // ContextEngineID is SNMPV3 ContextEngineID in ScopedPDU
    ContextEngineID string

    // ContextName is SNMPV3 ContextName in ScopedPDU
    ContextName string

    // Internal - used to sync requests to responses - snmpv3
    msgID uint32
}

// Default connection settings
var Default = &GoSNMP{
    Port:      161,
    Community: "public",
    Version:   Version2c,
    Timeout:   time.Duration(2) * time.Second,
    Retries:   3,
    MaxOids:   MaxOids,
}

// SnmpPDU will be used when doing SNMP Set's
type SnmpPDU struct {
    // Name is an oid in string format eg ".1.3.6.1.4.9.27"
    Name string

    // The type of the value eg Integer
    Type Asn1BER

    // The value to be set by the SNMP set, or the value when
    // sending a trap
    Value interface{}

    // Logger implements the Logger interface
    Logger Logger
}

// Asn1BER is the type of the SNMP PDU
type Asn1BER byte

// Asn1BER's - http://www.ietf.org/rfc/rfc1442.txt
const (
    EndOfContents     Asn1BER = 0x00
    UnknownType               = 0x00 // TODO these should all be type Asn1BER, however
    Boolean                   = 0x01 // tests fail if implemented. See for example
    Integer                   = 0x02 /// http://stackoverflow.com/questions/5037610/typed-constant-declaration-list.
    BitString                 = 0x03
    OctetString               = 0x04
    Null                      = 0x05
    ObjectIdentifier          = 0x06
    ObjectDescription         = 0x07
    IPAddress                 = 0x40
    Counter32                 = 0x41
    Gauge32                   = 0x42
    TimeTicks                 = 0x43
    Opaque                    = 0x44
    NsapAddress               = 0x45
    Counter64                 = 0x46
    Uinteger32                = 0x47
    NoSuchObject              = 0x80
    NoSuchInstance            = 0x81
    EndOfMibView              = 0x82
)

// SNMPError is the type for standard SNMP errors.
type SNMPError uint8

// SNMP Errors
const (
    NoError             SNMPError = iota
    TooBig
    NoSuchName
    BadValue
    ReadOnly
    GenErr
    NoAccess
    WrongType
    WrongLength
    WrongEncoding
    WrongValue
    NoCreation
    InconsistentValue
    ResourceUnavailable
    CommitFailed
    UndoFailed
    AuthorizationError
    NotWritable
    InconsistentName
)

func (x *GoSNMP) Connect() error {
    var err error
    err = x.validateParameters()
    if err != nil {
        return err
    }

    addr := net.JoinHostPort(x.Target, strconv.Itoa(int(x.Port)))
    x.Conn, err = net.DialTimeout("udp", addr, x.Timeout)
    if err != nil {
        return fmt.Errorf("Error establishing connection to host: %s\n", err.Error())
    }
    if x.random == nil {
        x.random = rand.New(rand.NewSource(time.Now().UTC().UnixNano()))
    }
    x.msgID = uint32(x.random.Int31())
    x.requestID = x.random.Uint32()

    x.rxBuf = new([rxBufSize]byte)

    return nil
}

func (x *GoSNMP) validateParameters() error {
    if x.Logger == nil {
        x.Logger = log.New(ioutil.Discard, "", 0)
    } else {
        x.loggingEnabled = true
    }

    if x.MaxOids == 0 {
        x.MaxOids = MaxOids
    } else if x.MaxOids < 0 {
        return fmt.Errorf("MaxOids cannot be less than %d", 0)
    }

    if x.Version == Version3 {
        x.MsgFlags |= Reportable

        err := x.validateParametersV3()
        if err != nil {
            return err
        }
        err = x.SecurityParameters.init(x.Logger)
        if err != nil {
            return err
        }
    }

    return nil
}

func (x *GoSNMP) mkSnmpPacket(pdutype PDUType, pdus []SnmpPDU, nonRepeaters uint8, maxRepetitions uint8) *SnmpPacket {
    var newSecParams SnmpV3SecurityParameters
    if x.SecurityParameters != nil {
        newSecParams = x.SecurityParameters.Copy()
    }
    return &SnmpPacket{
        Version:            x.Version,
        Community:          x.Community,
        MsgFlags:           x.MsgFlags,
        SecurityModel:      x.SecurityModel,
        SecurityParameters: newSecParams,
        ContextEngineID:    x.ContextEngineID,
        ContextName:        x.ContextName,
        Error:              0,
        ErrorIndex:         0,
        PDUType:            pdutype,
        NonRepeaters:       nonRepeaters,
        MaxRepetitions:     maxRepetitions,
        Variables:          pdus,
    }
}

// Get sends an SNMP GET request
func (x *GoSNMP) Get(oids []string) (result *SnmpPacket, err error) {
    oidCount := len(oids)
    if oidCount > x.MaxOids {
        return nil, fmt.Errorf("oid count (%d) is greater than MaxOids (%d)",
            oidCount, x.MaxOids)
    }
    // convert oids slice to pdu slice
    var pdus []SnmpPDU
    for _, oid := range oids {
        pdus = append(pdus, SnmpPDU{oid, Null, nil, x.Logger})
    }
    // build up SnmpPacket
    packetOut := x.mkSnmpPacket(GetRequest, pdus, 0, 0)
    return x.send(packetOut, true)
}

// Set sends an SNMP SET request
func (x *GoSNMP) Set(pdus []SnmpPDU) (result *SnmpPacket, err error) {
    var packetOut *SnmpPacket
    switch pdus[0].Type {
    case Integer, OctetString:
        packetOut = x.mkSnmpPacket(SetRequest, pdus, 0, 0)
    default:
        return nil, fmt.Errorf("ERR:gosnmp currently only supports SNMP SETs for Integers and OctetStrings")
    }
    return x.send(packetOut, true)
}

// GetNext sends an SNMP GETNEXT request
func (x *GoSNMP) GetNext(oids []string) (result *SnmpPacket, err error) {
    oidCount := len(oids)
    if oidCount > x.MaxOids {
        return nil, fmt.Errorf("oid count (%d) is greater than MaxOids (%d)",
            oidCount, x.MaxOids)
    }

    // convert oids slice to pdu slice
    var pdus []SnmpPDU
    for _, oid := range oids {
        pdus = append(pdus, SnmpPDU{oid, Null, nil, x.Logger})
    }

    // Marshal and send the packet
    packetOut := x.mkSnmpPacket(GetNextRequest, pdus, 0, 0)

    return x.send(packetOut, true)
}

// GetBulk sends an SNMP GETBULK request
//
// For maxRepetitions greater than 255, use BulkWalk() or BulkWalkAll()
func (x *GoSNMP) GetBulk(oids []string, nonRepeaters uint8, maxRepetitions uint8) (result *SnmpPacket, err error) {
    oidCount := len(oids)
    if oidCount > x.MaxOids {
        return nil, fmt.Errorf("oid count (%d) is greater than MaxOids (%d)",
            oidCount, x.MaxOids)
    }

    // convert oids slice to pdu slice
    var pdus []SnmpPDU
    for _, oid := range oids {
        pdus = append(pdus, SnmpPDU{oid, Null, nil, x.Logger})
    }

    // Marshal and send the packet
    packetOut := x.mkSnmpPacket(GetBulkRequest, pdus, nonRepeaters, maxRepetitions)
    return x.send(packetOut, true)
}

//
// SNMP Walk functions - Analogous to net-snmp's snmpwalk commands
//

type WalkFunc func(dataUnit SnmpPDU) error

func (x *GoSNMP) BulkWalk(rootOid string, walkFn WalkFunc) error {
    return x.walk(GetBulkRequest, rootOid, walkFn)
}

func (x *GoSNMP) BulkWalkAll(rootOid string) (results []SnmpPDU, err error) {
    return x.walkAll(GetBulkRequest, rootOid)
}

func (x *GoSNMP) Walk(rootOid string, walkFn WalkFunc) error {
    return x.walk(GetNextRequest, rootOid, walkFn)
}

func (x *GoSNMP) WalkAll(rootOid string) (results []SnmpPDU, err error) {
    return x.walkAll(GetNextRequest, rootOid)
}

func Partition(currentPosition, partitionSize, sliceLength int) bool {
    if currentPosition < 0 || currentPosition >= sliceLength {
        return false
    }
    if partitionSize == 1 { // redundant, but an obvious optimisation
        return true
    }
    if currentPosition%partitionSize == partitionSize-1 {
        return true
    }
    if currentPosition == sliceLength-1 {
        return true
    }
    return false
}

func ToBigInt(value interface{}) *big.Int {
    var val int64
    switch value := value.(type) { // shadow
    case int:
        val = int64(value)
    case int8:
        val = int64(value)
    case int16:
        val = int64(value)
    case int32:
        val = int64(value)
    case int64:
        val = int64(value)
    case uint:
        val = int64(value)
    case uint8:
        val = int64(value)
    case uint16:
        val = int64(value)
    case uint32:
        val = int64(value)
    case uint64:
        return (uint64ToBigInt(value))
    case string:
        // for testing and other apps - numbers may appear as strings
        var err error
        if val, err = strconv.ParseInt(value, 10, 64); err != nil {
            return new(big.Int)
        }
    default:
        return new(big.Int)
    }
    return big.NewInt(val)
}
