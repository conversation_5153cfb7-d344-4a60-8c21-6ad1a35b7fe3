package handles

import (
	//"net/url"
	"net/url"
	"strconv"
	"strings"
)

type FileVersion struct {
	File  string
	MD5   string
	Index string
	URL   string
}

// GetVersionMap 解析版本
func GetVersionMap(api, s string) (version string, vmap map[string]FileVersion) {
	v := strings.FieldsFunc(s, func(i rune) bool {
		return i == '\r' || i == '\n'
	})

	vmap = make(map[string]FileVersion)
	for i, line := range v {
		if i == 0 {
			version = line
		} else {
			arr := strings.Split(line, " ")
			if len(arr) == 2 {
				var result string
				sArray := strings.Split(arr[1], "\\")
				for _, s := range sArray {
					if result != "" {
						result = result + "\\"
					}
					result = result + url.QueryEscape(s)
				}
				url := strings.Replace(api+version+"/"+result, "\\", "/", -1)
				vmap[arr[1]] = FileVersion{arr[1], arr[0], strconv.Itoa(i), url}
			}
		}
	}

	return
}
