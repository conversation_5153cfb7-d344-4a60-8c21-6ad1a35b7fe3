package common

import (
    "os"
    "strings"
)

// GetVersion path 1是根目录 2是一级子目录 app 1是主程序 2是监控程序
func GetVersion(path, app int) string {
    file := "VERSION"
    if path == 2 {
        file = "../VERSION"
    }
    if app == 2 {
        file = "MONITORVERSION"
        if path == 2 {
            file = "../MONITORVERSION"
        }
    }
    fo, err := os.Open(file)
    if os.IsNotExist(err) {
        f, err := os.Create(file)
        defer f.Close()
        if err != nil {
            panic(err)
        } else {
            f.WriteString("1.0.0")
        }
        return "1.0.0"
    }
    buf := make([]byte, 1024)
    n, err := fo.Read(buf)
    return strings.TrimSpace(string(buf[:n]))
}