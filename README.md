# 多拉中间件安装使用说明
> 系统镜像、工具驱动以及使用说明 链接: https://pan.baidu.com/s/1hsaH5U8 密码: ik3m

1. 安装打印机驱动<不要安装监视器>（驱动文件见supports包中Y15B_C1_UL-inst-E1.EXE），安装方式为TCP/IP协议，安装完成后记录打印机IP
2. 打印机驱动安装完成后安装gs打印库（文件见supports包中gs921w64.exe），默认点下一步完成
3. 安装扫描头，将扫描头修改为串口连接，并记录串口号`COM{num}`
4. 修改配置文件（conf/app.ini） [Printer] 中 IPAddress=上面记录的IP；修改配置文件 [BarCode] 中 PortName=第三步中串口号
5. 运行终端`monitor.exe`
6. `ALT+CTRL+WIN+SHIFT+O`或`ALT+CTRL+O`切换桌面是否开启 `ALT+CTRL+WIN+SHIFT+X`或`ALT+CTRL+X`退出终端和监控程序
7. ctrl+alt+o打开桌面 ctrl+alt+k杀死桌面 ctrl+alt+x关闭程序并打开桌面

# 初始化中间件 && 退出、重启中间件
> 触屏模式长按操作为右击

1. 连接外接键盘按WIN键，点击开始按钮右侧的`任务视图`按键。在弹出的界面中点击最右侧新建桌面，点击并进入新桌面
2. 在新桌面按`WIN`弹出任务栏，在右击任务栏选择任务管理程序结束`monitor.exe`、`dora.exe`或者进行其他操作，结束了`dora.exe`之后返回桌面1删除桌面2即可，详情查看附图
3. 若此时未连接WiFi，则点击右下方的WiFi按钮，弹出WiFi连接界面，连接对应WiFi即可
4. 系统第一次运行的时候需要配置打印机：运行附加程序`PrinterMaintain.exe`，查看提示即可。若配置失败，需要重新安装打印机驱动。*（点了获取IP之后会检测一下IP和打印机是否匹配，不匹配的话我会设置一下，设置失败或者没有打印机的时候会提示你安装驱动，若匹配，则直接跳过。最后再打开打印机设置页面）*

> 若需要终止监控程序，也可：按WIN键，输入`taskkill /f /t /im monitor.exe`，或者执行程序目录下killtask.cmd

> 平时维护的时候建议严格使用键盘和鼠标，**不要使用触摸键盘**
---

<img src="screens/x1.png" width="320" height="180">
---
<img src="screens/x2.png" width="320" height="180">
---
<img src="screens/x3.png" width="320" height="180">
---
<img src="screens/x4.png" width="320" height="180">

# 多拉更新使用说明
1. HTML前端目录为`html/src`编译后目录为`html/dist`，HTML编译命令`cd html` `npm install && npm run build`
2. 修改文件 VERSION 修改版本号
3. [OR] 如果要本地运营测试，直接`go build` 本地运行即可，要发布的话，跳过此步执行步骤4：
4. [OR] 双击执行`release.bat`自动编译发版
5. 执行release完毕后自动打包文件目录为`release/binaries/x.x.x/dora.zip`
6. 上传`release/binaries/x.x.x`、`release/dora.json`，`release/binaries/monitor/x.x.x`、`release/monitor/monitor.json`到终端更新服务器`/var/www/doraterminal/`


# 多拉中间件更新日志

### 2017-05-23
- [x] 测试、发版

### 2017-05-22
- [x] 浏览器二次开发
- [x] 串口处理库合并整理
- [x] 继续测试

### 2017-05-21
- [ ] 测试终端稳定性[今天没测试！]
- [x] 浏览器二次开发[95%，今天编译失败，明天上午搞定]
- [x] pdf处理库合并整理

### 2017-05-20
- [x] 二次开发浏览器[80%]
- [x] 与兄弟技术沟通[*兄弟的技术不接电话*]
- [x] 使用不同类型的各种订单进行反复测试系统稳定性
- [x] 终端JS新开发
- [x] SNMP库合并整理

### 2017-05-19#2
- [x] 更新方案重写
- [x] 自动Release
- [x] 更新服务端调整
- [x] 添加闲时与自动更新（检测到更新包之后若终端未被操作，未打印中，未扫描中之后5分钟自动更新，更新完毕后自动重启终端）
- [x] 二次开发浏览器[60%]
- [x] 新增默认配置，防止更新时覆盖配置文档

### 2017-05-19#1
- [x] 热更新部署方法[100%]
- [x] 热更新服务端
- [x] 热更新部署本地端
- [x] 增量热更新
- [x] 继续打印机高强度稳定性测试

### 2017-05-18
- [x] 二次开发浏览器[40%]
- [x] 打印机高强度稳定性测试
- [x] 热更新部署方法[40%]

### 2017-05-17
- [x] 打印过程向终端实时更新是否打印成功
- [x] 终端配置处理
- [x] 终端运行监控[monitor.exe]，误退出处理
- [x] 打印机设置

### 2017-05-16
- [x] 与终端进行联调扫码枪
- [x] 打印错误处理以及其他非监控信息处理
- [x] 打印机综合调配以及文件打印出纸
- [x] 打印过程向终端实时更新是否打印成功[*第三个任务给耽搁了，调用c#程序的时候发现了一个问题，重写了打印库*]

### 2017-05-15
- [x] 对终端下载任务处理
- [x] 对终端扫码任务处理
- [x] 对终端打印任务处理
- [x] 终端对中间件通信协议制定与实施
- [x] 打印机状态监控与获取
- [x] 联系兄弟技术处理遗留问题 [*已联系，反馈说需要等到周五*]

### 2017-05-14#2
- [x] 终端下载任务通信以及测试效果展示
- [x] 下载任务使用多线程处理
- [x] 定时删除临时文件夹，时间可自行配置
- [x] 自定义配置新增`TimeLoop`，`LastModified`

### 2017-05-14#1
- [x] 对终端下载任务处理以及临时文件处理

### 2017-05-13
- [x] 封装支持v1, v2的snmp请求包
- [x] 模块化配置读取
- [x] 优化目录结构
- [x] 终端启动初始化后，自动获取扫描头和打印机是否可达并通知终端

#### 通知示例
```json
{"action":"NOTIFY","detail":"exist","device":"printer"}
```

```json
{"action":"NOTIFY","detail":"exist","device":"barcode"}
```

### 2017-05-12#3
- [x] C#驱动双面打印

### 2017-05-12#2
- [x] 结构化设备
- [x] 模块化解析通信消息
- [x] 多份打印
- [x] 消息格式规范化

#### 消息示例：
```json
{"action", "BARCODESCAN", "detail", ""}
```

### 2017-05-12#1
- [x] 打印文件(RAW)

### 2017-05-11#2
- [x] 切分PDF文档

### 2017-05-11#1
- [x] ~~C++打印文件~~
- [x] ~~Golang调用c++读取PDF文档~~
- [x] ~~Golang调用C++打印PDF文档~~
- [x] ~~C++编辑PDF文档~~

### 2017-05-10
- [x] 设置扫描头
- [x] 获取扫描头状态
- [x] 控制扫描头读码、取消读码
