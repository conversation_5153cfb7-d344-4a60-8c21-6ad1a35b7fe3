package bridge

import (
    "fmt"
    "dora/logger"
    "dora/windows"
    "os"
    "path/filepath"
    "strings"
    "syscall"
    "time"

    "github.com/json-iterator/go"
)

var allowedExts = map[string]bool{
    ".pdf":  true,
    ".docx": true,
    ".doc":  true,
    ".ppt":  true,
    ".pptx": true,
    ".xls":  true,
    ".xlsx": true,
    ".rtf":  true,
    ".dot":  true,
    ".dotx": true,
    ".docm": true,
    ".dotm": true,
    ".xlt":  true,
    ".xla":  true,
    ".xltm": true,
    ".xltx": true,
    ".xlsm": true,
    ".xlam": true,
    ".xlsb": true,
    ".pot":  true,
    ".pps":  true,
    ".ppa":  true,
    ".potx": true,
    ".ppsx": true,
    ".ppam": true,
    ".pptm": true,
    ".potm": true,
    ".ppsm": true,
    ".jpg":  true,
    ".jpe":  true,
    ".jpeg": true,
    ".jfif": true,
    ".gif":  true,
    ".png":  true,
    ".bmp":  true,
    ".dib":  true,
    ".tif":  true,
    ".tiff": true,
    ".wmf":  true,
    ".emf":  true,
}

// all support files path
var folders *Folder

// UsbDetector USB检测
type UsbDetector struct {
    TimeOut    time.Duration
    Exist      bool
    Drivers    map[string]string
    IsPrinting bool // 是否正在disk打印流程
    Scanning   bool // 是否正在detector流程
    ReStartCh  chan bool
}

type UsbFile struct {
    Name           string `json:"name"`
    Size           int64  `json:"size"`
    LastModified   string `json:"last_modified"`
    CreationTime   string `json:"creation_time"`
    LastAccessTime string `json:"last_access_time"`
}

type Folder struct {
    Name         string             `json:"name"`
    Desc         string             `json:"desc"`
    LastModified string             `json:"last_modified"`
    Files        []UsbFile          `json:"files"`
    Folders      map[string]*Folder `json:"folders"`
}

var DefaultUSBDetector = &UsbDetector{
    TimeOut:    time.Second * 55,
    Exist:      false,
    Drivers:    nil,
    IsPrinting: false,
    ReStartCh:  make(chan bool),
}

var usbSupportPath = filepath.Join("vendor", "USBSupport")

// Eject all usb drives
func (u *UsbDetector) Eject(f func(s string)) {
    ret, err := windows.EjectUsb(usbSupportPath)
    obj := map[string]interface{}{
        "action": USBEJECT,
        "detail": true,
    }
    if err != nil {
        logger.App().Errorln(err)
    } else {
        logger.App().Info(ret)
    }
    b, _ := jsoniter.Marshal(obj)
    f(string(b))
}

func (u *UsbDetector) EjectAll() {
    ret, err := windows.EjectUsb(usbSupportPath)
    if err != nil {
        logger.App().Errorln(err)
    } else {
        logger.App().Info(ret)
    }
}

// check usb drive exists
func (u *UsbDetector) USBExists() bool {
    got, drives := windows.GetUsbDrives(usbSupportPath, -1)
    if !got {
        return false
    } else {
        return len(drives) > 0
    }
}

func (u *UsbDetector) StopToPrint(f func(s string)) {
    logger.App().Info("等待移除U盘进行打印")
    timer := time.NewTimer(u.TimeOut)
    for {
        select {
        case <-timer.C:
            go u.EjectAll()
            u.Exist = false
            u.Drivers = nil
            timer.Stop()
            f(`{"action":"USBEJECT","detail":true,"DISKPRINTING":true}`)
            logger.App().Info("USB设备超时未移除，终端自动进行下一步操作")
            return
        default:
            if u.Exist {
                logger.App().Info("用户尚未移除U盘")
            } else {
                u.Exist = false
                u.Drivers = nil
                f(`{"action":"USBEJECT","detail":true,"DISKPRINTING":true}`)
                logger.App().Info("用户已经移除设备，开始打印流程")
                return
            }
            time.Sleep(time.Second)
        }
    }
}

func (u *UsbDetector) AutoDetector(f func(s string)) {
    for {
        if u.Scanning && !u.Exist {
            begin := time.Now()
            logger.App().Info("开始监测U盘")
            got, drivers := windows.GetUsbDrives(usbSupportPath, -1)
            logger.App().Info("结束监测U盘,用时%v", time.Now().Sub(begin))
            logger.App().Infof("got %v,%v", got, drivers)
            if got {
                if u.IsPrinting {
                    go u.EjectAll()
                } else {
                    u.Exist = true
                    u.Drivers = drivers
                }
            }
        }
        if u.Exist {
            got, _ := windows.GetUsbDrives(usbSupportPath, -1)
            if !got {
                u.Exist = false
                u.Drivers = nil
                time.Sleep(time.Second)
                if !u.IsPrinting {
                    f(`{"action":"USBEJECT","detail":true,"DISKPRINTING":false}`)
                }
            }
        }
        time.Sleep(time.Second)
    }
}

// Scan scan all usb and get readable usb drive with support files
func (u *UsbDetector) Scan(f func(s string)) {
    u.Scanning = true
    obj := map[string]interface{}{
        "action": USBDETECTOR,
        "detail": "",
        "got":    0,
    }
    fn := func() {
        u.Scanning = false
        b, _ := jsoniter.Marshal(obj)
        fmt.Println(string(b))
        f(string(b))
    }
    defer fn()
    timer := time.NewTimer(u.TimeOut)
    for {
        select {
        case <-timer.C:
            logger.App().Info("U盘检测超时")
            if err, ok := obj["error"]; ok {
                obj["got"] = -1
                obj["error"] = err
            }
            return
        case <-u.ReStartCh:
            timer.Reset(u.TimeOut)
        default:
            if u.Exist {
                logger.App().Info("已检测到U盘,开始读取文件列表")
                obj["got"] = 1
                detail := []*Folder{}
                for u, d := range u.Drivers {
                    folders = newFolder(u)
                    folders.Desc = d
                    filepath.Walk(u+"\\", visit)
                    detail = append(detail, folders)
                }
                obj["detail"] = detail
                logger.App().Info("U盘文件列表读取完毕")
                return
            } else {
                logger.App().Info("AutoDetector尚未检测到U盘")
            }
        }
        time.Sleep(time.Second)
    }
}

func newFolder(name string) *Folder {
    return &Folder{name, "", "", []UsbFile{}, make(map[string]*Folder)}
}

func (f *Folder) getFolder(name string) *Folder {
    nextF, ok := f.Folders[name]
    if !ok {
        nextF = newFolder(name)
        nextF.LastModified = getFolderLastModified(lastPath, name)
        f.Folders[name] = nextF
    }
    return nextF
}

func (f *Folder) addPath(path []string, file UsbFile) {
    for i, segment := range path {
        // the root
        if i > 0 {
            // the last one has none subs
            if i == len(path)-1 {
                if segment != file.Name {
                    fo := newFolder(segment)
                    fo.LastModified = getFolderLastModified(lastPath, segment)
                    f.Folders[segment] = fo
                } else {
                    f.Files = append(f.Files, file)
                }
            } else {
                // check/get and put subs into parent folder
                f.getFolder(segment).addPath(path[1:], file)
                return
            }
        } else {
            lastPath = append(lastPath, segment)
        }
    }
}

// get the folder's last modifition time
func getFolderLastModified(p []string, name string) (mtime string) {
    p = append(p, name)
    fo := p[0] + string(os.PathSeparator) + filepath.Join(p[1:]...)
    f, err := os.Stat(fo)
    if err == nil {
        return f.ModTime().Format("2006-01-02 15:04:05")
    }
    return
}

var lastPath []string

func visit(path string, f os.FileInfo, err error) error {
    //logger.App().Println("all path = " + path)

    filterArray := []string{".idea", ".git", ".Trashes", ".fseventsd", ".Spotlight"}
    for _, str := range filterArray {
        if strings.HasSuffix(path, str) {
            return nil
        }
    }

    ext := filepath.Ext(path)
    _, ok := allowedExts[ext]
    if ok {
        // check ishidden
        if !windows.PathHidden(path) {
            //logger.App().Println("allowed file path =" + path)
            _path := strings.Split(path, string(os.PathSeparator))
            stat := f.Sys().(*syscall.Win32FileAttributeData)
            file := UsbFile{
                Name:           f.Name(),
                Size:           f.Size(),
                CreationTime:   time.Unix(0, stat.CreationTime.Nanoseconds()).Format("2006-01-02 15:04:05"),
                LastModified:   time.Unix(0, stat.LastWriteTime.Nanoseconds()).Format("2006-01-02 15:04:05"),
                LastAccessTime: time.Unix(0, stat.LastAccessTime.Nanoseconds()).Format("2006-01-02 15:04:05"),
            }
            lastPath = []string{}
            folders.addPath(_path, file)
        }
    }
    return nil
}

