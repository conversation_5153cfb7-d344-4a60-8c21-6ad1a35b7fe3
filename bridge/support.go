package bridge

type Support struct {
	Terminal        *Terminal `conf:"Terminal"`
	BarCode         *BarCode  `conf:"BarCode"`
	Printer         *Printer  `conf:"Printer"`
	File            *Trapper  `conf:"File"`
	UsbDetector     *UsbDetector
	DiskFileSupport *DiskFileSupport
}

// 终端配置
type Terminal struct {
	URL       string `ini:"URL" conf:"URL"`
	UpdateURL string `ini:"UpdateURL" conf:"UpdateURL"`
	Mode      string `ini:"Mode" conf:"Mode"`
	Idle      bool
}

// 版本结构
type VersionInfo struct {
	Version string
	FileMD5 string
}

func ReadAcroRd32Path() string {
	return `C:\Program Files (x86)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe`
}

func (t *Terminal) GetIdle() bool {
	return t.Idle
}
