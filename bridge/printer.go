package bridge

import (
	"dora/components/browser"
	g "dora/components/snmp"
	"dora/helpers"
	log "dora/logger"
	"dora/windows"
	"encoding/json"
	"fmt"
	"net"
	"path/filepath"
	"strconv"
	"time"

	"github.com/StackExchange/wmi"
	"github.com/alexbrainman/printer"
	"github.com/json-iterator/go"
	"github.com/sirupsen/logrus"
)

const (
	StatusOID     = ".1.3.6.1.2.1.43.18.1.1.6"
	CancelOID     = ".1.3.6.1.4.1.2435.2.3.9.4.2.1.5.4.4.6"
	MaintenaceOID = ".1.3.6.1.4.1.2435.2.3.9.4.2.1.5.5.8.0"
)

const (
	PTNTYPEPDF  = 1 << iota // 正常 pdf打印
	PTNTYPEDISK             // 本地文件打印
)

//var CancelOID = []string{".1.3.6.1.4.1.2435.2.3.9.4.2.1.5.4.4.6"}

var DEVICE_STATUS = map[string]string{
	"10001": "就绪",
	"10003": "正在初始化",
	"10007": "正在取消任务",
	"10023": "正在打印",
	"10205": "需要更换硒鼓",
	"10209": "鼓粉即将耗尽（警告）",
	"10305": "需要更换硒鼓",
	"10316": "需要更换定影仪",
	"12201": "纸盒1打开错误",
	"12301": "纸盒2打开错误",
	"12401": "纸盒3打开错误",
	"12501": "纸盒4打开错误",
	"12601": "纸盒5打开错误",
	"15001": "出纸槽已满",
	"15011": "由于仅使用标准纸盘而引起的纸槽已满",
	"15021": "邮箱1出纸时纸槽已满",
	"15022": "邮箱出纸时后盖被打开",
	"15031": "邮箱2出纸时纸槽已满",
	"15041": "邮箱3出纸时纸槽已满",
	"15051": "邮箱4出纸时纸槽已满",
	"30016": "内存已满",
	"40000": "深度休眠",
	"40021": "前盖／顶盖被打开",
	"40105": "指定邮箱时后盖被打开",
	"40121": "后盖被打开",
	"40309": "鼓粉已耗尽（错误）",
	"40405": "硒鼓未安装",
	"40409": "无粉盒",
	"42104": "打印机卡纸",
	"44102": "打印机卡纸",
	"44113": "打印机卡纸",
	"44114": "打印机卡纸",
	"44115": "打印机卡纸",
	"44116": "打印机卡纸",
	"48022": "打印机卡纸",
	"50076": "定影仪错误（警告）",
	"60000": "存储设备或内存已满",
	"60003": "正在冷却中",
	"60005": "硒鼓需清洁",
	"60021": "定影仪盖板被打开",
	"60023": "双面打印纸张尺寸错误",
	"60030": "机器错误",
	"60129": "打印纸张类型设定与机器纸张类型设定不一致",
	"60142": "纸张尺寸错误",
	"60144": "新粉盒检测错误",
	"60152": "USB接口电流过载",
	"60163": "鼓粉已耗尽（错误）",
	"60164": "接入网络出错（服务器超时）",
	"60165": "接入网络出错（验证错误）",
	"60166": "接入网络出错（文件读取错误）",
	"60167": "接入网络出错（错误的时间／日期）",
	"60179": "纸盘过多",
	"60182": "过零点错误",
	"60185": "定影仪等待超过十分钟",
	"60191": "塔式纸盘电源关闭",
	"60192": "3槽邮箱",
	"62002": "需更换激光元件",
	"62109": "需更换纸盘t1输纸套件 ",
	"62110": "需更换纸盘t2输纸套件 ",
	"62111": "需更换纸盘t3输纸套件 ",
	"62112": "需更换纸盘t4输纸套件 ",
	"62116": "需更换纸盘mp输纸套件 ",
	"62121": "鼓粉即将耗尽（警告）",
	"62122": "纸盘1纸量低（警告）",
	"62123": "纸盘2纸量低（警告）",
	"62124": "纸盘3纸量低（警告）",
	"62125": "纸盘4纸量低（警告）",
	"62126": "需更换纸盘t5输纸套件 ",
	"62127": "纸盘5纸量低（警告）",
	"65003": "[纸盘1卡纸] 纸盘1卡纸错误",
	"65004": "[纸盘2卡纸] 纸盘2卡纸错误",
	"65005": "[纸盘2卡纸] 纸盘2卡纸错误",
	"65008": "[纸盘3卡纸] 纸盘3卡纸错误",
	"65009": "[纸盘3卡纸] 纸盘3卡纸错误",
	"65012": "打印机卡纸",
	"65013": "[纸盘4卡纸] 纸盘4卡纸错误",
	"65024": "打印机卡纸",
	"65025": "[纸盘5卡纸] 纸盘5卡纸错误",
	"11213": "纸盒1中无纸（警告）",
	"11313": "纸盒2中无纸（警告）",
	"11413": "纸盒3中无纸（警告）",
	"11513": "纸盒4中无纸（警告）", // 115xx
	"11613": "纸盒5中无纸（警告）", // xx -> 13
	"41013": "纸盒mp中无纸",    // xx -> 13
	"41113": "手动送纸口无纸",    // xx -> 13
	"41213": "纸盒1中无纸",     // xx -> 13
	"41313": "纸盒2中无纸",     // xx -> 13
	"41413": "纸盒3中无纸",     // xx -> 13
	"41513": "纸盒4中无纸",     // xx -> 13
	"41613": "纸盒5中无纸",     // 416xx
	"41913": "打印机无纸",
	// additional code for maintenance
	"80001": "硒鼓余量", // 硒鼓余量
	"80002": "鼓粉余量", //鼓粉余量
	"90001": "硒鼓余量（少于等于10%）余量低（Low）",
	"90002": "硒鼓余量（少于等于5%）接近为空（nearly empty）",
	"90003": "硒鼓余量（0%）为空（empty）	",
	"90004": "鼓粉余量（少于等于15%）余量低（Low）",
	"90005": "鼓粉余量（少于等于10%）接近为空（nearly empty）",
	"90006": "鼓粉余量（0%）为空（empty）	",
}

type confPrinter struct {
	*Printer
}

type Printer struct {
	NormalName    string         `ini:"NormalName" json:"-" conf:"NormalName"`
	IPAddress     string         `ini:"IPAddress" json:"-" conf:"IPAddress"`
	Tray          string         `ini:"Tray" conf:"Tray"`
	Exist         bool           `json:"exist"` // 设备是否可达
	PrinterStatus *PrinterStatus `json:"printer_status"`
	isPrinting    bool
	AcroRd32Path  string
}

type PrinterStatus struct {
	Action   string            `json:"action"`
	Detail   string            `json:"detail"` // 设备状态
	Normal   bool              `json:"normal"` // 设备是否可打印
	Code     []string          `json:"code"`
	Infos    map[string]string `json:"infos,omitempty"`
	Supplies *Supplies         `json:"supplies,omitempty"`
}

type Win32_Printer struct {
	Name                   string
	WorkOffline            bool
	PrinterStatus          int
	PrinterState           int
	JobCountSinceLastReset int
	PortName               string
	SystemName             string
	DeviceID               string
	DriverName             string
	PrintJobDataType       string
	PrintProcessor         string
	Status                 string
}

type Supplies struct {
	Toner int `json:"toner"` // 鼓粉 0满 1少 2非常少 3空
	Drum  int `json:"drum"`  // 硒鼓 0好 1快坏了 2坏了
	Paper int `json:"paper"` // 纸箱 0满 1少 2空
}

// Exists
func (p *Printer) Exists(f func(s string)) {
	obj := map[string]string{
		"action": "NOTIFY",
		"device": "printer",
		"detail": "unknown",
	}

	r := helpers.NewReacher()
	ra, err := net.ResolveIPAddr("ip4:icmp", p.IPAddress)
	if err != nil {
		log.Printer().Fatalf("配置文件错误，请检查打印机IP配置。%v", err)
	}
	r.AddIP(ra.String())
	//fmt.Println(ra.String())
	r.OnRecv = func(addr *net.IPAddr, dur time.Duration) {
		p.Exist = true
		obj["detail"] = "exist"
	}
	r.Run()

	tc, err := json.Marshal(obj)
	if err != nil {
		log.Printer().Errorf("%s 转码失败：%v", "printer exists", err)
	}
	f(string(tc))
}

func (p *Printer) GetDriverStatus() int {
	printerStr, err := printer.Default()
	if printerStr == "" { //error occurs in getting printer driver
		log.Printer().Errorf("获取驱动状态失败,%v", err)
	} else {
		var dst []Win32_Printer
		q := wmi.CreateQuery(&dst, "Where DeviceID=\""+printerStr+"\"")
		err = wmi.Query(q, &dst)
		if err != nil {
			log.Printer().Errorf("获取驱动状态失败,%v", err)
		} else {
			return dst[0].PrinterState
		}
	}
	return -1
}

// Status get printer status
// ret: return result
func (p *Printer) GetStatus(w *liasicaectron.Window, ret bool) *PrinterStatus {
	// printer status initialization
	p.PrinterStatus = new(PrinterStatus)
	p.PrinterStatus.Supplies = new(Supplies)
	p.PrinterStatus.Action = PRINTERSTATUS
	p.PrinterStatus.Infos = make(map[string]string)
	p.PrinterStatus.Normal = true

	if PCFreeC>0 && PCFreeC < 100 {
		p.PrinterStatus.Normal = false
		p.PrinterStatus.Detail = "C盘空间不足"
		p.PrinterStatus.Infos["DiskStatus"] = "C盘空间不足"
	} else {
		driverStatus := p.GetDriverStatus()
		if driverStatus == 128 {
			p.PrinterStatus.Normal = false
			p.PrinterStatus.Detail = "打印驱动脱机"
			p.PrinterStatus.Infos["DriverStatus128"] = "打印驱动脱机"
		} else {
			g.Default.Target = p.IPAddress
			g.Default.Community = "public"
			err := g.Default.Connect()
			var code []string
			if err != nil {
				p.PrinterStatus.Normal = false
				log.Printer().Errorf("连接失败：%v", err)
			} else {
				defer g.Default.Conn.Close()
				//定义回调函数，每个GetNext操作完成后用来处理返回的SnmpPDU
				//函数类型：type WalkFunc func(dataUnit SnmpPDU) error
				fn := func(v g.SnmpPDU) error {
					fmt.Printf("oid: %s, value: ", v.Name)
					switch v.Type {
					case g.OctetString:
						fmt.Printf("%s\n", string(v.Value.([]byte)))
					default:
						fmt.Printf("%s\n", g.ToBigInt(v.Value).String())
					}
					c := g.ToBigInt(v.Value).String()
					code = append(code, c)
					p.PrinterStatus.Code = code
					var value string
					p.statusDetail(c, value)
					return nil
				}
				err = g.Default.Walk(StatusOID, fn)
				if err != nil {
					//p.PrinterStatus.Normal = false
					log.Printer().Errorf("获取OID失败：%v", err)
					return nil
				}
				// start to get the maintenance info
				oids := []string{MaintenaceOID}
				pck, gerr := g.Default.Get(oids)
				if gerr != nil {
					//p.PrinterStatus.Normal = false
					log.Printer().Errorf("获取MaintenanceOID失败：%v", gerr)
					return nil
				}
				var pdus []g.SnmpPDU
				var rCode string
				pdus = pck.Variables
				for _, v := range pdus {
					fmt.Printf("oid: %s, value: ", v.Name)
					switch v.Type {
					case g.OctetString:
						rCode = fmt.Sprintf("%X", v.Value) //  DrumLife: 【19， 20】; TonerLife:  【33， 34】
						hexDrumLife := rCode[38:42]
						DrumLife := hexdec(hexDrumLife)
						hexTonerLife := rCode[66:70]
						TonerLife := hexdec(hexTonerLife)
						var drumCode string
						var tonerCode string
						drumValue := fmt.Sprintf("%d", DrumLife/100) + "%"
						tonerValue := fmt.Sprintf("%d", TonerLife/100) + "%"

						// "80001": "硒鼓余量%%",
						// "80002": "鼓粉余量%%",
						code = append(code, "80001")
						p.statusDetail("80001", drumValue)
						code = append(code, "80002")
						p.statusDetail("80002", tonerValue)
						// less than 10%
						if DrumLife <= 1000 {
							drumCode = "90001"
						}
						// less than 5%
						if DrumLife <= 500 {
							drumCode = "90002"
						}
						if DrumLife == 0 {
							drumCode = "90003"
						}
						// less than 20%
						if TonerLife <= 1500 {
							tonerCode = "90004"
						}
						// less than 10%
						if TonerLife <= 1000 {
							tonerCode = "90005"
						}
						if TonerLife == 0 {
							tonerCode = "90006"
						}
						if drumCode != "" {
							code = append(code, drumCode)
							p.statusDetail(drumCode, "")
						}
						if tonerCode != "" {
							code = append(code, tonerCode)
							p.statusDetail(tonerCode, "")
						}
						p.PrinterStatus.Code = code
					default:
						rCode = g.ToBigInt(v.Value).String()
						fmt.Printf("%s\n", g.ToBigInt(v.Value).String())
						log.Printer().Infof("Printer Maintenance value：%s", string(rCode))
					}
				}
			}
		}
	}
	// 记录日志
	tc, _ := json.Marshal(p.PrinterStatus)
	log.Stats().Info(tc)
	if !ret {
		// send to client
		w.Send(string(tc))
	}
	return p.PrinterStatus
}

/*
{\"action\":\"PRINTERSTATUS\",\"detail\":\"纸盘2纸量低（警告）\",\"normal\":true,\"code\":[\"11213\",\"62123\"],\"infos\":{\"11213\":\"纸盒1中无纸（警告）\",\"62123\":\"纸盘2纸量低（警告）\"},\"supplies\":{\"toner\":0,\"drum\":0,\"paper\":0}}"
*/
func (p *Printer) statusDetail(code string, value string) bool {
	tray, err := strconv.Atoi(p.Tray)
	if err != nil {
		tray = 3
	}

	if code == "10001" {
		p.PrinterStatus.Detail = "ready"
	} else {
		if code == "10023" || (p.isPrinting && code == "60003") {
			p.PrinterStatus.Detail = "printing"
		}
		if p.PrinterStatus.Detail != "ready" && p.PrinterStatus.Detail != "printing" {
			p.PrinterStatus.Detail = ""
			//p.PrinterStatus.Detail = DEVICE_STATUS[code]
		}
		switch code {
		case "":
			fmt.Println(code, "empty")
			return false
		case "10205":
			p.PrinterStatus.Supplies.Drum = 1
		case "10209", "62121":
			p.PrinterStatus.Supplies.Toner = 2
		case "12201", "12301", "12401", "12501", "12601", "10305", "15001", "15011", "15021", "15022", "15031", "15041", "15051", "30016", "40021", "40105", "40121", "40309", "40405", "40409", "42104", "44102", "44113", "44114", "44115", "44116", "48022", "50076", "60000", "60005", "60021", "60030", "60023", "60129", "60142", "60144", "60152", "60163", "60164", "60165", "60166", "60167", "60179", "60182", "60185", "60191", "60192", "62002", "62109", "62110", "62111", "62112", "62116", "62126", "65003", "65004", "65005", "65008", "65009", "65012", "65013", "65024", "65025", "41013", "41113", "41913":
			p.PrinterStatus.Normal = false
			if code == "40409" || code == "40309" || code == "60163" {
				p.PrinterStatus.Supplies.Toner = 3
			}
			if code == "10305" {
				p.PrinterStatus.Supplies.Drum = 2
			}
		case "62122", "62123", "62124", "62125", "62127", "11213", "11313", "11413", "11513", "11613":
			if tray == 1 || (tray == 2 && (code == "62123" || code == "11313")) || (tray == 3 && (code == "62124" || code == "11413")) || (tray == 4 && (code == "62125" || code == "11513")) || (tray == 5 && (code == "62127" || code == "11613")) {
				p.PrinterStatus.Supplies.Paper = 1
			}
		}
	}
	// 判断无纸
	subCode := string(code[0:3])
	fmt.Println(subCode)
	if ((subCode == "112" || subCode == "412") && tray == 1) ||
		((subCode == "113" || subCode == "413") && tray == 2) ||
		((subCode == "114" || subCode == "414") && tray == 3) ||
		subCode == "419" {
		p.PrinterStatus.Supplies.Paper = 2
		p.PrinterStatus.Normal = false
	}
	p.PrinterStatus.Infos[code] = DEVICE_STATUS[code] + value
	return true
}

// Printing online files
func (p *Printer) PrintFiles(print_type int, files map[string]File, f func(s string)) {
	obj := map[string]string{
		"action":   PRINTINGSTATUS,
		"detail":   "failed",
		"file":     "",
		"err":      "",
		"file_url": "",
	}
	var tc []byte
	errInf := make(map[string]string)
	failedFn := func(errKey, errValue string, killAndCancel bool) {
		p.isPrinting = false
		if killAndCancel {
			killOk, err := helpers.KillProcess("USBDISKPRINT.exe")
			if !killOk {
				log.Printer().Errorf("Kill USBDISKPRINT.exe  failed $v", err)
			}
		}
		obj["detail"] = "failed"
		if errKey != "" {
			errInf[errKey] = errValue
			e, _ := json.Marshal(errInf)
			obj["err"] = string(e)
		} else {
			obj["err"] = errValue
		}
		tc, _ = json.Marshal(obj)
		f(string(tc))
	}

	for _, file := range files {
		d := "1"
		if file.Duplex != 0 {
			d = strconv.Itoa(file.Duplex)
		}

		printlib := filepath.Join("vendor", "USBPrinter", "USBDISKPRINT.exe")
		per := "1"
		if file.PagesPerSheet > 0 {
			per = strconv.Itoa(file.PagesPerSheet)
		}

		libType := "3"
		switch print_type {
		default:
			return
		case PTNTYPEPDF:
			libType = "3"
		case PTNTYPEDISK:
			libType = "2"
		}

		args := []string{
			libType,
			file.Path,
			p.NormalName,
			strconv.Itoa(file.From),
			strconv.Itoa(file.To),
			strconv.Itoa(file.Copies),
			d,
			per,
			"1",
			"1",
		}
		time.Sleep(time.Second * 3)
		log.Printer().Infof("RunCMDWithTimeout:%s,%v", printlib, args)
		output, err, timeout := windows.RunCMDWithTimeout(printlib, args...)
		log.Printer().Infof("RunCMDResult:output<%v>,err<%v>,timeout<%v>", string(output), err, timeout)
		if err != nil {
			if timeout {
				log.Printer().Error("打印库超过10分钟没有返回结果")
				failedFn("2017_5590_002", "打印库超过10分钟没有返回结果", true)
				return
			}
			log.Printer().Infof("打印库返回结果：%s", string(output))
			log.Printer().WithFields(logrus.Fields{"URL": file.URL, "path": file.Path}).Errorf("文件打印失败：%v", err)
			obj["file"] = file.URL + "|" + file.Path
			failedFn("", fmt.Sprintf("%v", err), true)
			return
		}
		log.Printer().Infof("打印库返回结果：%s", string(output))
		log.Printer().WithFields(logrus.Fields{"URL": file.URL, "path": file.Path}).Info("文件发送打印机成功")
		res := new(Result)
		jsoniter.Unmarshal([]byte(output), res)
		if res.Result != "1" {
			log.Printer().Errorln("文件打印失败，" + res.Errcode + "，" + res.Errmsg)
			obj["file"] = file.URL + "|" + file.Path
			obj["file_url"] = file.URL
			failedFn("", res.Errmsg, true)
			return
		}
		p.isPrinting = true
	}
	// wait 3 second for status
	time.Sleep(3 * time.Second)
	log.Printer().Info("开始查询打印结果")
	obj["detail"] = "printing"
	tc, _ = json.Marshal(obj)
	f(string(tc))
	beginCheck := time.Now()
	for {
		sta := p.GetStatus(nil, true)
		if sta != nil {
			v, _ := json.Marshal(sta)
			log.Printer().Infof("当前打印机状态：%s", string(v))
			beginCheck = time.Now()
			if !sta.Normal {
				e, _ := json.Marshal(sta.Infos)
				failedFn("", string(e), true)
				return
			} else if sta.Detail != "printing" {
				obj["detail"] = "success"
				tc, _ = json.Marshal(obj)
				f(string(tc))
				p.isPrinting = false
				return
			}
		} else {
			if time.Since(beginCheck) > time.Minute*5 {
				failedFn("2017_5590_001", "打印机失联", true)
				return
			}
		}
		time.Sleep(3 * time.Second)
	}
}

func (p *Printer) CancelPrinting() {
	g.Default.Target = p.IPAddress
	g.Default.Community = "public"
	err := g.Default.Connect()
	var pdus []g.SnmpPDU
	pdu := g.SnmpPDU{
		Name:  CancelOID,
		Type:  g.OctetString,
		Value: "cancel",
	}
	pdus = append(pdus, pdu)
	if err != nil {
		p.PrinterStatus.Normal = false
		log.Printer().Errorf("打印任务取消失败：%v", err)
	} else {
		defer g.Default.Conn.Close()
		res, err := g.Default.Set(pdus)
		if err != nil {
			p.PrinterStatus.Normal = false
			log.Printer().Errorf("打印任务取消获取OID失败：%v", err)
		} else {
			for _, v := range res.Variables {
				fmt.Printf("oid: %s ", v.Name)
				switch v.Type {
				case g.OctetString:
					fmt.Printf("string: %s\n", string(v.Value.([]byte)))
				default:
					fmt.Printf("number: %d\n", g.ToBigInt(v.Value))
				}
			}
		}
	}
}

func hexdec(s string) uint64 {
	d := uint64(0)
	for i := 0; i < len(s); i++ {
		x := uint64(s[i])
		if x >= 'a' {
			x -= 'a' - 'A'
		}
		d1 := x - '0'
		if d1 > 9 {
			d1 = 10 + d1 - ('A' - '0')
		}
		if 0 > d1 || d1 > 15 {
			log.Printer().Info("hexdec")
		}
		d = (16 * d) + d1
	}
	return d
}
