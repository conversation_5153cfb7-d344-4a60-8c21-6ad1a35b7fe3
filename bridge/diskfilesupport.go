package bridge

import (
    "dora/windows"
    "fmt"
    "dora/logger"
    "github.com/json-iterator/go"
    "path/filepath"
    "os"
    "strings"
    "strconv"
    "dora/helpers"
    "github.com/satori/go.uuid"
    "crypto/md5"
    "time"
)

type DiskFile struct {
    File          string `json:"file"`
    From          int `json:"from,omitempty"`
    To            int `json:"to,omitempty"`
    Duplex        int `json:"duplex,omitempty"`
    Copies        int `json:"copies,omitempty"`
    PagesPerSheet int `json:"pages_per_sheet,omitempty"`
}

type DiskFileSupport struct {
    DiskFile DiskFile
    TempPath string
}

type Result struct {
    Result    string `json:"result,omitempty"`
    Errcode   string `json:"errcode,omitempty"`
    Errmsg    string  `json:"errmsg,omitempty"`
    FilePath  string `json:"filePath,omitempty"`
    PageCount int `json:"pageCount,omitempty"`
}

func (d *DiskFileSupport) SetFile(file DiskFile) {
    d.DiskFile.File = file.File
    d.DiskFile.From = file.From
    d.DiskFile.To = file.To
    d.DiskFile.Duplex = file.Duplex
}

var libpath = filepath.Join("vendor", "USBPrinter")

// GetPages get support file pages
func (d *DiskFileSupport) GetPages(f func(s string)) {
    obj := map[string]string{
        "action":    FILEPAGES,
        "detail":    "error",
        "err":       "",
        "file_path": "",
        "pages":     "0",
        "file_id":   "",
    }
    args := []string{
        "1",
        d.DiskFile.File,
        d.TempPath,
    }

    exe := filepath.Join(libpath, "USBDISKPRINT.exe")

    ret, err := windows.RunCMD(exe, args...)
    if err != nil {
        logger.App().Errorln(err)
        obj["err"] = fmt.Sprintf("%v", err)
    } else {
        fmt.Println(string(ret))
        res := new(Result)
        jsoniter.Unmarshal(ret, res)
        // 获取失败
        if res.Errcode != "" {
            logger.App().Errorln(res.Errmsg)
        } else {
            obj["detail"] = "success"
            obj["file_path"] = d.DiskFile.File

            if res.FilePath != "" {
                obj["file_path"], _ = filepath.Abs(res.FilePath)
            }
            obj["pages"] = strconv.Itoa(res.PageCount)
            id := strconv.Itoa(int(time.Now().UnixNano()))
            id = fmt.Sprintf("%X", md5.Sum([]byte(id)))
            obj["file_id"] = id

            // 判定文件是否已经拷贝过
            temp, _ := filepath.Abs(d.TempPath)
            dir, _ := filepath.Abs(filepath.Dir(obj["file_path"]))

            // 先把生成路径传过去
            // 生成新文件名
            n := uuid.NewV4().String()
            prefile := obj["file_path"]
            newfile := obj["file_path"]
            if !strings.Contains(dir, temp) {
                newfile = filepath.Join(d.TempPath, n+filepath.Ext(obj["file_path"]))
                // 异步拷贝
                go func() {
                    // 拷贝文件
                    // todo 用户取消时中断拷贝
                    logger.App().Info("需要拷贝文件：" + prefile)
                    copied := map[string]interface{}{
                        "action":  FILECOPIED,
                        "detail":  "",
                        "copied":  false,
                        "file_id": id,
                    }

                    _, err := os.Stat(prefile)
                    if err != nil {
                        copied["detail"] = fmt.Sprintf("%v", err)
                        logger.App().Infof("即将拷贝的文件读取失败：%v\n", err)
                    }

                    // 复制到临时目录
                    _,err = helpers.CopyFile(prefile, newfile)
                    if err != nil {
                        copied["detail"] = fmt.Sprintf("%v", err)
                        logger.App().Infof("文件拷贝失败：%v\n", err)
                    } else {
                        copied["copied"] = true
                        obj["file_path"] = newfile
                        d.DiskFile.File = prefile
                        logger.App().Println("文件已拷贝：" + prefile)
                    }
                    _b, _ := jsoniter.Marshal(copied)
                    fmt.Println(string(_b))
                    f(string(_b))
                }()
            } else {
                f("{\"action\":\"FILECOPIED\",\"detail\":\"\",\"copied\":true,\"file_id\":\"" + id + "\"}")
            }

            // 先传文件路径
            obj["file_path"] = newfile
        }
        b, _ := jsoniter.Marshal(obj)
        fmt.Println(string(b))
        f(string(b))
    }
}

func (d *DiskFileSupport) CheckFile() bool {
    if _, err := os.Stat(d.DiskFile.File); os.IsNotExist(err) {
        return false
    }
    return true
}
