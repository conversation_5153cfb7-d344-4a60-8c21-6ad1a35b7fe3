package bridge

import (
	"dora/common"
	"net/http"
	"os"

	"github.com/hprose/hprose-golang/rpc"
)

func version() string {
	return common.GetVersion(1, 1)
}

func api() string {
	return updateURL
}

func closeClient() {
	os.Exit(0)
}

var updateURL string

func OpenRPC(devs *Support) {
	updateURL = devs.Terminal.UpdateURL

	service := rpc.NewHTTPService()
	//service.AddFunction("version", version, rpc.Options{})
	//service.AddFunction("idle", idle, rpc.Options{})
	service.AddFunctions([]string{"version", "api", "close", "isPrinting"}, []interface{}{version, api, closeClient, func() bool { return devs.Printer.isPrinting }}, rpc.Options{})
	service.AddMethod("GetIdle", devs.Terminal, "idle")
	http.ListenAndServe(":9004", service)
}
