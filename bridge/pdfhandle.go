package bridge

import (
    log "dora/logger"
    "dora/components/pdfdoc/pdf"
    "os"
    "strconv"
    "path/filepath"
)

func (file *File) FileHandle() (outfile string, err error) {
    outfile = ""
    err = nil
    path := file.Path
    pw := pdf.NewPdfWriter()
    f, err := os.Open(path)
    //f, err := os.Open(outfile)
    log.App().Infof("文件[%s]处理开始", path)
    if err != nil {
        log.App().Errorf("open pdf file failed: %v", err)
        return
    }
    defer f.Close()

    pr, err := pdf.NewPdfReader(f)
    if err != nil {
        log.App().<PERSON><PERSON><PERSON>("read pdf failed: %v", err)
        return
    }

    isEncrypted, err := pr.IsEncrypted()
    if err != nil {
        log.App().Errorf("check pdf failed: %v", err)
        return
    }
    if isEncrypted {
        _, err = pr.Decrypt([]byte(""))
        if err != nil {
            log.App().Errorf("decode failed: %v", err)
            return
        }
    }

    pages, err := pr.GetNumPages()
    if err != nil {
        log.App().Errorf("get pdf pages failed: %v", err)
        return
    }

    to := file.To
    if pages < to {
        log.App().Errorf("to (%d) < pdf total pages (%d)", to, pages)
        return
    }

    from := file.From
    for i := from; i <= to; i++ {
        n := i
        page, err := pr.GetPage(n)
        if err != nil {
            log.App().Errorf("get pdf page (%d) failed: %v", n, err)
            return "", err
        }

        err = pw.AddPage(page)
        if err != nil {
            log.App().Errorf("add page to new file failed: %v", err)
            return "", err
        }
    }

    outfile = filepath.Dir(path) + string(os.PathSeparator) + strconv.Itoa(from) + "_" + strconv.Itoa(to) + filepath.Base(path) + "." + filepath.Ext(path)
    fw, err := os.Create(outfile)
    if err != nil {
        outfile = ""
        log.App().Errorf("write pdf stream failed: %v", err)
        return
    }
    defer fw.Close()

    err = pw.Write(fw)
    if err != nil {
        log.App().Errorf("write pdf file failed: %v", err)
        return
    }
    log.App().Infof("文件[%s]处理结束", path)
    return
}
