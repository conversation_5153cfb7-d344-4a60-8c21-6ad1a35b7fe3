package bridge

import (
	"dora/components/serial"
	log "dora/logger"
	"strings"
	"encoding/json"
	"fmt"
	"dora/helpers"
	"strconv"
)

// Barcode Commands
var (
	BarcodeStartScan  = []byte{0x16, 'T', 0x0d}
	BarcodeCancelScan = []byte{0x16, 'U', 0x0d}
	BarcodeSetPAPHHC  = []byte{0x16, 'M', 0x0d, 'P', 'A', 'P', 'H', 'H', 'C', 0x2e}
	BarcodeStatus     = []byte{0x16, 'M', 0x0d, 'B', 'E', 'P', 'L', 'V', 'L', '1', 0x2e}
)

var (
	out  chan string
	quit chan bool
)

// BarCode 扫描头
type BarCode struct {
	PortName string `ini:"PortName" conf:"PortName"`
	BaudRate string `ini:"BaudRate" conf:"BaudRate"`
	TimeOut  string `ini:"TimeOut" conf:"TimeOut"`
	IsOpen   bool
	Reading  bool
	Exist    bool
	port     *serial.Port
}

// Open 打开扫描头
func (b *BarCode) Open(f func(s string)) bool {
	obj := map[string]string{
		"action": "NOTIFY",
		"device": "barcode",
		"detail": "unknown",
	}
	var err error
	baud, err := strconv.Atoi(b.BaudRate)
	if err != nil {
		baud = 115200
	}
	c := &serial.Config{Name: b.PortName, Baud: baud}
	b.port, err = serial.OpenPort(c)
	if err != nil {
		log.Barcode().Errorf("扫描头端口打开失败：%v", err)
	} else {
		b.IsOpen = true
		b.Exist = true
		obj["detail"] = "exist"
	}
	tc, err := json.Marshal(obj)
	if err != nil {
		log.Barcode().Errorf("%s 转码失败：%v", "barcode opened", err)
	}
	f(string(tc))
	return b.IsOpen
}

// Close 关闭扫描头
func (b *BarCode) Close() bool {
	err := b.port.Close()
	if err != nil {
		log.Barcode().Errorf("扫描头关闭失败：%v", err)
	}
	return err == nil
}

//// SetPAPHHC 设置扫描头手机模式
//func (b *BarCode) SetPAPHHC(conf *Conf) bool {
//    if b.write(BarcodeSetPAPHHC) {
//        // 更新配置文档
//        b.IsPAPHHC = true
//        bc := &confBarcode{b}
//        err := ini.ReflectFrom(conf.File, bc)
//        _err := conf.File.SaveTo(conf.Path)
//        if err != nil || _err != nil {
//            return false
//            log.Barcode().Errorf("配置文件报错失败：%v | %v", err, _err)
//        }
//        return true
//    }
//    return false
//}

// Status 获取状态
func (b *BarCode) Status(f func(s string)) {
	if !b.Reading {
		if b.write(BarcodeStatus) {
			// 异步读取信息
			b.read(2, f)
		}
	}
}

// StartScan 开始扫描
func (b *BarCode) StartScan(f func(s string)) bool {
	if !b.IsOpen {
		return false
	}
	fmt.Println("StartScan reading", b.Reading)
	if !b.Reading {
		if b.write(BarcodeStartScan) {
			// 异步读取信息
			b.read(1, f)
		}
	}
	return false
}

// CancelScan 取消扫码
func (b *BarCode) CancelScan() {
	if b.Reading {
		canceled := b.write(BarcodeCancelScan)
		fmt.Println("canceled", canceled)
		if canceled {
			b.Reading = false
			fmt.Println("reading", b.Reading)
			quit <- true
		}
	}
}

func (b *BarCode) write(c []byte) bool {
	n, err := b.port.Write(c)
	if err != nil {
		log.Barcode().Errorf("命令执行失败：", err)
		return false
	}
	log.Barcode().Infof("发送指令[%d字节]：% #x", n, c)
	return true
}

// op 1是扫码操作 2是状态操作
func (b *BarCode) read(op int, f func(s string)) {
	out = make(chan string)
	quit = make(chan bool)
	b.Reading = true
	t := BARCODESTATUS
	timeout := 3
	ret := "unknown"
	if op == 1 {
		ret = ""
		t = BARCODESCAN
		timeout, _ = strconv.Atoi(b.TimeOut)
	}
	fmt.Println(timeout)
	log.Debugf("%s 数据中...", t)
	obj := make(map[string]string)
	obj = map[string]string{
		"action": t,
		"detail": ret,
	}

	go func() {
		buf := make([]byte, 1024)
		n, err := b.port.Read(buf)
		if err != nil {
			log.Barcode().Errorf("数据获取失败：%v", err)
			quit <- true
		} else {
			s := string(buf[:n])
			s = strings.Replace(s, "\r", "", -1)
			s = strings.Replace(s, "\n", "", -1)
			s = strings.Replace(s, "\r\n", "", -1)
			log.Barcode().Infof("获取数据成功[%d字节]：%s", n, s)
			if s == helpers.TASKBARCOMMAND {
				helpers.SwitchTaskbar()
			}
			out <- s
		}
	}()

	select {
	case obj["detail"] = <-out:
		if op == 2 {
			obj["detail"] = "normal"
		}
	case <-quit:
		return
		//case <-time.After(time.Second * time.Duration(timeout)):
		//    if op == 2 {
		//        tc, e := json.Marshal(obj)
		//        if e != nil {
		//            log.Barcode().Errorf("%s 转码失败：%v", t, e)
		//        } else {
		//            // 发送给终端
		//            f(string(tc))
		//        }
		//    } else {
		//        log.Barcode().Info("读取超时")
		//        b.CancelScan()
		//        return
		//    }
	}
	b.Reading = false
	if op > 0 {
		tc, e := json.Marshal(obj)
		if e != nil {
			log.Barcode().Errorf("%s 转码失败：%v", t, e)
		}
		// 发送给终端
		f(string(tc))
	}
}
