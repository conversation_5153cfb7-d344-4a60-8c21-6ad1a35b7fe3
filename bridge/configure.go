package bridge

import (
	"dora/logger"
	"dora/windows"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"golang.org/x/sys/windows/registry"
	"path/filepath"
	"dora/helpers"
)

const (
	RegistryKey  = registry.CURRENT_USER
	RegistryPath = `Software\Dora`
)

// 使用默认配置读取注册表
// conver 是否强制覆盖注册表项，首参不携带或者不为conver为不覆盖
func (c *Support) ConfWithDefaults(args ...string) {
	cover := false
	if len(args) > 0 {
		cover = args[0] == "cover"
	}

	v := reflect.ValueOf(c).Elem()

	for i := 0; i < v.NumField(); i++ {
		_tag := v.Type().Field(i).Tag.Get("conf")
		if _tag != "" && _tag != "-" {
			if v.Field(i).Type().Kind() == reflect.Ptr {
				block := reflect.Indirect(v.Field(i)).Type().Name()

				blockvalue := reflect.Indirect(v.Field(i))

				fmt.Println("\nblock -------->", block, "| sons ->", blockvalue.NumField())

				path := RegistryPath + "\\" + block
				key, _, err := registry.CreateKey(RegistryKey, path, registry.ALL_ACCESS)
				defer key.Close()
				if err != nil {
					logger.App().Fatalf("注册表读取或者创建失败：%v", err)
				}

				for n := 0; n < blockvalue.NumField(); n++ {
					// get tag of configure
					tag := blockvalue.Type().Field(n).Tag.Get("conf")
					// check value and typeof value
					if tag != "-" && tag != "" && blockvalue.Field(n).Type().Kind() == reflect.String {
						name := blockvalue.Type().Field(n).Name
						value := blockvalue.Field(n).String()
						fmt.Println("name ->", name, "|", "type ->", blockvalue.Field(n).Type().Name(), "|", "value ->", value, "|", "tag ->", tag)
						_cover := false

						// 打印机IP
						if name == "IPAddress" {
							ip := GetPrinterIP()
							if value != ip && ip != "" {
								_cover = true
								value = ip
							}
						}

						if name == "TimeOut" {
							timeout, _ := strconv.Atoi(value)
							if timeout < 5 || timeout > 25 {
								value = "20"
								_cover = true
							}
						}

						// read or create conf from registry to lcoal, and use registry value
						regvalue, err := ReadOrCreateValue(key, name, value)
						if err != nil {
							logger.App().Fatalf("注册表值读取失败：%v", err)
						}
						
						if name == "UpdateURL" && !strings.Contains(regvalue, "dora-terminal.oss") {
							value = "http://dora-terminal.oss-cn-zhangjiakou.aliyuncs.com/"
							_cover = true
						}

						if name == "LastModified" && regvalue != value {
							_cover = true
						}

						// if registry value is different with local value
						if regvalue != value && (cover || _cover) {
							// write local value to registry
							key.SetStringValue(name, value)
							regvalue = value
						}
						if blockvalue.Field(n).CanSet() {
							// set new value from registry and use local value
							blockvalue.Field(n).SetString(regvalue)
						}
					}
				}

			} else {
				// todo has none value to registry, if had, just write read and write value to registry path root ... ~ ...
				fmt.Println("name ->", v.Type().Field(i).Name, "|", "value type ->", v.Field(i).Type().Name(), "|", "value ->", v.Field(i).Interface())
			}
		}
	}
	go closeWindowsUpdate()
}

func closeWindowsUpdate() {
	logger.App().Info("start to close windows update")
	for {
		base, _ := os.Getwd()
		filePath := filepath.Join(base, "vendor", "BatFiles", "CloseWindowsUpdate.bat")
		logger.App().Infof("path:%s", filePath)
		if helpers.FileExist(filePath) {
			logger.App().Info("find CloseWindowsUpdate.bat,start to close windows update")
			args := []string{}
			cmdStr := filepath.Join("vendor", "BatFiles", "CloseWindowsUpdate.bat")
			windows.RunCMD(cmdStr, args...)
			break
		}
		logger.App().Info("didn't find CloseWindowsUpdate.bat")
		time.Sleep(time.Second * 5)
	}
}

// read or create string value from registy key
func ReadOrCreateValue(key registry.Key, name, value string) (value_ string, err error) {
	value_, _, err = key.GetStringValue(name)
	if os.IsNotExist(err) {
		err = key.SetStringValue(name, value)
		if err != nil {
			return
		}
		value_ = value
		return
	} else {
		return
	}
}

// 读取打印机IP
func GetPrinterIP() (ip string) {
	b, _ := windows.RunCMD("cmd", "/C", "arp", "-a")
	s := string(b)
	f := strings.FieldsFunc(s, func(i rune) bool {
		return i == '\r' || i == '\n'
	})
	hc := http.Client{Timeout: 3 * time.Second}
	for _, v := range f {
		if len(v) > len("ff-ff-ff-ff-ff-ff") {
			p := strings.FieldsFunc(v, func(i rune) bool {
				return i == ' ' || i == '\t'
			})
			if len(p) == 3 {
				if p[1] == "ff-ff-ff-ff-ff-ff" {
					break
				}
				// 获取页面
				resp, err := hc.Get("http://" + p[0])
				if err == nil {
					defer resp.Body.Close()
					a, _ := ioutil.ReadAll(resp.Body)
					isPrinter := strings.Contains(string(a), "Brother HL-5590DN")
					if isPrinter {
						resp.Body.Close()

						sip := "************"
						if SetPrinterIP(sip, p[0]) {
							return sip
						}
						return p[0]
					}
				}
			}
		}
	}
	return
}

// ip 新IP
// oip 原始IP
func SetPrinterIP(ip, oip string) bool {
	u := fmt.Sprintf(`http://%s/net/wired/tcpip.html`, oip)
	resp, _ := http.Get(u)
	defer resp.Body.Close()

	dom, _ := goquery.NewDocument(u)
	ctoken, _ := dom.Find("#CSRFToken1").Attr("value")
	pageid, _ := dom.Find("#pageid").Attr("value")
	transition, _ := dom.Find("#transition").Attr("value")
	//B475, _ := dom.Find("#B475").Attr("value")
	//B476, _ := dom.Find("#B476").Attr("value")
	//B477, _ := dom.Find("#B477").Attr("value")
	B478, _ := dom.Find("#B478").Attr("value")
	if B478 != "7" {
		B479, _ := dom.Find("#B479").Attr("value")
		B474, _ := dom.Find("#B474").Attr("value")

		form := url.Values{}
		form.Add("CSRFToken", ctoken)
		form.Add("pageid", pageid)
		form.Add("pageid", transition)
		form.Add("B475", ip)
		form.Add("B476", "*************")
		form.Add("B477", "***********")
		form.Add("B478", "7")
		form.Add("B479", B479)
		form.Add("B474", B474)

		//res, err := http.NewRequest("POST", u, strings.NewReader(form.Encode()))

		res, err := http.PostForm(u, form)

		if err != nil {
			fmt.Println("error")
			fmt.Println(err)
			logger.App().Errorf("设置打印机IP失败：%v", err)
			return false
		}
		defer res.Body.Close()

		f, _ := os.Create("res.html")
		b, _ := ioutil.ReadAll(res.Body)

		f.Write(b)
		f.Close()

		reg := regexp.MustCompile(`var argUrl='http://(.*)/';`)
		rf := reg.FindStringSubmatch(string(b))
		if len(rf) > 1 && rf[1] == ip {
			return true
		}
	}
	return false
}
