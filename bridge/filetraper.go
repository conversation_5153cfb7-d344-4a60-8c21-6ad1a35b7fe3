package bridge

import (
	"dora/helpers"
	"dora/logger"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"time"

	"github.com/json-iterator/go"
)

type Trapper struct {
	Path         string `ini:"Path" conf:"Path"`
	TimeLoop     string `ini:"TimeLoop" conf:"TimeLoop"`
	LastModified string `ini:"LastModified" conf:"LastModified"`
	Retry        string `conf:"RetryDownload"` // 重试下载次数
	RetryTime    string `conf:"RetryTime"`     // 下载重试访问间隔 (s)
}

type proc struct {
	Action     string  `json:"action"`
	Detail     string  `json:"detail"`
	Progress   float32 `json:"progress"`
	Total      int64   `json:"total"`
	Size       int64   `json:"size"`
	RetryTimes int     `json:"retry_times"`
	Error      string  `json:"error"`
	Errcode    int     `json:"errcode"`
	FileUrl    string  `json:"file_url"`
	File       string  `json:"file"`
	OriginUrl  string  `json:"origin_url"`
}

type File struct {
	URL           string  `json:"url,omitempty"`
	File          string  `json:"file,omitempty"`
	Copies        int     `json:"copies,omitempty"`
	Duplex        int     `json:"duplex,omitempty"`
	PagesPerSheet int     `json:"pages_per_sheet,omitempty"`
	From          int     `json:"from,omitempty"`
	To            int     `json:"to,omitempty"`
	Path          string  `json:"path,omitempty"`
	Speed         float64 `json:"speed,omitempty"`
	Size          int64   `json:"size,omitempty"`
	Complete      int64   `json:"complete,omitempty"`
	Progess       float64 `json:"progess,omitempty"`
	Eta           int64   `json:"eta,omitempty"`
}

var (
	PCFreeD int
	PCFreeC int
)

func (t *Trapper) RemoveFiles() {
	d, err := strconv.ParseInt(t.LastModified, 10, 64)
	if err != nil {
		d = 60
	}
	checkDisk()
	ticker := time.NewTicker(time.Minute * time.Duration(d))
	for {
		select {
		case <-ticker.C:
			deleteAll := false
			checkDisk()
			if PCFreeD < 3000 {
				deleteAll = true
				logger.App().Warn("检测到当前D盘剩余空间已经低于3G，开始清理temp文件夹")
			}
			files, _ := ioutil.ReadDir(t.Path)
			for _, f := range files {
				if deleteAll {
					os.Remove(t.Path + string(os.PathSeparator) + f.Name())
				} else {
					if int64(time.Now().Unix()-f.ModTime().Unix()) > 604800 {
						os.Remove(t.Path + string(os.PathSeparator) + f.Name())
					}
				}
			}
		}
	}
}

func checkDisk() {
	PCFreeC = helpers.GetFreeSpaceOfDisk("C:")
	logger.App().Infof("当前C盘剩余空间:%dM", PCFreeC)
	PCFreeD = helpers.GetFreeSpaceOfDisk("D:")
	logger.App().Infof("当前D盘剩余空间:%dM", PCFreeD)
}

// PrintDownloadPercent send progress message to client
func PrintDownloadPercent(done chan int64, path string, total int64, f func(s string)) {
	for {
		select {
		case <-done:
			return
		default:
			file, err := os.Open(path)
			if err != nil {
				logger.App().Errorf("文件下载失败 - 打开错误\t%s：%v", err, path)
			}
			fi, err := file.Stat()
			if err != nil {
				logger.App().Errorf("文件下载失败 - 打开错误\t%s：%v", err, path)
			}
			size := fi.Size()
			if size == 0 {
				size = 1
			}
			percent := float32(size) / float32(total) * 100
			p.Detail = "downloading"
			p.Errcode = ErrNone
			p.Error = ""
			p.Size = size
			p.Total = total
			p.Progress = percent
			fmt.Printf("%.02f%%\n", percent)
			fs, _ := jsoniter.Marshal(p)
			f(string(fs))
			if size == total {
				p.Size = p.Total
				p.Detail = "success"
				p.Progress = 100
				fs, _ := jsoniter.Marshal(p)
				f(string(fs))
			}
		}

		time.Sleep(time.Second)
	}
}

var start = time.Now()
var p *proc

// Download file
func (t *Trapper) Download(url string, retrytimes int, f func(s string)) {
	p = &proc{
		Action:    FILEDOWNLOAD,
		Detail:    "error",
		Total:     0,
		Size:      0,
		Progress:  0,
		Error:     "",
		Errcode:   ErrFetch,
		OriginUrl: url,
	}

	r, _ := strconv.Atoi(t.Retry)
	if r < 1 {
		r = 3
	}
	rt, _ := strconv.Atoi(t.RetryTime)
	if rt < 1 {
		rt = 5
	}
	if retrytimes > 0 {
		time.Sleep(time.Duration(rt) * time.Second)
	} else {
		start = time.Now()
	}
	retrytimes++

	file := path.Base(url)
	fileP := filepath.Join(t.Path, file)

	if helpers.FileExistAndNormal(file, fileP) {
		logger.App().Infof("本地已存在的文件,跳过下载,Path:%s,URL:%s", fileP, url)
		p.File = fileP
		p.FileUrl = "http://127.0.0.1:4002/" + file
		p.Error = ""
		p.Errcode = ErrNone
		p.Detail = "success"
		p.Progress = 100
		fs, _ := jsoniter.Marshal(p)
		f(string(fs))
		return
	}

	headResp, err := http.Head(url)
	if err != nil {
		logger.App().Errorf("文件下载失败 - 网络故障：%v", err)
		if retrytimes < r {
			t.Download(url, retrytimes, f)
		} else {
			p.Error = "网络故障"
			p.Errcode = ErrNetWork
			p.Detail = "error"
			fs, _ := jsoniter.Marshal(p)
			f(string(fs))
		}
		return
	}

	defer headResp.Body.Close()
	// 判断文件是否存在
	if headResp.StatusCode != 200 {
		logger.App().Errorf("文件下载失败 - 文件不存在：%v\t%s", headResp.StatusCode, url)
		p.Error = "网络文件不存在"
		p.Errcode = ErrNotExist
		p.Detail = "error"
		fs, _ := jsoniter.Marshal(p)
		f(string(fs))
		return
	}

	cl := headResp.Header.Get("Content-Length")
	size, err := strconv.Atoi(cl)
	fmt.Println(size)
	if err != nil {
		logger.App().Errorf("文件下载失败 - 获取失败：%v\t%s", headResp.StatusCode, url)
		if retrytimes < r {
			t.Download(url, retrytimes, f)
		} else {
			p.Error = "获取失败"
			p.Detail = "error"
			fs, _ := jsoniter.Marshal(p)
			f(string(fs))
		}
		return
	}

	p.File = filepath.Join(t.Path, file)
	p.FileUrl = "http://127.0.0.1:4002/" + file

	out, err := os.Create(fileP)
	if err != nil {
		if retrytimes < r {
			t.Download(url, retrytimes, f)
		} else {
			p.Errcode = ErrOpen
			p.Error = "文件打开失败"
			p.Detail = "error"
			fs, _ := jsoniter.Marshal(p)
			f(string(fs))
		}
		return
	}
	defer out.Close()

	//done := make(chan int64)

	//go PrintDownloadPercent(done, fpath.String(), int64(size), f)

	client := &http.Client{Timeout:time.Second*55}
	resp, err := client.Get(url)

	if err != nil {
		logger.App().Errorf("文件下载失败 - 网络故障：%v", err)
		if retrytimes < r {
			t.Download(url, retrytimes, f)
		} else {
			p.Error = "网络故障"
			p.Detail = "error"
			p.Errcode = ErrNetWork
			fs, _ := jsoniter.Marshal(p)
			f(string(fs))
		}
		return
	}
	defer resp.Body.Close()

	n, err := io.Copy(out, resp.Body)
	logger.App().Infof("下载字节数：%v", n)

	if err != nil {
		//done <- n
		logger.App().Errorf("文件保存失败 - 系统故障：%v", err)
		if retrytimes < r {
			t.Download(url, retrytimes, f)
		} else {
			p.Error = "系统故障"
			p.Detail = "error"
			p.Errcode = ErrSystem
			fs, _ := jsoniter.Marshal(p)
			f(string(fs))
		}
		return
	}

	// todo 有可能被阻塞
	//done <- n

	p.Error = ""
	p.Errcode = ErrNone
	p.Size = p.Total
	p.Detail = "success"
	p.Progress = 100
	fs, _ := jsoniter.Marshal(p)
	f(string(fs))

	elapsed := time.Since(start)
	logger.App().Printf("文件下载完成，用时：%s", elapsed)
}
