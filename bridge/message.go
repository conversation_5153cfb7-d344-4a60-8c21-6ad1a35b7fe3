package bridge

import (
	"dora/components/browser"
	"dora/logger"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"net"
	"strings"
)

// App actions
const (
	TERMINALIDLE = "TERMINALIDLE"
	VERSION      = "VERSION"
	REBOOT       = "REBOOT"
	MACINFO      = "MACINFO"
)

// Barcode actions
const (
	BARCODESCAN   = "BARCODESCAN"   // 扫码
	BARCODECANCEL = "BARCODECANCEL" // 取消扫码
	BARCODESTATUS = "BARCODESTATUS" // 获取扫描头状态
)

// A4 printer actions
const (
	PRINTERSTATUS   = "PRINTERSTATUS"   // 获取打印机状态
	PRINTERPRINTING = "PRINTERPRINTING" // 打印文件
	PRINTINGSTATUS  = "PRINTINGSTATUS"  // 文件打印状态
)

// File actions
const (
	FILEDOWNLOAD = "FILEDOWNLOAD" // 文件下载
	FILEPAGES    = "FILEPAGES"    // 文件页数
	FILECOPIED   = "FILECOPIED"   // 文件拷贝
)

// USB actions
const (
	USBDETECTOR = "USBDETECTOR" // USB检测
	USBEJECT    = "USBEJECT"    // 移除USB设备
	USBEJECTED  = "USBEJECTED"  // 成功移除所有USB设备
)

// printer actions
const (
	DISKPRINTING = "DISKPRINTING" // 本地文件打印
)

var usbDetectorOut = make(chan bool)

// Message struct
type Message struct {
	Action    string   `json:"action"`
	URLs      []string `json:"urls,omitempty"`
	URL       string   `json:"url"`
	Detail    string   `json:"detail,omitempty"`
	PrintData []File   `json:"print_data,omitempty"`
	DiskFile  DiskFile `json:"disk_file,omitempty"`
}

// Parse terinmal's command
func Parse(msg string, devs *Support, w *liasicaectron.Window) (m *Message, err error) {
	m = new(Message)
	err = json.Unmarshal([]byte(msg), m)
	if err != nil {
		logger.App().Errorf("消息解析失败：%v", err)
		return
	}
	devs.Terminal.Idle = false
	switch m.Action {
	default:
		emsg := "未知指令：" + m.Action
		logger.App().Error(emsg)
		return m, errors.New(emsg)
	case MACINFO:
		if interfaces, err := net.Interfaces(); err == nil {
			for _, inter := range interfaces {
				if strings.Contains(inter.Name, "本地连接") || strings.Contains(inter.Name, "以太网") || strings.Contains(inter.Name, "WLAN") {
					mac := inter.HardwareAddr //获取本机MAC地址
					w.Send(fmt.Sprintf(`{"action":"MACINFO","detail":"%s"}`, mac))
					break
				}
			}
		} else {
			logger.App().Errorf("Get MacInfo Error:%v", err)
		}
	case BARCODESCAN:
		fmt.Println(devs.BarCode.Reading)
		go devs.BarCode.StartScan(func(s string) {
			err := w.Send(s)
			if err != nil {
				logger.Barcode().Errorf("扫码失败：%v", err)
			}
		})
	case BARCODESTATUS:
		if devs.BarCode.IsOpen {
			go devs.BarCode.Status(func(s string) {
				// 发送给终端状态
				w.Send(s)
			})
		}
	case BARCODECANCEL:
		fmt.Println(devs.BarCode.Reading)
		if devs.BarCode.Reading {
			devs.BarCode.CancelScan()
		}
	case FILEDOWNLOAD:
		if len(m.URL) < 10 {
			obj := map[string]string{
				"action": PRINTERPRINTING,
				"detail": "failed",
				"err":    "文件为空",
			}
			tc, _ := json.Marshal(obj)
			w.Send(string(tc))
			return
		}
		devs.File.Download(m.URL, 0, func(s string) {
			w.Send(s)
		})
	case PRINTERPRINTING:
		devs.DiskFileSupport.DiskFile = m.DiskFile
		// 先判定文件是否满足打印需求
		if !devs.DiskFileSupport.CheckFile() {
			w.Send(`{"action":"PRINTINGSTATUS","detail":"failed","err":"文件不存在"}`)
			return
		}
		if devs.UsbDetector.IsPrinting || devs.Printer.isPrinting {
			logger.App().Errorln("打印指令正在进行中，丢弃当前指令")
			w.Send(`{"action":"PRINTINGSTATUS","detail":"failed","err":"上次打印尚未完成"}`)
		} else {
			files := map[string]File{
				"diskfile": {
					Path:          m.DiskFile.File,
					From:          m.DiskFile.From,
					To:            m.DiskFile.To,
					Copies:        m.DiskFile.Copies,
					Duplex:        m.DiskFile.Duplex,
					PagesPerSheet: m.DiskFile.PagesPerSheet,
				},
			}
			go devs.Printer.PrintFiles(PTNTYPEPDF, files, func(s string) {
				w.Send(s)
			})
		}
	case PRINTERSTATUS:
		go devs.Printer.GetStatus(w, false)
	case TERMINALIDLE:
		devs.Terminal.Idle = m.Detail == "idle"
	case USBDETECTOR:
		if devs.UsbDetector.IsPrinting || devs.Printer.isPrinting {
			w.Send(`{"action":"USBDETECTOR","detail":"","got":0}`)
		} else {
			// 检测USB设备
			if !devs.UsbDetector.Scanning {
				devs.UsbDetector.Scan(func(s string) {
					w.Send(s)
				})
			} else {
				devs.UsbDetector.ReStartCh <- true
				logger.App().Errorln("检测指令正在进行中，丢弃当前指令")
			}
		}
	case USBEJECT:
		// 推出所有U盘
		go devs.UsbDetector.Eject(func(s string) {
			w.Send(s)
		})
	case DISKPRINTING:
		devs.DiskFileSupport.DiskFile = m.DiskFile
		// 先判定文件是否满足打印需求
		if !devs.DiskFileSupport.CheckFile() {
			w.Send(`{"action":"PRINTINGSTATUS","detail":"failed","err":"文件不存在"}`)
			return
		}
		// 若已经开始打印流程则忽略指令
		if devs.UsbDetector.IsPrinting || devs.Printer.isPrinting {
			logger.App().Errorln("打印指令正在进行中，丢弃当前指令")
			w.Send(`{"action":"PRINTINGSTATUS","detail":"failed","err":"上次打印尚未完成"}`)
		} else {
			devs.UsbDetector.IsPrinting = true
			devs.UsbDetector.StopToPrint(func(s string) { w.Send(s) })
			files := map[string]File{
				"diskfile": {
					Path:          m.DiskFile.File,
					From:          m.DiskFile.From,
					To:            m.DiskFile.To,
					Copies:        m.DiskFile.Copies,
					Duplex:        m.DiskFile.Duplex,
					PagesPerSheet: m.DiskFile.PagesPerSheet,
				},
			}
			go devs.Printer.PrintFiles(PTNTYPEDISK, files, func(s string) {
				w.Send(s)
				devs.UsbDetector.IsPrinting = false
			})
		}
	case FILEPAGES:
		devs.DiskFileSupport.DiskFile = m.DiskFile
		// todo 判断磁盘容量是否满足, 若不满足则清空临时文件
		// 判断文件是否存在
		if !devs.DiskFileSupport.CheckFile() {
			obj := map[string]string{
				"action": FILEPAGES,
				"detail": "failed",
				"err":    "文件不存在",
			}
			tc, _ := json.Marshal(obj)
			w.Send(string(tc))
			return
		}

		// 获取页数
		go devs.DiskFileSupport.GetPages(func(s string) {
			w.Send(s)
		})
	case VERSION:
		w.Send(`{"action":"` + VERSION + `","version":"` + version() + `"}`)
	case REBOOT:
		// 重启
		logger.App().Info("收到重启指令，开始重启电脑")
		//args := []string{"/r", "/f", "/t", "0"}
		//exec.Command("shutdown", args...).Run()
		w.Send(`{"action":"REBOOT"}`)
	}
	return m, err
}
