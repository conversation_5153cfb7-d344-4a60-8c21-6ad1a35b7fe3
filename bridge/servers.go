package bridge

import (
    "strings"
    "fmt"
    "path/filepath"
    "os"
    "bufio"
    "net/http"
)

type HttpHandler struct {
    http.Handler
}

func ContentType(path string) (contentType string) {
    if strings.HasSuffix(path, ".css") {
        contentType = "text/css"
    } else if strings.HasSuffix(path, ".html") {
        contentType = "text/html"
    } else if strings.HasSuffix(path, ".js") {
        contentType = "application/javascript"
    } else if strings.HasSuffix(path, ".png") {
        contentType = "image/png"
    } else {
        contentType = "text/plain"
    }
    return
}

// We create a custom handler
func (h *HttpHandler) ServeHTTP(w http.ResponseWriter, req *http.Request) {
    fmt.Println(req.URL.Path)
    pathCom := []string{`D:\temp`}
    pathCom = append(pathCom, strings.Split(req.URL.Path, `\`)...)
    path := filepath.Join(pathCom...)
    fmt.Println(path)
    // Open a file, which does not render it but keep it ready for read
    f, err := os.Open(path)
    defer f.Close()

    // if a file exists, check its content type, or return 404
    if err == nil {
        // read the content to buffer in order to save memory
        bufferedReader := bufio.NewReader(f)
        // check content type of the file according to its suffix
        //w.Header().Add("Content Type", ContentType(path))
        w.Header().Add("Access-Control-Allow-Origin", "*")
        // write the file content to the response
        bufferedReader.WriteTo(w)
    } else {
        w.WriteHeader(404)
        w.Write([]byte("404 - " + http.StatusText(404)))
    }
}
