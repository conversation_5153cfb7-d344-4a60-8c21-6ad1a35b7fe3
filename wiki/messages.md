# 指令说明

### 错误代码
- 0     无错误
- 16001 网络错误
- 16002 文件不存在
- 16003 获取失败
- 16004 系统故障
- 16005 打开错误

### 下载文件
- `total` 文件总大小`byte`
- `size` 当前下载大小
- `retry_times` 重试次数
- `error` 错误描述
- `errcode` 错误代码
- `file_url` 供给前端使用的url连接
- `file` 供给打印使用的file path
- `origin_url` 原始下载连接

```json
{"action":"FILEDOWNLOAD","detail":"","url":"https://wordpress.org/wordpress-4.4.2.zip"}
```

返回示例 - 正在下载
```json
{
    "action": "FILEDOWNLOAD",
    "detail": "downloading",
    "progress": 14.207813,
    "total": 7726228,
    "size": 1097728,
    "retry_times": 0,
    "error": "",
    "errcode": 0,
    "file_url": "http://127.0.0.1:4002/wordpress-4.4.2.zip",
    "file": "D:\\temp\\wordpress-4.4.2.zip",
    "origin_url": "http://wordpress.org/wordpress-4.4.2.zip"
}
```

返回示例 - 下载成功
```json
{
    "action": "FILEDOWNLOAD",
    "detail": "success",
    "progress": 100,
    "total": 7726228,
    "size": 7726228,
    "retry_times": 0,
    "error": "",
    "errcode": 0,
    "file_url": "http://127.0.0.1:4002/wordpress-4.4.2.zip",
    "file": "D:\\temp\\wordpress-4.4.2.zip",
    "origin_url": "http://wordpress.org/wordpress-4.4.2.zip"
}
```

返回示例 - 下载失败
```json
{
    "action": "FILEDOWNLOAD",
    "detail": "error",
    "progress": 0,
    "total": 0,
    "size": 0,
    "retry_times": 0,
    "error": "网络故障",
    "errcode": 16001,
    "file_url": "",
    "file": "",
    "origin_url": "http://wordpressxxxxxxxxxx.org/wordpress-4.4.2.zip"
}
```

<br>
<br>
<br>

### 打印文件
> 返回和之前一样
> 建议重新处理一下进度条

```json
{"action":"PRINTERPRINTING","disk_file":{"file":"D:\\temp\\test.pdf","copies":1,"duplex":false,"from":2,"to":2}}
```