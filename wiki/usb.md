# USB指令说明
- USB打印流程：需要先`查询文件页数`，成功返回的参数中`file_path`为实际打印需要发送的文件
- USB监控说明：`DISKPRINTING`：是否已开始打印流程。当用户获取USB目录之后会自动监控U盘是否推出，当用户未开始打印流程的时候若U盘推出会收到指令`{"action":"USBEJECT","detail":true,"DISKPRINTING":false}`，当用户已开始打印流程的时候见`USB打印`说明

### 获取USB文件以及目录指令
`{"action":"USBDETECTOR","detail":""}`

获取信息说明
`got`是否获取到，1为成功获取到U盘文件, 0为未获取到且未发生错误, -1为未获取到且发生错误此时错误信息为`error`

收取信息示例：

获取失败或者超时
```json
{"action":"USBDETECTOR","detail":"","got":0,"error":""}
```

获取出错
```json
{"action":"USBDETECTOR","detail":"","got":-1,"error":"error detail"}
```

获取成功
```json
[
  {
    "name": "G:",
    "desc": "H",
    "last_modified": "",
    "files": [
      {
        "name": "xx.pdf",
        "size": 3335159,
        "last_modified": "2017-05-03 17:28:34",
        "creation_time": "2017-06-19 15:13:29",
        "last_access_time": "2017-06-26 00:00:00"
      }
    ],
    "folders": {
      "f2": {
        "name": "f2",
        "desc": "",
        "last_modified": "2017-06-26 22:10:46",
        "files": [
          {
            "name": "f2pdf.pdf",
            "size": 3335159,
            "last_modified": "2017-05-03 17:28:34",
            "creation_time": "2017-06-26 22:10:48",
            "last_access_time": "2017-06-26 00:00:00"
          }
        ],
        "folders": {
          
        }
      },
      "honeywell": {
        "name": "honeywell",
        "desc": "",
        "last_modified": "2017-05-04 13:44:16",
        "files": [
          {
            "name": "N56XX-IM.pdf",
            "size": 3335159,
            "last_modified": "2017-05-03 17:28:34",
            "creation_time": "2017-05-04 13:44:16",
            "last_access_time": "2017-06-21 00:00:00"
          },
          {
            "name": "N56XX-UG.pdf",
            "size": 2739090,
            "last_modified": "2017-05-03 17:28:34",
            "creation_time": "2017-05-04 13:44:17",
            "last_access_time": "2017-06-01 00:00:00"
          }
        ],
        "folders": {
          "win7驱动": {
            "name": "win7驱动",
            "desc": "",
            "last_modified": "2017-05-04 13:12:12",
            "files": [
              {
                "name": "N56XX-IM.pdf",
                "size": 3335159,
                "last_modified": "2017-05-03 17:28:34",
                "creation_time": "2017-06-19 15:16:46",
                "last_access_time": "2017-06-23 00:00:00"
              }
            ],
            "folders": {
              
            }
          },
          "条码": {
            "name": "条码",
            "desc": "",
            "last_modified": "2017-04-25 23:59:46",
            "files": [
              
            ],
            "folders": {
              "代码交付件": {
                "name": "代码交付件",
                "desc": "",
                "last_modified": "2017-04-25 23:59:46",
                "files": [
                  {
                    "name": "条码扫描枪驱动接口文档.pdf",
                    "size": 550755,
                    "last_modified": "2017-01-19 03:08:34",
                    "creation_time": "2017-05-04 13:44:26",
                    "last_access_time": "2017-05-04 00:00:00"
                  }
                ],
                "folders": {
                  
                }
              }
            }
          },
          "测试中文": {
            "name": "测试中文",
            "desc": "",
            "last_modified": "2017-06-19 15:17:02",
            "files": [
              {
                "name": "N56XX-IM.pdf",
                "size": 3335159,
                "last_modified": "2017-05-03 17:28:34",
                "creation_time": "2017-06-19 15:17:04",
                "last_access_time": "2017-06-23 00:00:00"
              }
            ],
            "folders": {
              
            }
          }
        }
      }
    }
  }
]
```


### 查询文件页数

说明
- `file_id` 为当前处理文件的ID, 具备唯一性与时效性
- `copied`命令为是文件否拷贝成功, 只有当filepages以及

发送指令: 
```json
{
    "action": "FILEPAGES",
    "detail": "",
    "disk_file": {
        "file": "D:\\x.xlsx"
    }
}
```

返回成功示例
```json
{
    "err": "",
    "file_path": "G:\\x.xlsx",
    "pages": 1,
    "action": "FILEPAGES",
    "detail": "success",
    "file_id":"391F2EBA7AB21EFB198FD1A774A250C3"
}
```

```json
{
    "copied": true,
    "action": "FILECOPIED",
    "detail": "",
    "file_id":"391F2EBA7AB21EFB198FD1A774A250C3"
}
```

返回失败示例
```json
{
    "action": "FILEPAGES",
    "detail": "failed",
    "err": "文件不存在",
    "file_id":"391F2EBA7AB21EFB198FD1A774A250C3"
}
```

```json
{
    "copied": false,
    "action": "FILECOPIED",
    "detail": "file not exists",
    "file_id":"391F2EBA7AB21EFB198FD1A774A250C3"
}
```

### USB打印
- `copies` 打印份数
- `duplex` 打印单双面选项 1单面 2双面长边 3双面短边
- `from` 打印开始页
- `to` 打印结束页
- `pages_per_sheet` 每页打印页数

```json
{"action":"DISKPRINTING","disk_file":{"file":"D:\\Downloads\\apple.pptx","copies":1,"duplex":1,"from":2,"to":2,"pages_per_sheet":1}}
```

指令说明
- 和普通打印指令差不多，`file`参数为获取文件页数当中返回的！
- 此指令返回和正常的流程完全一致
- 若发送完指令中间件判定U盘未移除此时中间件会卡壳（最多一分钟）并返回`{"action":"USBEJECT","detail":false,"DISKPRINTING":true}`，当用户移除了U盘设备之后会返回`{"action":"USBEJECT","detail":true,"DISKPRINTING":true}`并且立即进行打印流程。

### 卸载所有USB设备，暂时无需使用
```json
{"action":"USBEJECT","detail":""}
```

返回示例：
```json
{"action":"USBDETECTOR","detail":"success"}
```
