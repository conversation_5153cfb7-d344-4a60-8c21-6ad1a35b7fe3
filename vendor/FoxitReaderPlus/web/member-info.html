<!DOCTYPE html>
<html lang="en"  style="overflow:hidden;padding:0; margin:0; border:0;width:205px;height:107px;">
<head>
    <meta charset="UTF-8">
    <title></title>
	<script src="jquery-1.8.3.min.js"></script>
    <style>
        .con-group{
            font-family: '宋体 常规', '宋体';
            font-weight: 400;
            font-style: normal;
            font-size: 12px;
            overflow: hidden;
            clear: both;
          
        }
        .dis-ib{
            display: inline-block;
            float: left;
            position: relative;
			  
        }
        .dis-ib.lf{
            width: 35%;
        }
        .dis-ib.rt{
            width: 65%;
        }
        .header-pho{
			cursor: pointer;
            height: 50px;
            width: 50px;
            margin: 12px;
            border-radius: 50%;
			
   /*  border: 1px solid #cccccc; */
   
   

        }
        .mes-box{
            margin: 15px 10px;
        }
        .mes-box .name-box{
		  
            display: block;
            font-weight: 700;
            font-size: 12px;
            padding: 3px 0 10px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
		
       /*  .mes-box .vip-box{
            background: #ababab;
            color: #ffffff;
            padding: 1px 2px;
            font-size: 8px;
        } */
		.vip-box{
			cursor: pointer;
		}
        .quit{
			 width:190px; 
            background-color: #f5f5f5;
            color: #767677;
            text-align: center;
            padding: 10px;
			cursor: default;
        }
        .quit:hover{
            color: #f78564;
        }
    </style>
</head>
<body onselectstart="return false" style="margin:0">
<div class="conrent">
    <div class="con-group">
        <div class="dis-ib lf">
            <img ondragstart='return false;' id="headImg" style="position:relative;top:3px;left:5px;" src="images/u20.png" class="header-pho" alt=""/>
        </div>
        <div class="dis-ib rt">
            <div class="mes-box"  style="position:relative;top:5px;">
                <span class="name-box" id="name"></span>
                <span class="vip-box" id="uuid">
                   
                </span>
            </div>
        </div>
    </div>
    <div class="con-group quit" id="button">
        退出当前帐号
    </div>
</div>
</body>
</html>
<script>
var head;
var name;
var uuid;
			function InitUserData(val){
				
			//val="images/333.png&HHHH&123";
//alert("InitUserData"+val);

			   head=val.split('&')[0];
			   name=val.split('&')[1];
			   uuid=val.split('&')[2];

			//if (!name.match(/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/)  ||  !name.match(/^(((13[0-9]{1})|159|153)+\d{8})$/)) { //邮箱或者手机
				//$("#headImg").attr("src","images/111.png");
			//}
			if(head==undefined || head=="" || head==null){}
			else{
			   $("#headImg").attr("src",head);
			}
			   $("#name").html(name);
			   $("#uuid").html("ID:"+uuid);

			   $("#name").css({'cursor':'pointer'});


			}
			   $("#button").click(function(){
			//alert($("#button").html());
			//InitUserData();
			    external.Logout();

			   })
			$(function(){
			    $("#headImg,#name,#uuid").on('mousedown',function(){
			//alert(1);
			    external.UserCenter();
			   })	
			})
</script>