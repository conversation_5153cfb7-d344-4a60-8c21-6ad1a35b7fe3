<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html xmlns="http://www.w3.org/1999/xhtml"  style="overflow:hidden;padding:0; margin:0; border:0;">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>无标题文档</title>
<link rel="stylesheet" href="Scroll.css" /> 
<style>
 
*{ padding:0; margin:0; font-size:12px; font-family:微软雅黑}

body{ overflow:hidden;}


.MainBox{ width:322px; height:250px; overflow-x:hidden; background:#fff; overflow-y:auto; padding-top:30px; SCROLLBAR-FACE-COLOR: #cdcdcd;        /*滚动条凸出部分的颜色*/
SCROLLBAR-HIGHLIGHT-COLOR: #ffffff;    /*滚动条空白部分的颜色*/
SCROLLBAR-SHADOW-COLOR: #ffffff;     /*立体滚动条阴影的颜色*/
SCROLLBAR-3DLIGHT-COLOR: #ffffff;     /*滚动条亮边的颜色*/
SCROLLBAR-ARROW-COLOR: #ffffff;     /*上下按钮上三角箭头的颜色*/
SCROLLBAR-TRACK-COLOR: #ffffff;     /*滚动条的背景颜色*/
SCROLLBAR-DARKSHADOW-COLOR: #ffffff;     /*滚动条强阴影的颜色*/
SCROLLBAR-BASE-COLOR: #ffffff; }

.MainBox .cont{ width:290px;height:30px; margin-top:10px; margin-bottom:20px; margin-left:15px;}
.MainBox .cont .left{width:240px; height:30px; float:left;}
.MainBox .cont .left .tt{width:230px; height:26px;}
.MainBox .cont .left .tt .name{width:150px; height:26px; float:left; text-align:left; line-height:26px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;}
.MainBox .cont .left .tt .status{ width:80px; height:26px; float:left; text-align:right; line-height:26px;}
.MainBox .cont .left .bb{width:230px; height:4px; background:#d7d7d7; line-height:4px;overflow: hidden;}
.MainBox .cont .left .bb .bar{ width:0%; height:4px; background:#ff9933}
.MainBox .cont .right{ width:50px; height:30px; float:left; text-align:center; line-height:30px;overflow:hidden}


.cont2{ width:290px;height:50px;  background-color:#fff; padding:10px; display:block; color:#333;border:1px solid #999999;overflow:hidden}
.cont2 .left{width:290px; height:30px; float:left;}
.cont2 .left .tt{width:290px; height:26px;}
.cont2 .left .tt .name{width:190px; height:26px; float:left; text-align:left; line-height:26px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;}
.cont2 .left .tt .status{ width:95px; height:26px; float:left; text-align:right; line-height:26px; padding-right:5px;}
.cont2 .left .bb{width:290px; height:4px; background:#d7d7d7; line-height:4px;overflow: hidden;}
.cont2 .left .bb .bar{ width:0%; height:4px; background:#ff9933;}

.cs a,.qx a{ cursor:pointer; text-decoration:none; color:#333}

#top{width:317px; height:34px;height:30px\9; background:#f0f0f0; border-bottom:0; line-height:30px; position:relative; position:absolute; left:0; top:0;}
#top .mask{ width:12px; height:17px; background:#000; z-index:888; position:absolute; right:0; top:0;}
</style>
</head>

<body>
 
 
	<div id="top">
    	<div style="width:275px; height:30px; float:left; text-indent:15px;">缓冲管理</div>
          <a style="width:30px; height:30px; float:left; display:block; background:url(min.png) no-repeat center" href="min" target="_blank"></a>
          
         <!-- <div class="mask" style="top:31px;"></div>
          <div class="mask" style="top:230px;"></div>-->
          
    </div>
    
    
    <div style="width:322px; line-height:10px\9;"></div>
    <div id="MainBox" class="example MainBox"></div>
    
    
<!-- <a class="cont2"  href="max" target="_blank" style="display:none" >
            
            	<div class="left">
                	
                    <div class="tt">
                    
                    	<div class="name"></div>
                        
                        <div class="status" style="text-align:center;"></div>
                    
                    </div>
                    
                	<div class="bb">
                    
                    	<div class="bar"></div>
                    	
                    </div>
                
                </div>
                
            	 
            </a> 	

 -->

<div style="position:absolute; left:0; top:70px;" id="xx"></div>

<script language="javascript" src="jquery-1.8.3.min.js"></script>
<script type="text/javascript" src="handlebars.min.js"></script>  
<script type="text/javascript" src="json.js"></script>  
<script id="template" type="text/x-handlebars-template">
{{#download}}

 <div class="cont" id="{{id}}">
            
            	<div class="left">
                	
                    <div class="tt">
                    
                    	<div class="name" data-name="{{name}}" title="{{name}}">{{name}}</div>
                        
                        <div class="status">{{status}}</div>
                    
                    </div>
                    
                	<div class="bb">
                    
                    	<div class="bar"></div>
                    	
                    </div>
                
                </div>
                
            	<div class="right btn qx" data-id ="{{id}}"><a data-id="{{id}}"  target="_blank" href="">取消&nbsp;&nbsp;</a></div>
				
				<div class="right btn cs" data-id ="{{id}}" style="display:none;"><a  data-id="{{id}}" target="_blank" href="">重试&nbsp;&nbsp;</a></div>
				
				<div  style="clear:both"></div>
                
            </div> 	
			
{{/download}}
</script>


<script>

 

//$('#top').click(function(){
//	var randomnumber=Math.floor(Math.random()*100000) ;
//	download('{ "id": '+randomnumber+', "name": "aXQxNjg=" , "width": "87", "status": "1"}');//href="{{id}},Tryagain"   href="{{id}},cancel"
//})

 
		//0 代表下载中 1代表下载完成  2代表下载失败
		var template = Handlebars.compile($("#template").html());
		
		var data = {download: []};
 
		
		
		function download(jsonData){
			var data1 = eval('(' + jsonData + ')');
			var flag = true;
			for (var i = 0; i < data.download.length; i++){
				if(data1.id==data.download[i].id){
					data.download[i].width=data1.width;
					data.download[i].status=data1.status;
					data.download[i].name= data1.name;
					flag = false;
				}
				 
			}
			
			if(flag) {
					data.download.push(data1);
					$('#MainBox').html(template(data));	 
					if($('.cont').length>4){$('.cont').last().css("margin-bottom","70px")}
			}
				
						
			for (var i = 0; i < data.download.length; i++){
				$('#'+data.download[i].id).addClass(data.download[i].id)
				if($('.'+data.download[i].id).length>1){$('.'+data.download[i].id).eq(1).remove()}
				if(data.download[i].status=="0"){
					$('#'+data.download[i].id).find('.name').html(data.download[i].name)
					$('#'+data.download[i].id).find('.bar').css("width",data.download[i].width+"%")
					$('#'+data.download[i].id).find('.status').html(data.download[i].width+"%");
				}
				
				
 				
				 
				if(data.download[i].status=="2"){ 
					$('#'+data.download[i].id).find('.status').html("下载失败"); 
					$('#'+data.download[i].id).find('.right').eq(0).css("width","0px");
					$('#'+data.download[i].id).find('.right').eq(1).css("display","block");
				}
				
				
				if(data.download[i].status=="3"){
					$('#'+data.download[i].id).find('.status').html("等待缓冲");
				}
				
				
				if(data.download[i].width=="100"){ 
					if($('#'+data.download[i].id).length>0){$('#'+data.download[i].id).remove();}
					data.download.splice(i,1);		
					isNull();
				}
				
				
				
			}
			
			 
		}
		
		
		
		 $('.qx a').live('click',function(){ 
			 				var num  =  $(this).parents('.cont').index();
							var oid = $(this).data('id'); 
							data.download.splice(num,1);
							$("#"+oid).remove();  
							cancel(oid);
							window.setTimeout(function(){ 
								isNull();
							},500); 
		 })
 		 
		 
		 var times = 0;
		 $('.cs a').live('click',function(){
			 			if(times!=0)return;
						times=1;
	     				var num  =  $(this).parents('.cont').index();
						var oid = $(this).data('id');
						data.download.splice(num,1);
						$("#"+oid).remove();  
						window.setTimeout(function(){times = 0;Try(oid);},500); 	
		 })
		
		
					
		//取消 和 重试			
		function cancel(s){
		 	window.open(s+",cancel");
		}
		function Try(s){
		 	window.open(s+",Tryagain");
		}
 		 
		function isNull(){
			$('.cont2 .name').html(data.download.length+"个文档等待缓冲");
			if($('.cont').length<1){
				data = {download: []}
				window.open("null");
			}
		}
		
		$(document).bind("contextmenu",function(e){   
			 return false;   //屏蔽右键菜单   
		});
		//function change(a){
//			if(a=="0"){
//				$('.cont2').hide();
//				$('#top').show();
//				$('#MainBox').show();
//			}
//			if(a=="1"){
//				
//				$('#top').hide();
//				$('#MainBox').hide();
//				$('.cont2').show();
//				//alert(data.download.length)
//				for (var i = 0; i < data.download.length; i++){
//					
//					if(data.download[i].status=="0"){
//						$('.cont2').find('.bar').css("width",data.download[i].width+"%")
//						$('.cont2 .status').html(data.download[i].width+"%");
//						$('.cont2 .name').html(data.download.length+"个文档等待缓冲");
//					}else{
//						$('.cont2 .name').html(data.download.length+"个文档等待缓冲");
//					}
//					
//				}
//			}
//		}
//		change(0);
		
		
 
</script>

</body>
</html>
