<!DOCTYPE>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>新标签页</title>
<link rel="stylesheet" href="css/index.css">
<link rel="stylesheet" href="css/ui-dialog.css">

<script type="text/javascript" src="js/jquery-1.8.3.min.js"></script>
<script type="text/javascript" src="js/jquery.cookie.js"></script>
<script type="text/javascript" src="js/json.js"></script>
<script type="text/javascript" src="js/handlebars.min.js"></script>
<script type="text/javascript" src="js/slides.min.jquery.js"></script>
<script type="text/javascript" src="js/jquery-ui.min.js"></script>
<script src="js/dialog-min.js"></script>
<script src="js/winsize.js"></script> 
<script src="js/jquery.mousewheel.js"></script> 
<script src="js/IframeOnClick.js"></script> 
<script src="js/b64.js"></script>
</head>
<body style=" height:0px\9;filter:alpha(opacity=0);opacity:0;">
  <img src="images/width.jpg" width="60%" style="position:absolute; z-index:-9999; top:-999; max-width:1050px;min-width:800px;min-height:350px;overflow:visible;_width:800px; _height:350px; min-height:360px\9;" id="widthID" /> 
  
 
<div style="height:100%;" id="myif">
<div class="bar"></div><a class="add"></a> 
<div id="slides" >

		<div class="slides_container">
			<div class="item">
				<iframe id="History" width="100%" height="100%" class="hdli" name="History" frameborder="0" scrolling="no" allowtransparency="true" src="Historytouch.html" data-url="images/recent.png" data-text="最近阅读"></iframe>
			</div>
		</div>
   
      
</div>


 


 
</div>
<script id="template" type="text/x-handlebars-template">
{{#menu}}
 <li style="background-image:url({{img_url}})"><span>{{tooltip}}</span></li>
{{/menu}}
</script>

<script id="template2" type="text/x-handlebars-template">
{{#menu}}
 <div class="showM tshowM {{id}}" index="{{isshow}}" id={{id}}>
 	<div class="img" style="background-image:url({{menuImg}})"><div class="icon"></div></div>
	<div class="text">{{tooltip}}</div>
 </div>
{{/menu}}
</script>
 
<script id="template3" type="text/x-handlebars-template">
{{#bookStoreSearchParam}}
  <a style="background-image:url({{img_url}});" data-bg="{{img_url}}" data-value="{{value}}" class="select_logo">{{showName}}</a>	
{{/bookStoreSearchParam}}
</script>
 
</body>

</html>
<script type="text/javascript">

var shows = "0";
 
var sx="0"; 

 

	
function initOtherInfo(bb){
		$.cookie("vs", bb, {path : '/'});		
}

	
function ShowHistoryList(HistoryData) {
	window.setTimeout(function(){
		$("#History")[0].contentWindow.initData(HistoryData);
		if($.cookie("vs")!=null){
			$("#History").contents().find('.pin').show();
			$("#History").contents().find('.nopin').show();
		}
	},10);	
}



var second = 0;
var H_keyword = "最近阅读";
//window.setInterval("OnlineStayTime();", 200);
var url01 = "";
var url02 = "";

var data = {};
var ss;
var conts = "{menu: " + $.cookie("index") + "}";
var data_Cookies = eval('(' + conts + ')');

var xx;
var xxx;

var data2 = {};



var select_logo = "";
var showM = "";
var numli;


var changes = "0";
var yidong = "0";

var jd = "0";

var startTime = new Date();


var template = Handlebars.compile($("#template").html());
var template2 = Handlebars.compile($("#template2").html());
var template3 = Handlebars.compile($("#template3").html());

function initRecommendJson(initRecommendJson_data) {
    var initRecommendJson_data = eval('(' + initRecommendJson_data + ')');
    var arr = initRecommendJson_data.recommendWord;

	var regs = new RegExp("；","gi");
	arr = arr.replace(regs,";")
	
	if(arr!=null){
		var arrs = new Array();
		arrs = arr.split(";");
		for (i = 0; i < arrs.length; i++) {
			var selecthtml = "<a class='selectBox_st'><div>" + (i + 1) + "</div><span>" + arrs[i] + "</span></a>";
			$('.selectBox').append(selecthtml);
		}
		$('.selectBox_st div').eq(0).css("background", "#f58c85");
		$('.selectBox_st div').eq(1).css("background", "#fcbc4b");
		$('.selectBox_st div').eq(2).css("background", "#a1d958");
	}else{
		shows = "1";
	}
    
}

  
 
  
$(window).resize(function() {
     winsize();
});

 
 
 
$(window).load(function() {
	  window.setTimeout(function(){
			$("#slides").append("<ul class='pagination'  style='border-right:1px solid #d4d4d4'><li class='current'><a href='#0' style='background-image: url(&quot;images/recent.png&quot;);'>最近阅读</a></li></ul>");
		   window.open("foxitContentSyndicationFromWeb_History"); 
		 $('body').css("height","100%");
		 $('body').css("opacity","1");
		 winsize(); 
	},500);		 
}); 

 
 


 
 
 
</script>
