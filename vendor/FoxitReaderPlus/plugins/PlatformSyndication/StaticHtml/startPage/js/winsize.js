
function winsize(){
 
	var imgW =  $("#widthID").width();
	var imgH =  $("#widthID").height();
	 
	var win_W = $(window).width();
	var win_H = $(window).height();
  	$('.slides_container ').css({ width : win_W, height : win_H});
	$('.slides_control ').css({ width : win_W, height : win_H});
 	$('.item').css({ width : imgW, height : imgH});

	 
	if(win_W>800){
		$(".item").css("margin-left",win_W/2-imgW/2);
		$(".item").css("margin-top",win_H/2-imgH/2+20);
		$('.sBox').css({"left":"50%","margin-left":"-300px"});
	} else{
		$(".item").css("margin-top",win_H/2-imgH/2);
		$(".item").css("margin-left","0px");
	}

	
	if(win_H>490){
		$('.sBox').css("top",((win_H-imgH+40)/2)/2-$('.sBox').height()/2-10)
	}else{
		$('#myif').css("margin-top","0");
		$(".item").css("margin-top","90px" );
		$('.sBox').css("top","15px");
	}
 
   	
  
   
   if($(window).width()<980){$('.pagination').css("margin-left",-$('.pagination').width()/2 );	$('.pagination li').css("width","60px");$('.prev,.next').hide();$('.pagination li').css("width","60px");$('.pagination li a').css("padding-left","60px");$('.add').css({left:$('.pagination').offset().left+$('.pagination').width()});$('.pagination').css("margin-left",-$('.pagination').width()/2 );}else{$('.pagination li').css("width","125px");$('.prev,.next').show();$('.pagination li').css("width","125px");$('.pagination li a').css("padding-left","0");$('.add').css({left:$('.pagination').offset().left+$('.pagination').width()});$('.pagination').css("margin-left",-$('.pagination').width()/2 );	}
      $('.add').css({top:$('.pagination').offset().top,left:$('.pagination').offset().left+$('.pagination').width()})
      $('.pagination').css("margin-left",-$('.pagination').width()/2 );
 
}  


function winsiz_sub(){

	 
 
}