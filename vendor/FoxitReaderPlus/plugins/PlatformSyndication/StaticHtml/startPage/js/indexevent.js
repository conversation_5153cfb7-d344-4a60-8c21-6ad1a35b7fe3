$(document).ready(function(){
	
	
	 
 
 $.ajax({
				type : "POST",
				dataType : "jsonp",
				jsonp:'callback', 
				url :"http://api.foxitreader.cn/getHeatKeyword",
				success : function(HeatKeyword) { 
						for(var i=0; i<HeatKeyword.data.length; i++){
							var keyhtml ="<a>"+HeatKeyword.data[i].keyWord+"</a>"
							$(".Keyword span").append(keyhtml);
						}
				} 
	 });
	 
  
  $('.sBox .border .logo').on('click', function () {
		var d = dialog({
			content: select_logo,
    		quickClose: true,
			padding: 1
		});
		d.show(document.getElementById('logo'));
		 $('.select_logo').on('click', function () {
			$('.sBox .border .logo').css("background-image","url("+$(this).data("bg")+")");
			url01 = $(this).data("value");
		 	d.close().remove();
	 	 });
	  });

	  
	  $('.stbn').on('click', function () {
		  if($('.input').val().replace(/^ +| +$/g,'')==''||url01==""){$("#test").focus();$("#test").val("");}else{
				url02=$('.input').val();
				if(url02.length>60){url02=url02.substr(0, 60)}
		    	window.open("statistic,300023,"+Base64.encode($(".input").val())+",foxitContentSyndicationFromWeb");//操作数据_搜索_Html搜索框击
		   		window.open("search.html?name="+Base64.encode(url01)+"&keyword="+Base64.encode(url02));
		  }
	  });
	  
	  
	  $('.sBox .border .input').on('focus', function () {
		  jd="1";
			 $(this).keydown(function(){ 
					window.setInterval(function(){
								if($('.sBox .border .input').val().length>0){$('.inputBG').hide();}else{$('.inputBG').show();}
					},1); 
			 });
			 
	  });
	  
	  
	  $('.sBox .border .input').on('click', function () {
		  if(shows == "0"){
			  $('.selectBox').show();
			  $('.sBox .Keyword').hide();
		  }
		  	 
	  });
	  
	   $('.selectBox_st').live('click', function () {
				 window.open("search.html?name="+Base64.encode(url01)+"&keyword="+Base64.encode($(this).find('span').text()));
	  });
	 
	  
	  $('.sBox .border .input').on('focusout', function () {
		  jd="0";
			 window.setTimeout(function(){$('.selectBox').hide();$('.sBox .Keyword').show();},200);	
	  });
 
 		
		 $(".Keyword span a").live("click",function(){
		 	window.open("statistic,200016,"+Base64.encode($(this).text())+",foxitContentSyndicationFromWeb");//内容数据_搜索_推荐关键字点击
		 	window.open("search.html?name="+Base64.encode(url01)+"&keyword="+Base64.encode($(this).text()));
	 }) 
	 
	 $(".inputBG").live("mousedown",function(){
		 $("#test").focus();
		 if(shows == "0"){
			 $('.selectBox').show();
			 $('.sBox .Keyword').hide();
		 }
	 }) 
  
  
   var h = " <div class='showM h' index='0'><div class='img' style='background-image:url(images/01.jpg)'><div class='icon'></div></div><div class='text'>最近阅读</div></div>"

    var hh = "<div id='slides'><div class='slides_container'><div class='item'><iframe id='History' width='100%' height='100%' class='hdli' name='History' frameborder='0' scrolling='no' allowtransparency='true' src='Historytouch.html' data-url='images/recent.png' data-text='最近阅读'></iframe></div></div></div>"
   
    var hhh = "<ul class='pagination' ><li class='current'><a href='#0' style='background-image: url(&quot;images/recent.png&quot;);'>最近阅读</a></li></ul>"

   
   
   var list_history = [] ;
   var list_now = [] ;
   
 
	  $('.add').on('click', function () {
 
				window.open("statistic,300025,"+Base64.encode('NavigetAddButton')+",foxitContentSyndicationFromWeb");//操作数据_新标签页_+号点击量
				
				conts =  "{menu: "+ $.cookie("index") +"}"; data_Cookies = eval('(' + conts + ')');
				data_Cookies.menu.sort( function(a, b){ return (a.id - b.id);});
				window.setTimeout(function(){
				$( ".ui-dialog-content" ).sortable({ opacity: 0.8 ,scroll: false,revert: true,items:'.tshowM',
					 	stop: function(event, ui) {
							yidong="1";
							var px = $(".ui-dialog-content" ).sortable('toArray');
							$.cookie("paixu",JSON.JsonToStr(px), { path: '/', expires: 100 });
							for(var i = 0; i < px.length; i++){data_Cookies.menu[px[i]].id=i;}
							changes="1";
							xx =  data_Cookies.menu;
				    		xxx = JSON.JsonToStr(xx);
							$.cookie("index",xxx, { path: '/', expires: 100 });
							
						} 
					 });
					$( ".ui-dialog-content" ).disableSelection(); 
					
					
					for(var i=0; i<data_Cookies.menu.length; i++){list_history.push(data_Cookies.menu[i].isshow)}//记录原始数据
					 
					
					
				},500);

				var d = dialog({
					title: '频道管理',
					content: h+template2(data_Cookies) ,
					cancel: function () {  
					if($.cookie('index')!=undefined){
						if(changes=="1"){
							 $('#slides').remove();
							 $('#myif').append(hh);
							 conts =  "{menu: "+ $.cookie("index") +"}"; data_Cookies = eval('(' + conts + ')'); 
							 data_Cookies.menu.sort( function(a, b){ return (a.id - b.id);});
							 for (var i = 0; i < data_Cookies.menu.length; i++){
								 var html = "<div class='item'><iframe width='100%' class='sss' height='100%' id=" + i + " frameborder='0' scrolling='no' allowTransparency='true'  src=" + data_Cookies.menu[i].content_url + " data-url=" + data_Cookies.menu[i].img_url + " data-text=" + data_Cookies.menu[i].tooltip + "></iframe></div>";
         $('.slides_container').append(html);
								 }
								for (var i = 0; i < data_Cookies.menu.length; i++){ 
									if(data_Cookies.menu[i].isshow =="1"){
										$('.item').eq(i+1).addClass('del');
										$('.pagination li').eq(i+1).addClass('del');
									}
									data_Cookies.menu[i].isshow = $(".showM").eq(i).attr("index");
								}
								$('.del').remove();
								
									$('#slides').slides({
											preload: true,
											generateNextPrev: true
								});
								
								if($('.pagination li').length=="0"){$("#slides").append(hhh);$(".item").addClass('abs');}else{$(".item").removeClass('abs');}
								$(".pagination a").each(function (index, domEle) { 
										$(domEle).html($('iframe').eq(index).data("text"))
										$(domEle).css("background-image","url("+$('iframe').eq(index).data("url")+")") 
								 }); 
								
								
								if($('.item').length=="1"){$('.prev,.next').remove()}
								winsize();
								conts =  "{menu: "+ $.cookie("index") +"}"; data_Cookies = eval('(' + conts + ')'); 
								xx =  data_Cookies.menu; xxx = JSON.JsonToStr(xx);$.cookie("index",xxx, { path: '/', expires: 100 });
								var tt="";
								 for(var i=0; i<data_Cookies.menu.length; i++){
									list_now.push(data_Cookies.menu[i].isshow)
									if(list_history[i]!=list_now[i]){
										if(data_Cookies.menu[i].isshow=="0"){
											tt=data_Cookies.menu[i].tooltip+"显示";
											window.open("statistic,200006,"+Base64.encode(tt)+",foxitContentSyndicationFromWeb");//内容数据_新标签页_频道显示或隐
										}
										if(data_Cookies.menu[i].isshow=="1"){
											tt=data_Cookies.menu[i].tooltip+"隐藏";
											window.open("statistic,200006,"+Base64.encode(tt)+",foxitContentSyndicationFromWeb");//内容数据_新标签页_频道显示或隐
										}
									}
 
								 }
 
								 
								frame();
								changes="0"
								window.open("foxitContentSyndicationFromWeb_History");
 								
								 
								list_history=[];
								list_now=[];
						}
							
							
					}			
					}	
					}).width(580).height(343).showModal();$('.ui-dialog-button').hide();
					if($.cookie('index')!=undefined){for (var i = 0; i < data_Cookies.menu.length; i++){if(data_Cookies.menu[i].isshow =="1"){$('.showM').eq(i+1).find('.icon').hide();}}}
	  });
	  
	 
	  
	  
	  
	  
	  $('.showM').live('click', function () {
		    changes="1";
		   	conts =  "{menu: "+ $.cookie("index") +"}";
			data_Cookies = eval('(' + conts + ')');
			
			if($(this).index()!=0){
				var num = $(this).index();
				if($(this).find('.icon').css('display')=="block"){
					$(this).find('.icon').hide(); 
					if(yidong=="0"){data_Cookies.menu[num-1].isshow = "1";}else{data_Cookies.menu[eval('(' + $.cookie("paixu") + ')')[num-1]].isshow = "1";}
					$(this).attr("index","1");
					xx =  data_Cookies.menu;
				    xxx = JSON.JsonToStr(xx);
					$.cookie("index",xxx, { path: '/', expires: 100 });data_Cookies.menu.sort( function(a, b){ return (a.id - b.id);});
				}else{
					$(this).find('.icon').show();
					if(yidong=="0"){data_Cookies.menu[num-1].isshow = "0";}else{data_Cookies.menu[eval('(' + $.cookie("paixu") + ')')[num-1]].isshow = "0";}
					$(this).attr("index","0");
					xx =  data_Cookies.menu;
				    xxx = JSON.JsonToStr(xx);
					$.cookie("index",xxx, { path: '/', expires: 100 });data_Cookies.menu.sort( function(a, b){ return (a.id - b.id);});
				}
			}
	  });
	  
	  
  
	  
$(document).bind("contextmenu",function(e){   
     return false;   //屏蔽右键菜单   
});

 
$('.pagination li').live('mousedown', function () {
	var endTime = new Date();
	second=endTime.getTime() - startTime.getTime()
	numli = $(this).index()+1
	window.open("statistic,200007,"+Base64.encode($('.current a').text())+",foxitContentSyndicationFromWeb");//内容数据_新标签页_频道被点击
	window.open("statistic,200009,"+ second+","+Base64.encode(H_keyword)+",foxitContentSyndicationFromWeb")//内容数据_新标签页_频道显示时长
	startTime= new Date();
	H_keyword = $('.current a').text();
	window.setTimeout(function(){
		window.open("statistic,200010,"+Base64.encode($('.current a').text())+",foxitContentSyndicationFromWeb")//内容数据_新标签页_频道阅读量（30秒一次）
	},30000);
	frame(); 
})


 $(document).keydown(function(event){
	 if(event.keyCode==13){
		 $('.stbn').click();
	 }
	 if(event.keyCode==37){
		  if(jd=="0"){
		 $('.prev').click();
		 window.open("statistic,200007,"+Base64.encode($('.current a').text())+",foxitContentSyndicationFromWeb");//内容数据_新标签页_频道被点击
		  }
	  }
	  if(event.keyCode==39){
		  if(jd=="0"){
		 	$('.next').click();
			 window.open("statistic,200007,"+Base64.encode($('.current a').text())+",foxitContentSyndicationFromWeb");//内容数据_新标签页_频道被点击
		  }
	  }
  
  }); 


   
$('body').bind('mousewheel', function(event, delta, deltaX, deltaY) {  
         if(delta>0){$('.prev').click();}else{$('.next').click();}
});  

 
 
  
  
})