var IframeOnClick = {
             resolution: 100,
             iframes: [],
             interval: null,
             Iframe: function() {
              this.element = arguments[0];
              this.cb = arguments[1];
              this.hasTracked = false;
             },
             track: function(element, cb) {
              this.iframes.push(new this.Iframe(element, cb));
             },
			 doit:function(){
				 setInterval(function() { IframeOnClick.checkClick();}, this.resolution);
			 },
             checkClick: function() {
              if (document.activeElement) {
               var activeElement = document.activeElement;
               for (var i in this.iframes) {
                if (activeElement === this.iframes[i].element) { // user is in this Iframe
                 if (this.iframes[i].hasTracked == false) {
                  this.iframes[i].cb.apply(activeElement, []);
                  this.iframes[i].hasTracked = true;
				  document.getElementById("test").focus();

                 }
                } else {
                 this.iframes[i].hasTracked = false;
                }
               }
              }
             }
            };