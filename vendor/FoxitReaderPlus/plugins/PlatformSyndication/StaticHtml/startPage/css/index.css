/* css 重置 */
		*{margin:0; padding:0; list-style:none; }
		html,body{ background:#f8f8f8; font:normal 13px/22px 微软雅黑;  overflow:hidden;position:relative}
		img{ border:0;  }
		a{ text-decoration:none; color:#333; position:relative}

		/* 本例子css */
		.slideBox{ width:100%; overflow:hidden; position:relative; }
		.hd{ width:100%;height:40px; overflow:hidden; position:absolute; bottom:0px; z-index:1;background:#f0f0f0; border-top:1px solid #b7b7b7; }
		.hd ul{ overflow:hidden; zoom:1; float:left; position:absolute; left:50%; width:125px; margin-left:-62px;}
		.hd ul li{ overflow:hidden; width:125px; height:38px; display:block; float:left; border-left:1px solid #d4d4d4; text-align:center; line-height:38px; cursor:pointer; background-repeat:no-repeat; background-position:18px 0; text-indent:20px; color:#666666;}
		.hd ul li.on{ background-color:#FFF; border-bottom:2px solid #f08200; color:#f08200;background-position:20px -38px;background-image:url(../images/recent.png);}
		 
		


		/* 下面是前/后按钮代码，如果不需要删除即可  bottom:70px;*/
		.slideBox .prev,
		.slideBox .next{ position:absolute; left:3%; top:50%; margin-top:-40px;display:block;width:50px;height:80px;background:url(../images/left_nor.png) no-repeat center;/*filter:alpha(opacity=50);opacity:0.5;*/ }
		.slideBox .next{ left:auto; right:3%; background:url(../images/right_nor.png) no-repeat center; }
		.slideBox .prev:hover{background:url(../images/left_hover.png) no-repeat center;}
		.slideBox .next:hover{background:url(../images/right_hover.png) no-repeat center;}
		.slideBox .prevStop{ display:none;}
		.slideBox .nextStop{ display:none;}
		
		 /* 搜索框 */
		 .sBox{ width:40%; height:120px; position:absolute; left:50%; top:0;z-index:99;}
		 .sBox .border{ width:100%; height:40px; border:1px solid #f08200; margin-top:40px;border-radius: 3px; position:relative; background:#fff;  }
		 .sBox .border .logo{ width:40px; height:40px; background-position:center; background-repeat:no-repeat; float:left; background-image:url(../images/s.png); cursor:pointer; position:absolute; z-index:999; left:0;}
		 .select_logo{ display:block;width:100px; height:30px; line-height:30px;background-position:10px center; text-indent:35px;background-repeat:no-repeat; cursor:pointer}
		 .select_logo:hover { background-color:#fff5e9}
		 
		 .selectBox{ width:100%; background:#fff; position:absolute; left:0; top:43px; display:none; border:1px solid #CCC}
		 .selectBox .selectBox_st{display:block;width:100%; height:30px; line-height:30px;  text-indent:35px; cursor:pointer}
		 .selectBox .selectBox_st:hover { background-color:#fff5e9}
		 
		 .sBox .border .input{ width:100%;_width:90%; height:40px; float:left; border:0; line-height:40px; color:#bcbcbc; text-indent:50px; background:url(../images/down.gif) no-repeat 32px center}
		 .sBox .stbn{width:110px; height:40px; background:#f08200; color:#FFF; text-align:center; line-height:40px;border-radius: 3px; cursor:pointer; position:absolute; right:-120px;}
		 .sBox .stbn:hover { background:#f6931f;}
		 .sBox .stbn:active {background:#f08200}
		  
		 .sBox .Keyword{width:100%; height:30px; margin-top:10px; color:#666; text-indent:5px;}
		 .sBox .Keyword a{margin-left:15px; color:#666;}
		 .sBox .Keyword a:hover {text-decoration:underline}
		 
		 
		 
		 
		 .showM{width:90px; height:125px; float:left; margin-left:45px; margin-top:20px; cursor:pointer;border:1px solid #fff}
		 .showM .img{width:90px; height:90px; background-position:center; background-repeat:no-repeat; position:relative; background-image:url(../images/01.jpg)}
		 .showM .img .icon{ width:29px; height:29px; background:url(../images/check.png); position:absolute; top:0; right:0px;}
		 .showM .text{width:88px; height:33px; border:1px solid #f2f2f2; line-height:33px; text-align:center; font-size:13px; color:#333}
		 .showM:hover{ border:1px solid #c6c6c6}
		 
		 
		 .showM2{width:90px; height:125px; float:left; margin-left:45px; margin-top:20px; cursor:pointer}
		 .showM2 .img{width:90px; height:90px; background-position:center; background-repeat:no-repeat; position:relative}
		 .showM2 .img .icon{ width:29px; height:29px; background:url(../images/check.png); position:absolute; top:0; right:0px;}
		 .showM2 .text{width:88px; height:33px; border:1px solid #f2f2f2; line-height:33px; text-align:center;}
		 