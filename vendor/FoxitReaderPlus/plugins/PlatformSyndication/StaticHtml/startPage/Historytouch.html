<!DOCTYPE>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title></title>
<style>
html,body,form ,ul{height:100%; margin:0; padding:0; overflow:hidden; font:normal 13px/22px 微软雅黑; background-color:#f8f8f8;} 
.suoding{width:100px; height:30px; position:absolute; left:50%; top:50%; margin-left:-50px; margin-top:-15px; z-index:99;background:url(images/pin.png) #fbbf6f no-repeat 5px center;filter:alpha(opacity=90);opacity:0.9; color:#FFF; text-align:center; line-height:30px; text-indent:10px; display:none}
.jiechu{width:100px; height:30px; position:absolute; left:50%; top:50%; margin-left:-50px; margin-top:-15px; z-index:99;background:url(images/nopin2.png) #fbbf6f no-repeat 5px center;filter:alpha(opacity=90);opacity:0.9; color:#FFF; text-align:center; line-height:30px; text-indent:10px; display:none}
.ddd{ background:#fff; width:10px; height:18px; position:absolute; right:0px; top:25px; z-index:99; display:none}
.box{background:#ffffff; position:relative; float:left;  cursor:pointer; min-width:188px; min-height:110px; list-style:none; width:24%; *width:23.5%; margin-left:1%; height:31%;  margin-bottom:1%}
.box p{ height:100%; margin:0; padding:0;}
.box strong{ position:absolute; width:92%; margin-top:10px; height:auto; left:4%; display:block; font-size:13px;word-break:break-all; /*white-space:nowrap;*/ line-height:20px;text-overflow:ellipsis;overflow:hidden;}
.box span{position:absolute; width:92%; left:4%; display:block; height:20px;  font-size:1em; bottom:28px; display:block; color:#999; white-space:nowrap;   font-size:12px;text-overflow:ellipsis;overflow:hidden;}
.tool{position:absolute; height:24px; width:100%; left:0; bottom:0px; z-index:99; background:#666666; line-height:24px; text-indent:10px; color:#FFF; font-size:1em; display:none;} 
.box .tool .text{ width:50%; height:30px; float:left;}
.box .tool .bar{ width:48%; height:30px; float:left;}
.box .tool .bar .close{ width:16px; height:16px; float:right; background:url(images/ts_close.png); margin-top:4px;}
.box .tool .bar .pin{ width:16px; height:16px; float:right; background:url(images/nopin.png); margin-top:3px; margin-right:5px; display:none}
.box .tool .bar .nopin{ width:16px; height:16px; float:right;  background:url(images/pin.png); margin-top:3px; margin-right:5px; display:none}
.index{background:#fbbf6f url(images/open.jpg) center no-repeat;}
.index .show{ background:#644c2c; display:block}
.text a{ color:#FFF; text-decoration:none}
.text a:hover {text-decoration:underline}
.bai{ position:static; float:left;  cursor:pointer; min-width:188px; min-height:110px; list-style:none; margin-left:8px; margin-top:8px; border: #e8e8e8 solid 1px;}
.ui-effects-transfer { border: 2px dotted gray; }
</style>
<link rel="stylesheet" href="js/jquery-ui.min.css">
<script type="text/javascript" src="js/jquery-1.8.3.min.js"></script>
<script type="text/javascript" src="js/handlebars.min.js"></script>
<script type="text/javascript" src="js/jquery.mousewheel.js"></script> 
<script type="text/javascript" src="js/handlebars.min.js"></script>
<script type="text/javascript" src="js/my_jquery-ui.min.js"></script> 
</head>

<body>
 
<ul id="sortable">
	<li class="box index">
            <div class="tool show">打开文档</div>
    </li>
</ul>
 
 

<script id="template" type="text/x-handlebars-template">
{{#history}}
<li class="box noindex" id="{{id}}" data-url="{{content_url}}">
	<div class="suoding">锁定内容</div>
	<div class="jiechu">解除锁定</div>
 	<div class="ddd">..</div>
 	<p>
		<strong>{{tooltip_nocode}}</strong>
    	<span>{{content_url_nocode}}</span>
	</p>
    <div class="tool"> 
		<div class="text"><a class="a_open">打开</a></div>
		<div class="bar">
			<a class="close" title="删除记录"></a>
			<a class="pin" title="置顶"></a>
		</div>
	</div>
 
</li>	
{{/history}}
</script>


<script type="text/javascript">
	var xx;
	var yy;
    var sd="0";
    var text;
 	var thisbox;
	var afters="0";
	
	$('body').bind('mousewheel', function(event, delta, deltaX, deltaY) {  
         if(delta>0){window.parent.prev()}else{window.parent.next()}
	});  

	var template = Handlebars.compile($("#template").html());
	var data;
	var ss;
	
	
	
	$( "#sortable" ).sortable({
				  items :".ks",                        
				  opacity: 0.9,                      
				  revert: true,                      
				  cancel: '.index',
				  scroll: false
	});
	
	
	 
	function initData(initda){

				ss = "{history: "+ initda +"}";
				data = eval('(' + ss + ')');
				data.history.sort( function(a, b){ return (a.id - b.id);});
				
				
				
				if(sd=="0"){
					$('.noindex').remove();
					$('.index').after(template(data));	
					winsize();
				}else{
						for (i = 0; i < data.history.length; i++) {
							
							if(text==decodeURIComponent(data.history[i].tooltip_nocode)){
								 
								$('.noindex').eq(i).addClass('here');
								
								var id = thisbox.attr('id');

								thisbox.effect( "transfer",{ to: ".here", className: "ui-effects-transfer" }, 400, function(){
									
										sd="0"; 
								 
										if(afters=="1"){
											$('.here').after(thisbox.clone());
										}else{
											$('.here').before(thisbox.clone());
										}
 
										$('.noindex').removeClass('here');
										
										afters="0";
										
										thisbox.remove();

										$('#'+id).effect( "highlight", 500);
 
									 
								} ); 
								
 
							}
						}
				}
				
			 
	
	}
 	
 	
	 
	
	
 
 
	$('.index').live('click', function() {
			window.open('index.html?js={"type":"open"}');
	});
	

	$('.noindex').live('mouseover mouseout', function(event) {	 
			 if (event.type == 'mouseover') {
				 $('.noindex').find('.tool').css("display","none");$('.noindex').removeClass('ks');
				 $(this).find('.tool').css("display","block");$(this).addClass('ks');
			} else {
				$(this).find('.tool').css("display","none");$(this).removeClass('ks');
			}	 
	});
	
	$('.a_open').live('click', function() {
				window.open('index.html?js={"content_url":"'+$(this).parents('.box').data('url')+'","type":"click"}');  
	});

	$('.noindex').find('p').live('click', function() {
			window.open('index.html?js={"content_url":"'+$(this).parents('.box').data('url')+'","type":"click"}');  
	});

	 $('.close').live('click',function(){
			   var url = $(this).parents('.box').data('url')
		 		$("#"+$(this).parents('.noindex').attr('id')).effect( "fold", 500, function(){
					$("#"+$(this).parents('.noindex').attr('id')).remove();	
					window.open('index.html?js={"content_url":"'+url+'","type":"close"}'); 
				} ); 	  
	 })
	 
	 $('.pin').live('click',function(){ 
 				sd="1";
				afters="0";
				text=$(this).parents('.box').find('strong').html();
				thisbox=$("#"+$(this).parents('.box').attr("id"))
				$(this).attr("class","nopin");
				$(this).attr("title","取消置顶");
				  window.open('index.html?js={"content_url":"'+$(this).parents('.box').data('url')+'","type":"pin"}'); 
		 		
	 })
	 
	$('.nopin').live('click',function(){
 				sd="1";
				afters="1";
				text=$(this).parents('.box').find('strong').html();
				thisbox=$("#"+$(this).parents('.box').attr("id"))
				$(this).attr("class","pin");
				$(this).attr("title","置顶");
				 window.open('index.html?js={"content_url":"'+$(this).parents('.box').data('url')+'","type":"nopin"}');

	 })
	 
	 
	
$(window).resize(function() {
		winsize(); 
});
 
function winsize(){ 
		if($(window).width()>900){$('.box strong').css("font-size","15px");$('.box span').css("font-size","14px");}else{$('.box strong').css("font-size","13px");$('.box span').css("font-size","12px");};
		
		$(".noindex").each(function (index, domEle) { 
		 	$(domEle).find('strong').text(decodeURIComponent($(domEle).find('strong').text()))
			$(domEle).find('span').text(decodeURIComponent($(domEle).find('span').text()))
			
			$(domEle).find('strong').attr("title",$(domEle).find('strong').text())
			$(domEle).find('span').attr("title",$(domEle).find('span').text())
			 
			if($(domEle).find('strong').height()>42){
				$(domEle).find('.ddd').show();
				$(domEle).find('strong').css("height","40px");
			}
			
			if(data.history[index].ispin==true){
				$(domEle).find('.pin').attr("class","nopin");
			}
		});
		$('.nopin').attr("title","取消置顶");
}	  
 
	 
</script>
 
</body>
</html>
