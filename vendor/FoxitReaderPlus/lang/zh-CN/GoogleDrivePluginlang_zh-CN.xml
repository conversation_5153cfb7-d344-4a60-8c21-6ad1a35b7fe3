<?xml version="1.0" encoding="UTF-8"?>
<Reader version="1.0.1.0414" lastdate="2016//09//13" language="zh-CN" publisher="Foxit">
<Font>
<PreFont FaceName="微软雅黑 Light" Charset="134"/>
</Font>
<Menus>
<menu id="119">
<popup id="|0" text="弹出窗口">
<menuitem id="32774" text="打开"/>
<menuitem id="32771" text="从列表中取消固定"/>
<menuitem id="32772" text="固定到列表"/>
<menuitem id="32773" text="从列表中移除"/>
</popup>
</menu>
</Menus>
<Dialogs>
<Dialog id="106" left="0" top="0" width="625" height="693">
<dlgitem id="3003" text="浏览" left="30" top="550" width="84" height="102"/>
<dlgitem id="3004" text="标题" left="30" top="79" width="474" height="53"/>
<dlgitem id="3005" text="" left="30" top="170" width="565" height="51"/>
<dlgitem id="3006" text="" left="30" top="266" width="565" height="268"/>
<dlgitem id="3007" text="当前文件夹" left="30" top="136" width="217" height="28"/>
<dlgitem id="3008" text="最近的文件夹" left="30" top="232" width="205" height="32"/>
</Dialog>
<Dialog id="107" text="打开" left="0" top="0" width="648" height="633">
<dlgitem id="3009" text="" left="23" top="79" width="595" height="421"/>
<dlgitem id="0" text="" left="0" top="0" width="591" height="24"/>
<dlgitem id="1" text="打开" left="432" top="559" width="88" height="30"/>
<dlgitem id="2" text="取消" left="532" top="559" width="88" height="30"/>
<dlgitem id="3010" text="http://" left="26" top="30" width="592" height="40"/>
<dlgitem id="3011" text="文件类型：" left="12" top="565" width="61" height="17"/>
<dlgitem id="3012" text="" left="84" top="561" width="177" height="25"/>
<dlgitem id="3013" text="文件名称：" left="12" top="521" width="61" height="17"/>
<dlgitem id="3014" text="" left="84" top="519" width="534" height="26"/>
</Dialog>
<Dialog id="2001" left="0" top="0" width="578" height="579">
<dlgitem id="3001" text="&lt;浏览器位置&gt;" left="0" top="0" width="571" height="548"/>
</Dialog>
<Dialog id="2002" text="登录" left="0" top="0" width="522" height="584">
<dlgitem id="3002" text="网页浏览" left="12" top="15" width="492" height="525"/>
<dlgitem id="1001" text="" left="229" top="210" width="35" height="43"/>
<dlgitem id="1002" text="Static" left="12" top="302" width="492" height="17"/>
</Dialog>
</Dialogs>
<Strings>
<string id="1001" text="正在上传文件..."/>
<string id="1002" text="您必须为这个必填项指定一个值。"/>
<string id="1003" text="非法数值。请检查并重试。"/>
<string id="1004" text="非法URL。请检查并重试。"/>
<string id="1005" text="连接到%s。"/>
<string id="1006" text="正在下载文件..."/>
<string id="1007" text="由于网络错误无法从页面URL获取网络URL。\r\n请检查您的网络连接或者联系服务器管理员。\r\n错误码是%d。"/>
<string id="1008" text="由于服务器错误，使用页面链接无法访问网页链接。\r\n请联系服务器管理员。 \r\n状态码是%d。"/>
<string id="1009" text="由于服务器配置原因无法从页面URL获取网络URL。\r\n请联系服务器管理员。"/>
<string id="1010" text="您提供的链接无法访问。请确认\r\n链接是否正确和网络位置是否可以访问。"/>
<string id="1011" text="不能连接到服务器。请检查您的网络连接。\r\n同时，请检查URL格式是否正确并确保您拥有访问该资源的权限。"/>
<string id="1012" text="由于网络错误无法获取URL数据段。\r\n请检查您的网络连接或者联系服务器管理员。\r\n错误码是%d。"/>
<string id="1013" text="由于服务器错误无法获取URL数据段。\r\n请联系服务器管理员。\r\n状态码是%d。"/>
<string id="1014" text="由于某些错误无法获取URL数据段。\r\n请联系服务器管理员。"/>
<string id="1015" text="该操作需要安全连接和服务器认证。\r\n该网站的证书颁发者不受信任或者未知。\r\n您要继续吗？"/>
<string id="1018" text="该文件不是PDF文件，无法打开。"/>
<string id="1019" text="打开"/>
<string id="1020" text="另存为"/>
<string id="1021" text="所有文件"/>
<string id="1022" text="PDF文件(*.pdf)"/>
<string id="1023" text="PPDF 文件(*.ppdf)"/>
<string id="1024" text="名称"/>
<string id="1025" text="修改日期"/>
<string id="1026" text="大小"/>
<string id="1027" text="..父目录"/>
<string id="1028" text="保存"/>
<string id="1029" text="正在加载“&apos;%s”"/>
<string id="1030" text="由于网络错误，该文档无法下载。\r\n请检查您的网络链接或者联系服务器管理员。\r\n错误码是%d。"/>
<string id="1031" text="由于服务器错误，该文档无法下载。\r\n请联系服务器管理员。 \r\n状态码是%d。"/>
<string id="1033" text="由于网络错误，该文档无法上传。\r\n请检查您的网络链接或者联系服务器管理员。\r\n错误码是%d。"/>
<string id="1034" text="由于服务器错误，该文档不能上传。\r\n请联系服务器管理员。 \r\n状态码是%d。"/>
<string id="1035" text="遇到未知错误，文档无法上传。"/>
<string id="1036" text="Google Drive"/>
<string id="1037" text="当我们正在加载登录的时候，请等待。"/>
<string id="1038" text="您是否想要移除该帐户？"/>
<string id="1039" text="登录"/>
<string id="1040" text="该文件夹已经被删除。"/>
</Strings>
<Version>
<VerDes name="FileDescription" value="Google Drive 插件是一种容易的可通过福昕阅读器或者福昕PDF编辑套件对Google Drive上的PDF文件进行管理和读取的方式。" />
<VerLeg name="LegalCopyright" value="2012-2016 福昕软件 版权所有。" />
</Version>
</Reader>
