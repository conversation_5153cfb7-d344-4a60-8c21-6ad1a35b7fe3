<?xml version="1.0"?>
<doc>
    <assembly>
        <name>itext.layout</name>
    </assembly>
    <members>
        <member name="T:iText.Layout.Borders.Border">
            <summary>Represents a border.</summary>
        </member>
        <member name="F:iText.Layout.Borders.Border.SOLID">
            <summary>The solid border.</summary>
            <seealso cref="T:iText.Layout.Borders.SolidBorder"/>
        </member>
        <member name="F:iText.Layout.Borders.Border.DASHED">
            <summary>The dashed border.</summary>
            <seealso cref="T:iText.Layout.Borders.DashedBorder"/>
        </member>
        <member name="F:iText.Layout.Borders.Border.DOTTED">
            <summary>The dotted border.</summary>
            <seealso cref="T:iText.Layout.Borders.DottedBorder"/>
        </member>
        <member name="F:iText.Layout.Borders.Border.DOUBLE">
            <summary>The double border.</summary>
            <seealso cref="T:iText.Layout.Borders.DoubleBorder"/>
        </member>
        <member name="F:iText.Layout.Borders.Border.ROUND_DOTS">
            <summary>The round-dots border.</summary>
            <seealso cref="T:iText.Layout.Borders.RoundDotsBorder"/>
        </member>
        <member name="F:iText.Layout.Borders.Border._3D_GROOVE">
            <summary>The 3D groove border.</summary>
            <seealso cref="T:iText.Layout.Borders.GrooveBorder"/>
        </member>
        <member name="F:iText.Layout.Borders.Border._3D_INSET">
            <summary>The 3D inset border.</summary>
            <seealso cref="T:iText.Layout.Borders.InsetBorder"/>
        </member>
        <member name="F:iText.Layout.Borders.Border._3D_OUTSET">
            <summary>The 3D outset border.</summary>
            <seealso cref="T:iText.Layout.Borders.OutsetBorder"/>
        </member>
        <member name="F:iText.Layout.Borders.Border._3D_RIDGE">
            <summary>The 3D ridge border.</summary>
            <seealso cref="T:iText.Layout.Borders.RidgeBorder"/>
        </member>
        <member name="F:iText.Layout.Borders.Border.NO_BORDER">
            <summary>The null Border, i.e.</summary>
            <remarks>The null Border, i.e. the presence of such border is equivalent to the absence of the border</remarks>
        </member>
        <member name="F:iText.Layout.Borders.Border.color">
            <summary>The color of the border.</summary>
            <seealso cref="T:iText.Kernel.Colors.Color"/>
        </member>
        <member name="F:iText.Layout.Borders.Border.transparentColor">
            <summary>The color of the border.</summary>
            <seealso cref="T:iText.Layout.Properties.TransparentColor"/>
        </member>
        <member name="F:iText.Layout.Borders.Border.width">
            <summary>The width of the border.</summary>
        </member>
        <member name="F:iText.Layout.Borders.Border.type">
            <summary>The type of the border.</summary>
        </member>
        <member name="F:iText.Layout.Borders.Border.hash">
            <summary>The hash value for the border.</summary>
        </member>
        <member name="M:iText.Layout.Borders.Border.#ctor(System.Single)">
            <summary>
            Creates a
            <see cref="T:iText.Layout.Borders.Border">border</see>
            with the given width.
            The
            <see cref="T:iText.Kernel.Colors.Color">color</see>
            to be set by default is black
            </summary>
            <param name="width">the width which the border should have</param>
        </member>
        <member name="M:iText.Layout.Borders.Border.#ctor(iText.Kernel.Colors.Color,System.Single)">
            <summary>
            Creates a
            <see cref="T:iText.Layout.Borders.Border">border</see>
            with given width and
            <see cref="T:iText.Kernel.Colors.Color">color</see>
            .
            </summary>
            <param name="color">the color which the border should have</param>
            <param name="width">the width which the border should have</param>
        </member>
        <member name="M:iText.Layout.Borders.Border.#ctor(iText.Kernel.Colors.Color,System.Single,System.Single)">
            <summary>
            Creates a
            <see cref="T:iText.Layout.Borders.Border">border</see>
            with given width,
            <see cref="T:iText.Kernel.Colors.Color">color</see>
            and opacity.
            </summary>
            <param name="color">the color which the border should have</param>
            <param name="width">the width which the border should have</param>
            <param name="opacity">the opacity which border should have; a float between 0 and 1, where 1 stands for fully opaque color and 0 - for fully transparent
                </param>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Layout.Borders.Border.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)" -->
        <!-- Badly formed XML comment ignored for member "M:iText.Layout.Borders.Border.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,iText.Layout.Borders.Border.Side,System.Single,System.Single)" -->
        <!-- Badly formed XML comment ignored for member "M:iText.Layout.Borders.Border.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,System.Single,iText.Layout.Borders.Border.Side,System.Single,System.Single)" -->
        <member name="M:iText.Layout.Borders.Border.DrawCellBorder(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single)">
            <summary>Draws the border of a cell.</summary>
            <param name="canvas">PdfCanvas to be written to</param>
            <param name="x1">x coordinate of the beginning point of the element side, that should be bordered</param>
            <param name="y1">y coordinate of the beginning point of the element side, that should be bordered</param>
            <param name="x2">x coordinate of the ending point of the element side, that should be bordered</param>
            <param name="y2">y coordinate of the ending point of the element side, that should be bordered</param>
        </member>
        <member name="M:iText.Layout.Borders.Border.GetBorderType">
            <summary>
            Returns the type of the
            <see cref="T:iText.Layout.Borders.Border">border</see>
            </summary>
        </member>
        <member name="M:iText.Layout.Borders.Border.GetColor">
            <summary>
            Gets the
            <see cref="T:iText.Kernel.Colors.Color">color</see>
            of the
            <see cref="T:iText.Layout.Borders.Border">border</see>
            </summary>
            <returns>
            the
            <see cref="T:iText.Kernel.Colors.Color">color</see>
            </returns>
        </member>
        <member name="M:iText.Layout.Borders.Border.GetOpacity">
            <summary>
            Gets the opacity of the
            <see cref="T:iText.Layout.Borders.Border">border</see>
            </summary>
            <returns>the border opacity; a float between 0 and 1, where 1 stands for fully opaque color and 0 - for fully transparent
                </returns>
        </member>
        <member name="M:iText.Layout.Borders.Border.GetWidth">
            <summary>
            Gets the width of the
            <see cref="T:iText.Layout.Borders.Border">border</see>
            </summary>
            <returns>the width</returns>
        </member>
        <member name="M:iText.Layout.Borders.Border.SetColor(iText.Kernel.Colors.Color)">
            <summary>
            Sets the
            <see cref="T:iText.Kernel.Colors.Color">color</see>
            of the
            <see cref="T:iText.Layout.Borders.Border">border</see>
            </summary>
        </member>
        <member name="M:iText.Layout.Borders.Border.SetWidth(System.Single)">
            <summary>
            Sets the width of the
            <see cref="T:iText.Layout.Borders.Border">border</see>
            </summary>
        </member>
        <member name="M:iText.Layout.Borders.Border.Equals(System.Object)">
            <summary>Indicates whether the border is equal to the given border.</summary>
            <remarks>
            Indicates whether the border is equal to the given border.
            The border type, width and color are considered during the comparison.
            </remarks>
        </member>
        <member name="M:iText.Layout.Borders.Border.GetHashCode">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.Border.GetBorderSide(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Returns the
            <see cref="T:iText.Layout.Borders.Border.Side">side</see>
            corresponded to the line between two points.
            Notice that we consider the rectangle traversal to be clockwise.
            If the rectangle sides are not parallel to the corresponding page sides
            the result is Side.NONE
            </summary>
            <param name="x1">the abscissa of the left-bottom point</param>
            <param name="y1">the ordinate of the left-bottom point</param>
            <param name="x2">the abscissa of the right-top point</param>
            <param name="y2">the ordinate of the right-top point</param>
            <returns>
            the corresponded
            <see cref="T:iText.Layout.Borders.Border.Side">side</see>
            </returns>
        </member>
        <member name="T:iText.Layout.Borders.Border.Side">
            <summary>Enumerates the different sides of the rectangle.</summary>
            <remarks>
            Enumerates the different sides of the rectangle.
            The rectangle sides are expected to be parallel to corresponding page sides
            Otherwise the result is Side.NONE
            </remarks>
        </member>
        <member name="T:iText.Layout.Borders.Border3D">
            <summary>Represents a border that is displayed using a 3D effect.</summary>
        </member>
        <member name="F:iText.Layout.Borders.Border3D.GRAY">
            <summary>
            Predefined gray
            <see cref="T:iText.Kernel.Colors.DeviceRgb">RGB-color</see>
            </summary>
        </member>
        <member name="M:iText.Layout.Borders.Border3D.#ctor(System.Single)">
            <summary>Creates a Border3D instance with the specified width.</summary>
            <remarks>Creates a Border3D instance with the specified width. Also sets the color to gray.</remarks>
            <param name="width">with of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.Border3D.#ctor(iText.Kernel.Colors.DeviceRgb,System.Single)">
            <summary>Creates a Border3D instance with the specified width and color.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.Border3D.#ctor(iText.Kernel.Colors.DeviceCmyk,System.Single)">
            <summary>Creates a Border3D instance with the specified width and color.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.Border3D.#ctor(iText.Kernel.Colors.DeviceGray,System.Single)">
            <summary>Creates a Border3D instance with the specified width and color.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.Border3D.#ctor(iText.Kernel.Colors.DeviceRgb,System.Single,System.Single)">
            <summary>Creates a Border3D instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.Border3D.#ctor(iText.Kernel.Colors.DeviceCmyk,System.Single,System.Single)">
            <summary>Creates a Border3D instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.Border3D.#ctor(iText.Kernel.Colors.DeviceGray,System.Single,System.Single)">
            <summary>Creates a Border3D instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.Border3D.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.Border3D.DrawCellBorder(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.Border3D.GetDarkerColor">
            <summary>
            Makes the
            <see cref="F:iText.Layout.Borders.Border.transparentColor"/>
            color of the border darker and returns the result
            </summary>
        </member>
        <member name="M:iText.Layout.Borders.Border3D.SetInnerHalfColor(iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Layout.Borders.Border.Side)">
            <summary>
            Sets the fill color for the inner half of
            <see cref="T:iText.Layout.Borders.Border3D">3D Border</see>
            </summary>
            <param name="canvas">PdfCanvas the color will be applied on</param>
            <param name="side">
            the
            <see cref="!:Side">side</see>
            the color will be applied on
            </param>
        </member>
        <member name="M:iText.Layout.Borders.Border3D.SetOuterHalfColor(iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Layout.Borders.Border.Side)">
            <summary>
            Sets the fill color for the outer half of
            <see cref="T:iText.Layout.Borders.Border3D">3D Border</see>
            </summary>
            <param name="canvas">PdfCanvas the color will be applied on</param>
            <param name="side">
            the
            <see cref="!:Side">side</see>
            the color will be applied on
            </param>
        </member>
        <member name="T:iText.Layout.Borders.DashedBorder">
            <summary>Draws a border with dashes around the element it's been set to.</summary>
        </member>
        <member name="F:iText.Layout.Borders.DashedBorder.DASH_MODIFIER">
            <summary>The modifier to be applied on the width to have the dash size</summary>
        </member>
        <member name="F:iText.Layout.Borders.DashedBorder.GAP_MODIFIER">
            <summary>The modifier to be applied on the width to have the initial gap size</summary>
        </member>
        <member name="M:iText.Layout.Borders.DashedBorder.#ctor(System.Single)">
            <summary>Creates a DashedBorder with the specified width and sets the color to black.</summary>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.DashedBorder.#ctor(iText.Kernel.Colors.Color,System.Single)">
            <summary>Creates a DashedBorder with the specified width and the specified color.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.DashedBorder.#ctor(iText.Kernel.Colors.Color,System.Single,System.Single)">
            <summary>Creates a DashedBorder with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.DashedBorder.GetBorderType">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.DashedBorder.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.DashedBorder.DrawCellBorder(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.DashedBorder.GetDotsGap(System.Double,System.Single)">
            <summary>Adjusts the size of the gap between dots</summary>
            <param name="distance">
            the
            <see cref="T:iText.Layout.Borders.Border">border</see>
            length
            </param>
            <param name="initialGap">the initial size of the gap</param>
            <returns>the adjusted size of the gap</returns>
        </member>
        <member name="T:iText.Layout.Borders.DottedBorder">
            <summary>Draws a dotted border around the element it has been set to.</summary>
            <remarks>
            Draws a dotted border around the element it has been set to. Do note that this border draw square dots,
            if you want to draw round dots, see
            <see cref="T:iText.Layout.Borders.RoundDotsBorder"/>
            .
            </remarks>
        </member>
        <member name="F:iText.Layout.Borders.DottedBorder.GAP_MODIFIER">
            <summary>The modifier to be applied on the width to have the initial gap size</summary>
        </member>
        <member name="M:iText.Layout.Borders.DottedBorder.#ctor(System.Single)">
            <summary>Creates a DottedBorder instance with the specified width.</summary>
            <remarks>Creates a DottedBorder instance with the specified width. The color is set to the default: black.
                </remarks>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.DottedBorder.#ctor(iText.Kernel.Colors.Color,System.Single)">
            <summary>Creates a DottedBorder instance with the specified width and color.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.DottedBorder.#ctor(iText.Kernel.Colors.Color,System.Single,System.Single)">
            <summary>Creates a DottedBorder with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.DottedBorder.GetBorderType">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.DottedBorder.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.DottedBorder.DrawCellBorder(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.DottedBorder.GetDotsGap(System.Double,System.Single)">
            <summary>Adjusts the size of the gap between dots</summary>
            <param name="distance">
            the
            <see cref="T:iText.Layout.Borders.Border">border</see>
            length
            </param>
            <param name="initialGap">the initial size of the gap</param>
            <returns>the adjusted size of the gap</returns>
        </member>
        <member name="T:iText.Layout.Borders.DoubleBorder">
            <summary>Creates a double border around the element it's set to.</summary>
            <remarks>
            Creates a double border around the element it's set to. The space between the two border lines has
            the same width as the two borders. If a background has been set on the element the color will show in
            between the two borders.
            </remarks>
        </member>
        <member name="M:iText.Layout.Borders.DoubleBorder.#ctor(System.Single)">
            <summary>Creates a DoubleBorder with the specified width for both the two borders as the space in between them.
                </summary>
            <remarks>
            Creates a DoubleBorder with the specified width for both the two borders as the space in between them.
            The color is set to the default: black.
            </remarks>
            <param name="width">width of the borders and the space between them</param>
        </member>
        <member name="M:iText.Layout.Borders.DoubleBorder.#ctor(iText.Kernel.Colors.Color,System.Single)">
            <summary>
            Creates a DoubleBorder with the specified width for both the two borders as the space in between them and
            the specified color for the two borders.
            </summary>
            <remarks>
            Creates a DoubleBorder with the specified width for both the two borders as the space in between them and
            the specified color for the two borders. The space in between the two borders is either colorless or will
            be filled with the background color of the element, if a color has been set.
            </remarks>
            <param name="width">width of the borders and the space between them</param>
        </member>
        <member name="M:iText.Layout.Borders.DoubleBorder.#ctor(iText.Kernel.Colors.Color,System.Single,System.Single)">
            <summary>
            Creates a DoubleBorder with the specified width for both the two borders as the space in between them and
            the specified color for the two borders.
            </summary>
            <remarks>
            Creates a DoubleBorder with the specified width for both the two borders as the space in between them and
            the specified color for the two borders. The space in between the two borders is either colorless or will
            be filled with the background color of the element, if a color has been set.
            </remarks>
            <param name="width">width of the borders and the space between them</param>
        </member>
        <member name="M:iText.Layout.Borders.DoubleBorder.GetBorderType">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.DoubleBorder.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.DoubleBorder.DrawCellBorder(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.GrooveBorder.#ctor(System.Single)">
            <summary>Creates a GrooveBorder instance with the specified width.</summary>
            <remarks>
            Creates a GrooveBorder instance with the specified width. The color is set to the default:
            <see cref="!:Border3D#GRAY">gray</see>
            .
            </remarks>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.GrooveBorder.#ctor(iText.Kernel.Colors.DeviceRgb,System.Single)">
            <summary>
            Creates a GrooveBorder instance with the specified width and the
            <see cref="T:iText.Kernel.Colors.DeviceRgb">rgb color</see>
            .
            </summary>
            <param name="width">width of the border</param>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.DeviceRgb">rgb color</see>
            of the border
            </param>
        </member>
        <member name="M:iText.Layout.Borders.GrooveBorder.#ctor(iText.Kernel.Colors.DeviceCmyk,System.Single)">
            <summary>
            Creates a GrooveBorder instance with the specified width and the
            <see cref="T:iText.Kernel.Colors.DeviceCmyk">cmyk color</see>
            .
            </summary>
            <param name="width">width of the border</param>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.DeviceCmyk">cmyk color</see>
            of the border
            </param>
        </member>
        <member name="M:iText.Layout.Borders.GrooveBorder.#ctor(iText.Kernel.Colors.DeviceGray,System.Single)">
            <summary>
            Creates a GrooveBorder instance with the specified width and the
            <see cref="T:iText.Kernel.Colors.DeviceGray">gray color</see>
            .
            </summary>
            <param name="width">width of the border</param>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.DeviceGray">gray color</see>
            of the border
            </param>
        </member>
        <member name="M:iText.Layout.Borders.GrooveBorder.#ctor(iText.Kernel.Colors.DeviceRgb,System.Single,System.Single)">
            <summary>Creates a GrooveBorder instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.GrooveBorder.#ctor(iText.Kernel.Colors.DeviceCmyk,System.Single,System.Single)">
            <summary>Creates a GrooveBorder instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.GrooveBorder.#ctor(iText.Kernel.Colors.DeviceGray,System.Single,System.Single)">
            <summary>Creates a GrooveBorder instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.GrooveBorder.GetBorderType">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.GrooveBorder.SetInnerHalfColor(iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Layout.Borders.Border.Side)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.GrooveBorder.SetOuterHalfColor(iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Layout.Borders.Border.Side)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.InsetBorder.#ctor(System.Single)">
            <summary>Creates a InsetBorder instance with the specified width.</summary>
            <remarks>
            Creates a InsetBorder instance with the specified width. The color is set to the default:
            <see cref="!:Border3D#GRAY">gray</see>
            .
            </remarks>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.InsetBorder.#ctor(iText.Kernel.Colors.DeviceRgb,System.Single)">
            <summary>
            Creates a InsetBorder instance with the specified width and the
            <see cref="T:iText.Kernel.Colors.DeviceRgb">rgb color</see>
            .
            </summary>
            <param name="width">width of the border</param>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.DeviceRgb">rgb color</see>
            of the border
            </param>
        </member>
        <member name="M:iText.Layout.Borders.InsetBorder.#ctor(iText.Kernel.Colors.DeviceCmyk,System.Single)">
            <summary>
            Creates a InsetBorder instance with the specified width and the
            <see cref="T:iText.Kernel.Colors.DeviceCmyk">cmyk color</see>
            .
            </summary>
            <param name="width">width of the border</param>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.DeviceCmyk">cmyk color</see>
            of the border
            </param>
        </member>
        <member name="M:iText.Layout.Borders.InsetBorder.#ctor(iText.Kernel.Colors.DeviceGray,System.Single)">
            <summary>
            Creates a InsetBorder instance with the specified width and the
            <see cref="T:iText.Kernel.Colors.DeviceGray">gray color</see>
            .
            </summary>
            <param name="width">width of the border</param>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.DeviceGray">gray color</see>
            of the border
            </param>
        </member>
        <member name="M:iText.Layout.Borders.InsetBorder.#ctor(iText.Kernel.Colors.DeviceRgb,System.Single,System.Single)">
            <summary>Creates a InsetBorder instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.InsetBorder.#ctor(iText.Kernel.Colors.DeviceCmyk,System.Single,System.Single)">
            <summary>Creates a InsetBorder instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.InsetBorder.#ctor(iText.Kernel.Colors.DeviceGray,System.Single,System.Single)">
            <summary>Creates a InsetBorder instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.InsetBorder.GetBorderType">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.InsetBorder.SetInnerHalfColor(iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Layout.Borders.Border.Side)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.InsetBorder.SetOuterHalfColor(iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Layout.Borders.Border.Side)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.OutsetBorder.#ctor(System.Single)">
            <summary>Creates a OutsetBorder instance with the specified width.</summary>
            <remarks>
            Creates a OutsetBorder instance with the specified width. The color is set to the default:
            <see cref="!:Border3D#GRAY">gray</see>
            .
            </remarks>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.OutsetBorder.#ctor(iText.Kernel.Colors.DeviceRgb,System.Single)">
            <summary>
            Creates a OutsetBorder instance with the specified width and the
            <see cref="T:iText.Kernel.Colors.DeviceRgb">rgb color</see>
            .
            </summary>
            <param name="width">width of the border</param>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.DeviceRgb">rgb color</see>
            of the border
            </param>
        </member>
        <member name="M:iText.Layout.Borders.OutsetBorder.#ctor(iText.Kernel.Colors.DeviceCmyk,System.Single)">
            <summary>
            Creates a OutsetBorder instance with the specified width and the
            <see cref="T:iText.Kernel.Colors.DeviceCmyk">cmyk color</see>
            .
            </summary>
            <param name="width">width of the border</param>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.DeviceCmyk">cmyk color</see>
            of the border
            </param>
        </member>
        <member name="M:iText.Layout.Borders.OutsetBorder.#ctor(iText.Kernel.Colors.DeviceGray,System.Single)">
            <summary>
            Creates a OutsetBorder instance with the specified width and the
            <see cref="T:iText.Kernel.Colors.DeviceGray">gray color</see>
            .
            </summary>
            <param name="width">width of the border</param>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.DeviceGray">gray color</see>
            of the border
            </param>
        </member>
        <member name="M:iText.Layout.Borders.OutsetBorder.#ctor(iText.Kernel.Colors.DeviceRgb,System.Single,System.Single)">
            <summary>Creates a OutsetBorder instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.OutsetBorder.#ctor(iText.Kernel.Colors.DeviceCmyk,System.Single,System.Single)">
            <summary>Creates a OutsetBorder instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.OutsetBorder.#ctor(iText.Kernel.Colors.DeviceGray,System.Single,System.Single)">
            <summary>Creates a OutsetBorder instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.OutsetBorder.GetBorderType">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.OutsetBorder.SetInnerHalfColor(iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Layout.Borders.Border.Side)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.OutsetBorder.SetOuterHalfColor(iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Layout.Borders.Border.Side)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.RidgeBorder.#ctor(System.Single)">
            <summary>Creates a RidgeBorder instance with the specified width.</summary>
            <remarks>
            Creates a RidgeBorder instance with the specified width. The color is set to the default:
            <see cref="!:Border3D#GRAY">gray</see>
            .
            </remarks>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.RidgeBorder.#ctor(iText.Kernel.Colors.DeviceRgb,System.Single)">
            <summary>
            Creates a RidgeBorder instance with the specified width and the
            <see cref="T:iText.Kernel.Colors.DeviceRgb">rgb color</see>
            .
            </summary>
            <param name="width">width of the border</param>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.DeviceRgb">rgb color</see>
            of the border
            </param>
        </member>
        <member name="M:iText.Layout.Borders.RidgeBorder.#ctor(iText.Kernel.Colors.DeviceCmyk,System.Single)">
            <summary>
            Creates a RidgeBorder instance with the specified width and the
            <see cref="T:iText.Kernel.Colors.DeviceCmyk">cmyk color</see>
            .
            </summary>
            <param name="width">width of the border</param>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.DeviceCmyk">cmyk color</see>
            of the border
            </param>
        </member>
        <member name="M:iText.Layout.Borders.RidgeBorder.#ctor(iText.Kernel.Colors.DeviceGray,System.Single)">
            <summary>
            Creates a RidgeBorder instance with the specified width and the
            <see cref="T:iText.Kernel.Colors.DeviceGray">gray color</see>
            .
            </summary>
            <param name="width">width of the border</param>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.DeviceGray">gray color</see>
            of the border
            </param>
        </member>
        <member name="M:iText.Layout.Borders.RidgeBorder.#ctor(iText.Kernel.Colors.DeviceRgb,System.Single,System.Single)">
            <summary>Creates a RidgeBorder instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.RidgeBorder.#ctor(iText.Kernel.Colors.DeviceCmyk,System.Single,System.Single)">
            <summary>Creates a RidgeBorder instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.RidgeBorder.#ctor(iText.Kernel.Colors.DeviceGray,System.Single,System.Single)">
            <summary>Creates a RidgeBorder instance with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">opacity of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.RidgeBorder.GetBorderType">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.RidgeBorder.SetInnerHalfColor(iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Layout.Borders.Border.Side)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.RidgeBorder.SetOuterHalfColor(iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Layout.Borders.Border.Side)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Layout.Borders.RoundDotsBorder">
            <summary>Draws a border with rounded dots around the element it's been set to.</summary>
            <remarks>
            Draws a border with rounded dots around the element it's been set to. For square dots see
            <see cref="T:iText.Layout.Borders.DottedBorder"/>
            .
            </remarks>
        </member>
        <member name="F:iText.Layout.Borders.RoundDotsBorder.GAP_MODIFIER">
            <summary>The modifier to be applied on the width to have the initial gap size</summary>
        </member>
        <member name="M:iText.Layout.Borders.RoundDotsBorder.#ctor(System.Single)">
            <summary>Creates a RoundDotsBorder with the specified wit?dth and sets the color to black.</summary>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.RoundDotsBorder.#ctor(iText.Kernel.Colors.Color,System.Single)">
            <summary>Creates a RoundDotsBorder with the specified wit?dth and the specified color.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.RoundDotsBorder.#ctor(iText.Kernel.Colors.Color,System.Single,System.Single)">
            <summary>Creates a RoundDotsBorder with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.RoundDotsBorder.GetBorderType">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.RoundDotsBorder.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.RoundDotsBorder.DrawCellBorder(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.RoundDotsBorder.GetDotsGap(System.Double,System.Single)">
            <summary>Adjusts the size of the gap between dots</summary>
            <param name="distance">
            the
            <see cref="T:iText.Layout.Borders.Border">border</see>
            length
            </param>
            <param name="initialGap">the initial size of the gap</param>
            <returns>the adjusted size of the gap</returns>
        </member>
        <member name="T:iText.Layout.Borders.SolidBorder">
            <summary>Draws a solid border around the element it's set to.</summary>
        </member>
        <member name="M:iText.Layout.Borders.SolidBorder.#ctor(System.Single)">
            <summary>Creates a SolidBorder with the specified width and sets the color to black.</summary>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.SolidBorder.#ctor(iText.Kernel.Colors.Color,System.Single)">
            <summary>Creates a SolidBorder with the specified width and the specified color.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.SolidBorder.#ctor(iText.Kernel.Colors.Color,System.Single,System.Single)">
            <summary>Creates a SolidBorder with the specified width, color and opacity.</summary>
            <param name="color">color of the border</param>
            <param name="width">width of the border</param>
            <param name="opacity">width of the border</param>
        </member>
        <member name="M:iText.Layout.Borders.SolidBorder.GetBorderType">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.SolidBorder.Draw(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Borders.SolidBorder.DrawCellBorder(iText.Kernel.Pdf.Canvas.PdfCanvas,System.Single,System.Single,System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Layout.Canvas">
            <summary>
            This class is used for adding content directly onto a specified
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            .
            <see cref="T:iText.Layout.Canvas"/>
            does not know the concept of a page, so it can't reflow to a 'next'
            <see cref="T:iText.Layout.Canvas"/>
            .
            This class effectively acts as a bridge between the high-level <em>layout</em>
            API and the low-level <em>kernel</em> API.
            </summary>
        </member>
        <member name="T:iText.Layout.RootElement`1">
            <summary>A generic abstract root element for a PDF layout object hierarchy.</summary>
            
        </member>
        <member name="T:iText.Layout.ElementPropertyContainer`1">
            <summary>A generic abstract element that fits in a PDF layout object hierarchy.</summary>
            <remarks>
            A generic abstract element that fits in a PDF layout object hierarchy.
            A superclass of all
            <see cref="T:iText.Layout.Element.IElement">layout object</see>
            implementations.
            </remarks>
            
        </member>
        <member name="T:iText.Layout.IPropertyContainer">
            <summary>
            A generic Map-like interface that defines methods for storing and retrieving
            objects by an enum key of the
            <see cref="T:iText.Layout.Properties.Property"/>
            type.
            </summary>
        </member>
        <member name="M:iText.Layout.IPropertyContainer.HasProperty(System.Int32)">
            <summary>Checks if this entity has the specified property.</summary>
            <remarks>
            Checks if this entity has the specified property. Compared to
            <see cref="M:iText.Layout.IPropertyContainer.HasOwnProperty(System.Int32)"/>
            ,
            this method can check parent's properties, styles, etc, depending on the origin of the instance
            </remarks>
            <param name="property">the property to be checked</param>
            <returns>
            
            <see langword="true"/>
            if this instance has given property,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Layout.IPropertyContainer.HasOwnProperty(System.Int32)">
            <summary>Checks if this entity has the specified property, i.e.</summary>
            <remarks>Checks if this entity has the specified property, i.e. if it was set to this very element earlier
                </remarks>
            <param name="property">the property to be checked</param>
            <returns>
            
            <see langword="true"/>
            if this instance has given own property,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Layout.IPropertyContainer.GetProperty``1(System.Int32)">
            <summary>Gets the property from this entity.</summary>
            <remarks>
            Gets the property from this entity. Compared to
            <see cref="M:iText.Layout.IPropertyContainer.GetOwnProperty``1(System.Int32)"/>
            ,
            this method can check parent's properties, styles, etc, depending on the origin of the instance
            </remarks>
            
            <param name="property">the property to be retrieved</param>
            <returns>
            the value of the given property.
            <see langword="null"/>
            will be returned if the property value was not found
            </returns>
        </member>
        <member name="M:iText.Layout.IPropertyContainer.GetOwnProperty``1(System.Int32)">
            <summary>Gets own property from this entity.</summary>
            <remarks>
            Gets own property from this entity. The property must have been set earlier to this entity.
            If the property is not found,
            <see langword="null"/>
            will be returned.
            </remarks>
            
            <param name="property">the property to be retrieved</param>
            <returns>
            the value of the given own property.
            <see langword="null"/>
            will be returned if the property value was not found
            </returns>
        </member>
        <member name="M:iText.Layout.IPropertyContainer.GetDefaultProperty``1(System.Int32)">
            <summary>Gets the default property from this entity.</summary>
            
            <param name="property">the property to be retrieved</param>
            <returns>
            the default property value. If the default property is not defined,
            <see langword="null"/>
            will be returned
            </returns>
        </member>
        <member name="M:iText.Layout.IPropertyContainer.SetProperty(System.Int32,System.Object)">
            <summary>Sets a property for this entity.</summary>
            <param name="property">the property to be set</param>
            <param name="value">the value of the property</param>
        </member>
        <member name="M:iText.Layout.IPropertyContainer.DeleteOwnProperty(System.Int32)">
            <summary>Deletes the own property of this entity.</summary>
            <param name="property">the property to be deleted</param>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.GetWidth">
            <summary>Gets the width property of the Element.</summary>
            <returns>the width of the element, with a value and a measurement unit.</returns>
            <seealso cref="T:iText.Layout.Properties.UnitValue"/>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetWidth(System.Single)">
            <summary>Sets the width property of the Element, measured in points.</summary>
            <param name="width">a value measured in points.</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetWidthPercent(System.Single)">
            <summary>Sets the width property of the Element, measured in percentage.</summary>
            <param name="widthPercent">a value measured in percentage.</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetWidth(iText.Layout.Properties.UnitValue)">
            <summary>
            Sets the width property of the Element with a
            <see cref="T:iText.Layout.Properties.UnitValue"/>
            .
            </summary>
            <param name="width">
            a
            <see cref="T:iText.Layout.Properties.UnitValue"/>
            object
            </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.GetHeight">
            <summary>Gets the height property of the Element.</summary>
            <returns>the height of the element, as a floating point value.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetHeight(System.Single)">
            <summary>Sets the height property of the Element.</summary>
            <param name="height">a floating point value for the new height</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetRelativePosition(System.Single,System.Single,System.Single,System.Single)">
            <summary>Sets values for a relative repositioning of the Element.</summary>
            <remarks>
            Sets values for a relative repositioning of the Element. Also has as a
            side effect that the Element's
            <see cref="F:iText.Layout.Properties.Property.POSITION"/>
            is changed to
            <see cref="F:iText.Layout.Layout.LayoutPosition.RELATIVE">relative</see>
            .
            The default implementation in
            <see cref="T:iText.Layout.Renderer.AbstractRenderer"/>
            treats
            <code>left</code> and <code>top</code> as the most important values. Only
            if <code>left == 0</code> will <code>right</code> be used for the
            calculation; ditto for top vs. bottom.
            </remarks>
            <param name="left">movement to the left</param>
            <param name="top">movement upwards on the page</param>
            <param name="right">movement to the right</param>
            <param name="bottom">movement downwards on the page</param>
            <returns>this Element.</returns>
            <seealso cref="F:iText.Layout.Layout.LayoutPosition.RELATIVE"/>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetFixedPosition(System.Single,System.Single,System.Single)">
            <summary>Sets values for a absolute repositioning of the Element.</summary>
            <remarks>
            Sets values for a absolute repositioning of the Element.
            The coordinates specified correspond to the
            bottom-left corner of the element and it grows upwards.
            Also has as a side effect that the Element's
            <see cref="F:iText.Layout.Properties.Property.POSITION"/>
            is changed to
            <see cref="F:iText.Layout.Layout.LayoutPosition.FIXED">fixed</see>
            .
            </remarks>
            <param name="x">horizontal position of the bottom-left corner on the page</param>
            <param name="y">vertical position of the bottom-left corner on the page</param>
            <param name="width">a floating point value measured in points.</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetFixedPosition(System.Single,System.Single,iText.Layout.Properties.UnitValue)">
            <summary>Sets values for a absolute repositioning of the Element.</summary>
            <remarks>
            Sets values for a absolute repositioning of the Element.
            The coordinates specified correspond to the
            bottom-left corner of the element and it grows upwards.
            Also has as a side effect that the Element's
            <see cref="F:iText.Layout.Properties.Property.POSITION"/>
            is changed to
            <see cref="F:iText.Layout.Layout.LayoutPosition.FIXED">fixed</see>
            .
            </remarks>
            <param name="x">horizontal position of the bottom-left corner on the page</param>
            <param name="y">vertical position of the bottom-left corner on the page</param>
            <param name="width">
            a
            <see cref="T:iText.Layout.Properties.UnitValue"/>
            </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetFixedPosition(System.Int32,System.Single,System.Single,System.Single)">
            <summary>Sets values for a absolute repositioning of the Element.</summary>
            <remarks>
            Sets values for a absolute repositioning of the Element.
            The coordinates specified correspond to the
            bottom-left corner of the element and it grows upwards.
            Also has as a side effect that the Element's
            <see cref="F:iText.Layout.Properties.Property.POSITION"/>
            is changed to
            <see cref="F:iText.Layout.Layout.LayoutPosition.FIXED">fixed</see>
            .
            </remarks>
            <param name="pageNumber">the page where the element must be positioned</param>
            <param name="x">horizontal position of the bottom-left corner on the page</param>
            <param name="y">vertical position of the bottom-left corner on the page</param>
            <param name="width">a floating point value measured in points.</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetFixedPosition(System.Int32,System.Single,System.Single,iText.Layout.Properties.UnitValue)">
            <summary>Sets values for a absolute repositioning of the Element.</summary>
            <remarks>
            Sets values for a absolute repositioning of the Element.
            The coordinates specified correspond to the
            bottom-left corner of the element and it grows upwards.
            Also has as a side effect that the Element's
            <see cref="F:iText.Layout.Properties.Property.POSITION"/>
            is changed to
            <see cref="F:iText.Layout.Layout.LayoutPosition.FIXED">fixed</see>
            .
            </remarks>
            <param name="pageNumber">the page where the element must be positioned</param>
            <param name="x">horizontal position of the bottom-left corner on the page</param>
            <param name="y">vertical position of the bottom-left corner on the page</param>
            <param name="width">a floating point value measured in points.</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetHorizontalAlignment(System.Nullable{iText.Layout.Properties.HorizontalAlignment})">
            <summary>Sets the horizontal alignment of this Element.</summary>
            <param name="horizontalAlignment">
            an enum value of type
            <see cref="!:iText.Layout.Properties.HorizontalAlignment?"/>
            </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetFont(iText.Kernel.Font.PdfFont)">
            <summary>Sets the font of this Element.</summary>
            <param name="font">
            a
            <see cref="T:iText.Kernel.Font.PdfFont">font</see>
            </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetFont(System.String)">
            <summary>Sets the font of this Element.</summary>
            <remarks>
            Sets the font of this Element. Note that
            <see cref="T:iText.Layout.Font.FontProvider"/>
            shall be set as well.
            See
            <see cref="M:iText.Layout.RootElement`1.SetFontProvider(iText.Layout.Font.FontProvider)"/>
            </remarks>
            <param name="font">
            a font name to fetch from
            <see cref="T:iText.Layout.Font.FontProvider"/>
            </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetFontColor(iText.Kernel.Colors.Color)">
            <summary>Sets the font color of this Element.</summary>
            <param name="fontColor">
            a
            <see cref="T:iText.Kernel.Colors.Color"/>
            for the text in this Element.
            </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetFontColor(iText.Kernel.Colors.Color,System.Single)">
            <summary>Sets the font color of this Element and the opacity of the text.</summary>
            <param name="fontColor">
            a
            <see cref="T:iText.Kernel.Colors.Color"/>
            for the text in this Element.
            </param>
            <param name="opacity">an opacity for the text in this Element; a float between 0 and 1, where 1 stands for fully opaque color and 0 - for fully transparent.
                </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetFontSize(System.Single)">
            <summary>Sets the font size of this Element.</summary>
            <param name="fontSize">a floating point value</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetTextAlignment(System.Nullable{iText.Layout.Properties.TextAlignment})">
            <summary>Sets the text alignment of this Element.</summary>
            <param name="alignment">
            an enum value of type
            <see cref="!:iText.Layout.Properties.TextAlignment?"/>
            </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetCharacterSpacing(System.Single)">
            <summary>Defines a custom spacing distance between all characters of a textual element.</summary>
            <remarks>
            Defines a custom spacing distance between all characters of a textual element.
            The character-spacing parameter is added to the glyph???s horizontal or vertical displacement (depending on the writing mode).
            </remarks>
            <param name="charSpacing">a floating point value</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetWordSpacing(System.Single)">
            <summary>Defines a custom spacing distance between words of a textual element.</summary>
            <remarks>
            Defines a custom spacing distance between words of a textual element.
            This value works exactly like the character spacing, but only kicks in at word boundaries.
            </remarks>
            <param name="wordSpacing">a floating point value</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetFontKerning(iText.Layout.Properties.FontKerning)">
            <summary>Enable or disable kerning.</summary>
            <remarks>
            Enable or disable kerning.
            Some fonts may specify kern pairs, i.e. pair of glyphs, between which the amount of horizontal space is adjusted.
            This adjustment is typically negative, e.g. in "AV" pair the glyphs will typically be moved closer to each other.
            </remarks>
            <param name="fontKerning">an enum value as a boolean wrapper specifying whether or not to apply kerning</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetBackgroundColor(iText.Kernel.Colors.Color)">
            <summary>Specifies a background color for the Element.</summary>
            <param name="backgroundColor">the background color</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetBackgroundColor(iText.Kernel.Colors.Color,System.Single)">
            <summary>Specifies a background color for the Element.</summary>
            <param name="backgroundColor">the background color</param>
            <param name="opacity">the background color opacity; a float between 0 and 1, where 1 stands for fully opaque color and 0 - for fully transparent.
                </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetBackgroundColor(iText.Kernel.Colors.Color,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Specifies a background color for the Element, and extra space that
            must be counted as part of the background and therefore colored.
            </summary>
            <param name="backgroundColor">the background color</param>
            <param name="extraLeft">extra coloring to the left side</param>
            <param name="extraTop">extra coloring at the top</param>
            <param name="extraRight">extra coloring to the right side</param>
            <param name="extraBottom">extra coloring at the bottom</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetBackgroundColor(iText.Kernel.Colors.Color,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Specifies a background color for the Element, and extra space that
            must be counted as part of the background and therefore colored.
            </summary>
            <param name="backgroundColor">the background color</param>
            <param name="opacity">the background color opacity; a float between 0 and 1, where 1 stands for fully opaque color and 0 - for fully transparent
                </param>
            <param name="extraLeft">extra coloring to the left side</param>
            <param name="extraTop">extra coloring at the top</param>
            <param name="extraRight">extra coloring to the right side</param>
            <param name="extraBottom">extra coloring at the bottom</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetBorder(iText.Layout.Borders.Border)">
            <summary>Sets a border for all four edges of this Element with customizable color, width, pattern type.</summary>
            <param name="border">
            a customized
            <see cref="T:iText.Layout.Borders.Border"/>
            </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetBorderTop(iText.Layout.Borders.Border)">
            <summary>Sets a border for the upper limit of this Element with customizable color, width, pattern type.</summary>
            <param name="border">
            a customized
            <see cref="T:iText.Layout.Borders.Border"/>
            </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetBorderRight(iText.Layout.Borders.Border)">
            <summary>Sets a border for the right limit of this Element with customizable color, width, pattern type.</summary>
            <param name="border">
            a customized
            <see cref="T:iText.Layout.Borders.Border"/>
            </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetBorderBottom(iText.Layout.Borders.Border)">
            <summary>Sets a border for the bottom limit of this Element with customizable color, width, pattern type.</summary>
            <param name="border">
            a customized
            <see cref="T:iText.Layout.Borders.Border"/>
            </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetBorderLeft(iText.Layout.Borders.Border)">
            <summary>Sets a border for the left limit of this Element with customizable color, width, pattern type.</summary>
            <param name="border">
            a customized
            <see cref="T:iText.Layout.Borders.Border"/>
            </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetSplitCharacters(iText.Layout.Splitting.ISplitCharacters)">
            <summary>Sets a rule for splitting strings when they don't fit into one line.</summary>
            <remarks>
            Sets a rule for splitting strings when they don't fit into one line.
            The default implementation is
            <see cref="T:iText.Layout.Splitting.DefaultSplitCharacters"/>
            </remarks>
            <param name="splitCharacters">
            an implementation of
            <see cref="T:iText.Layout.Splitting.ISplitCharacters"/>
            </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.GetSplitCharacters">
            <summary>Gets a rule for splitting strings when they don't fit into one line.</summary>
            <returns>
            the current string splitting rule, an implementation of
            <see cref="T:iText.Layout.Splitting.ISplitCharacters"/>
            </returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.GetTextRenderingMode">
            <summary>
            Gets the text rendering mode, a variable that determines whether showing
            text causes glyph outlines to be stroked, filled, used as a clipping
            boundary, or some combination of the three.
            </summary>
            <returns>the current text rendering mode</returns>
            <seealso cref="T:iText.Kernel.Pdf.Canvas.PdfCanvasConstants.TextRenderingMode"/>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetTextRenderingMode(System.Int32)">
            <summary>
            Sets the text rendering mode, a variable that determines whether showing
            text causes glyph outlines to be stroked, filled, used as a clipping
            boundary, or some combination of the three.
            </summary>
            <param name="textRenderingMode">an <code>int</code> value</param>
            <returns>this Element.</returns>
            <seealso cref="T:iText.Kernel.Pdf.Canvas.PdfCanvasConstants.TextRenderingMode"/>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.GetStrokeColor">
            <summary>Gets the stroke color for the current element.</summary>
            <remarks>
            Gets the stroke color for the current element.
            The stroke color is the color of the outlines or edges of a shape.
            </remarks>
            <returns>the current stroke color</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetStrokeColor(iText.Kernel.Colors.Color)">
            <summary>Sets the stroke color for the current element.</summary>
            <remarks>
            Sets the stroke color for the current element.
            The stroke color is the color of the outlines or edges of a shape.
            </remarks>
            <param name="strokeColor">a new stroke color</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.GetStrokeWidth">
            <summary>Gets the stroke width for the current element.</summary>
            <remarks>
            Gets the stroke width for the current element.
            The stroke width is the width of the outlines or edges of a shape.
            </remarks>
            <returns>the current stroke width</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetStrokeWidth(System.Single)">
            <summary>Sets the stroke width for the current element.</summary>
            <remarks>
            Sets the stroke width for the current element.
            The stroke width is the width of the outlines or edges of a shape.
            </remarks>
            <param name="strokeWidth">a new stroke width</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetBold">
            <summary>Switch on the simulation of bold style for a font.</summary>
            <remarks>
            Switch on the simulation of bold style for a font.
            Be aware that using correct bold font is highly preferred over this option.
            </remarks>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetItalic">
            <summary>Switch on the simulation of italic style for a font.</summary>
            <remarks>
            Switch on the simulation of italic style for a font.
            Be aware that using correct italic (oblique) font is highly preferred over this option.
            </remarks>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetLineThrough">
            <summary>Sets default line-through attributes for text.</summary>
            <remarks>
            Sets default line-through attributes for text.
            See
            <see cref="M:iText.Layout.ElementPropertyContainer`1.SetUnderline(iText.Kernel.Colors.Color,System.Single,System.Single,System.Single,System.Single,System.Int32)"/>
            for more fine tuning.
            </remarks>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetBaseDirection(iText.Layout.Properties.BaseDirection)">
            <summary>
            This attribute specifies the base direction of directionally neutral text
            (i.e., text that doesn't have inherent directionality as defined in Unicode)
            in an element's content and attribute values.
            </summary>
            <param name="baseDirection">base direction</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetUnderline">
            <summary>Sets default underline attributes for text.</summary>
            <remarks>
            Sets default underline attributes for text.
            See other overloads for more fine tuning.
            </remarks>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetUnderline(System.Single,System.Single)">
            <summary>Sets an horizontal line that can be an underline or a strikethrough.</summary>
            <remarks>
            Sets an horizontal line that can be an underline or a strikethrough.
            Actually, the line can be anywhere vertically and has always the text width.
            Multiple call to this method will produce multiple lines.
            </remarks>
            <param name="thickness">the absolute thickness of the line</param>
            <param name="yPosition">the absolute y position relative to the baseline</param>
            <returns>this element</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Layout.ElementPropertyContainer`1.SetUnderline(iText.Kernel.Colors.Color,System.Single,System.Single,System.Single,System.Single,System.Int32)" -->
        <!-- Badly formed XML comment ignored for member "M:iText.Layout.ElementPropertyContainer`1.SetUnderline(iText.Kernel.Colors.Color,System.Single,System.Single,System.Single,System.Single,System.Single,System.Int32)" -->
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetBaseDirection(System.Nullable{iText.Layout.Properties.BaseDirection})">
            <summary>
            This attribute specifies the base direction of directionally neutral text
            (i.e., text that doesn't have inherent directionality as defined in Unicode)
            in an element's content and attribute values.
            </summary>
            <param name="baseDirection">base direction</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetHyphenation(iText.Layout.Hyphenation.HyphenationConfig)">
            <summary>
            Sets a custom hyphenation configuration which will hyphenate words automatically accordingly to the
            language and country.
            </summary>
            <param name="hyphenationConfig"/>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetFontScript(System.Nullable{iText.IO.Util.UnicodeScript})">
            <summary>Sets the writing system for this text element.</summary>
            <param name="script">a new script type</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetDestination(System.String)">
            <summary>Sets a destination name that will be created when this element is drawn to content.</summary>
            <param name="destination">the destination name to be created</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.ElementPropertyContainer`1.SetOpacity(System.Nullable{System.Single})">
            <summary>Sets an opacity of the given element.</summary>
            <remarks>
            Sets an opacity of the given element. It will affect element content, borders and background. Note, that it will also
            affect all element children, as they are the content of the given element.
            </remarks>
            <param name="opacity">a float between 0 and 1, where 1 stands for fully opaque element and 0 - for fully transparent
                </param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.RootElement`1.Add(iText.Layout.Element.IBlockElement)">
            <summary>Adds an element to the root.</summary>
            <remarks>Adds an element to the root. The element is immediately placed in the contents.</remarks>
            <param name="element">an element with spacial margins, tabbing, and alignment</param>
            <returns>this element</returns>
            <seealso cref="T:iText.Layout.Element.BlockElement`1"/>
        </member>
        <member name="M:iText.Layout.RootElement`1.Add(iText.Layout.Element.Image)">
            <summary>Adds an image to the root.</summary>
            <remarks>Adds an image to the root. The element is immediately placed in the contents.</remarks>
            <param name="image">a graphical image element</param>
            <returns>this element</returns>
            <seealso cref="T:iText.Layout.Element.Image"/>
        </member>
        <member name="M:iText.Layout.RootElement`1.GetFontProvider">
            <summary>
            Gets
            <see cref="T:iText.Layout.Font.FontProvider"/>
            if presents.
            </summary>
            <returns>
            instance of
            <see cref="T:iText.Layout.Font.FontProvider"/>
            if exists, otherwise null.
            </returns>
        </member>
        <member name="M:iText.Layout.RootElement`1.SetFontProvider(iText.Layout.Font.FontProvider)">
            <summary>
            Sets
            <see cref="T:iText.Layout.Font.FontProvider"/>
            .
            Note, font provider is inherited property.
            </summary>
            <param name="fontProvider">
            instance of
            <see cref="T:iText.Layout.Font.FontProvider"/>
            .
            </param>
        </member>
        <member name="M:iText.Layout.RootElement`1.GetRenderer">
            <summary>
            Gets the rootRenderer attribute, a specialized
            <see cref="T:iText.Layout.Renderer.IRenderer"/>
            that
            acts as the root object that other
            <see cref="T:iText.Layout.Renderer.IRenderer">renderers</see>
            descend
            from.
            </summary>
            <returns>
            the
            <see cref="T:iText.Layout.Renderer.RootRenderer"/>
            attribute
            </returns>
        </member>
        <member name="M:iText.Layout.RootElement`1.ShowTextAligned(System.String,System.Single,System.Single,System.Nullable{iText.Layout.Properties.TextAlignment})">
            <summary>Convenience method to write a text aligned about the specified point</summary>
            <param name="text">text to be placed to the page</param>
            <param name="x">the point about which the text will be aligned and rotated</param>
            <param name="y">the point about which the text will be aligned and rotated</param>
            <param name="textAlign">horizontal alignment about the specified point</param>
            <returns>this object</returns>
        </member>
        <member name="M:iText.Layout.RootElement`1.ShowTextAligned(System.String,System.Single,System.Single,System.Nullable{iText.Layout.Properties.TextAlignment},System.Single)">
            <summary>Convenience method to write a text aligned about the specified point</summary>
            <param name="text">text to be placed to the page</param>
            <param name="x">the point about which the text will be aligned and rotated</param>
            <param name="y">the point about which the text will be aligned and rotated</param>
            <param name="textAlign">horizontal alignment about the specified point</param>
            <param name="angle">the angle of rotation applied to the text, in radians</param>
            <returns>this object</returns>
        </member>
        <member name="M:iText.Layout.RootElement`1.ShowTextAligned(System.String,System.Single,System.Single,System.Nullable{iText.Layout.Properties.TextAlignment},System.Nullable{iText.Layout.Properties.VerticalAlignment},System.Single)">
            <summary>Convenience method to write a text aligned about the specified point</summary>
            <param name="text">text to be placed to the page</param>
            <param name="x">the point about which the text will be aligned and rotated</param>
            <param name="y">the point about which the text will be aligned and rotated</param>
            <param name="textAlign">horizontal alignment about the specified point</param>
            <param name="vertAlign">vertical alignment about the specified point</param>
            <param name="angle">the angle of rotation applied to the text, in radians</param>
            <returns>this object</returns>
        </member>
        <member name="M:iText.Layout.RootElement`1.ShowTextAlignedKerned(System.String,System.Single,System.Single,System.Nullable{iText.Layout.Properties.TextAlignment},System.Nullable{iText.Layout.Properties.VerticalAlignment},System.Single)">
            <summary>Convenience method to write a kerned text aligned about the specified point</summary>
            <param name="text">text to be placed to the page</param>
            <param name="x">the point about which the text will be aligned and rotated</param>
            <param name="y">the point about which the text will be aligned and rotated</param>
            <param name="textAlign">horizontal alignment about the specified point</param>
            <param name="vertAlign">vertical alignment about the specified point</param>
            <param name="radAngle">the angle of rotation applied to the text, in radians</param>
            <returns>this object</returns>
        </member>
        <member name="M:iText.Layout.RootElement`1.ShowTextAligned(iText.Layout.Element.Paragraph,System.Single,System.Single,System.Nullable{iText.Layout.Properties.TextAlignment})">
            <summary>Convenience method to write a text aligned about the specified point</summary>
            <param name="p">
            paragraph of text to be placed to the page. By default it has no leading and is written in single line.
            Set width to write multiline text.
            </param>
            <param name="x">the point about which the text will be aligned and rotated</param>
            <param name="y">the point about which the text will be aligned and rotated</param>
            <param name="textAlign">horizontal alignment about the specified point</param>
            <returns>this object</returns>
        </member>
        <member name="M:iText.Layout.RootElement`1.ShowTextAligned(iText.Layout.Element.Paragraph,System.Single,System.Single,System.Nullable{iText.Layout.Properties.TextAlignment},System.Nullable{iText.Layout.Properties.VerticalAlignment})">
            <summary>Convenience method to write a text aligned about the specified point</summary>
            <param name="p">
            paragraph of text to be placed to the page. By default it has no leading and is written in single line.
            Set width to write multiline text.
            </param>
            <param name="x">the point about which the text will be aligned and rotated</param>
            <param name="y">the point about which the text will be aligned and rotated</param>
            <param name="textAlign">horizontal alignment about the specified point</param>
            <param name="vertAlign">vertical alignment about the specified point</param>
            <returns>this object</returns>
        </member>
        <member name="M:iText.Layout.RootElement`1.ShowTextAligned(iText.Layout.Element.Paragraph,System.Single,System.Single,System.Int32,System.Nullable{iText.Layout.Properties.TextAlignment},System.Nullable{iText.Layout.Properties.VerticalAlignment},System.Single)">
            <summary>Convenience method to write a text aligned about the specified point</summary>
            <param name="p">
            paragraph of text to be placed to the page. By default it has no leading and is written in single line.
            Set width to write multiline text.
            </param>
            <param name="x">the point about which the text will be aligned and rotated</param>
            <param name="y">the point about which the text will be aligned and rotated</param>
            <param name="pageNumber">the page number to write the text</param>
            <param name="textAlign">horizontal alignment about the specified point</param>
            <param name="vertAlign">vertical alignment about the specified point</param>
            <param name="radAngle">the angle of rotation applied to the text, in radians</param>
            <returns>this object</returns>
        </member>
        <member name="F:iText.Layout.Canvas.page">
            <summary>
            Is initialized and used only when Canvas element autotagging is enabled, see
            <see cref="M:iText.Layout.Canvas.EnableAutoTagging(iText.Kernel.Pdf.PdfPage)"/>
            .
            It is also used to determine if autotagging is enabled.
            </summary>
        </member>
        <member name="M:iText.Layout.Canvas.#ctor(iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Kernel.Pdf.PdfDocument,iText.Kernel.Geom.Rectangle)">
            <summary>Creates a new Canvas to manipulate a specific document and page.</summary>
            <param name="pdfCanvas">the low-level content stream writer</param>
            <param name="pdfDocument">the document that the resulting content stream will be written to</param>
            <param name="rootArea">the maximum area that the Canvas may write upon</param>
        </member>
        <member name="M:iText.Layout.Canvas.#ctor(iText.Kernel.Pdf.Canvas.PdfCanvas,iText.Kernel.Pdf.PdfDocument,iText.Kernel.Geom.Rectangle,System.Boolean)">
            <summary>Creates a new Canvas to manipulate a specific document and page.</summary>
            <param name="pdfCanvas">the low-level content stream writer</param>
            <param name="pdfDocument">the document that the resulting content stream will be written to</param>
            <param name="rootArea">the maximum area that the Canvas may write upon</param>
        </member>
        <member name="M:iText.Layout.Canvas.#ctor(iText.Kernel.Pdf.Xobject.PdfFormXObject,iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a new Canvas to manipulate a specific
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject"/>
            .
            </summary>
            <param name="formXObject">the form</param>
            <param name="pdfDocument">the document that the resulting content stream will be written to</param>
        </member>
        <member name="M:iText.Layout.Canvas.GetPdfDocument">
            <summary>
            Gets the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            for this canvas.
            </summary>
            <returns>the document that the resulting content stream will be written to</returns>
        </member>
        <member name="M:iText.Layout.Canvas.GetRootArea">
            <summary>Gets the root area rectangle.</summary>
            <returns>the maximum area that the Canvas may write upon</returns>
        </member>
        <member name="M:iText.Layout.Canvas.GetPdfCanvas">
            <summary>
            Gets the
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            .
            </summary>
            <returns>the low-level content stream writer</returns>
        </member>
        <member name="M:iText.Layout.Canvas.SetRenderer(iText.Layout.Renderer.CanvasRenderer)">
            <summary>
            Sets the
            <see cref="T:iText.Layout.Renderer.IRenderer"/>
            for this Canvas.
            </summary>
            <param name="canvasRenderer">a renderer specific for canvas operations</param>
        </member>
        <member name="M:iText.Layout.Canvas.GetPage">
            <summary>Returned value is not null only in case when autotagging is enabled.</summary>
            <returns>the page, on which this canvas will be rendered, or null if autotagging is not enabled.</returns>
        </member>
        <member name="M:iText.Layout.Canvas.EnableAutoTagging(iText.Kernel.Pdf.PdfPage)">
            <summary>Enables canvas content autotagging.</summary>
            <remarks>Enables canvas content autotagging. By default it is disabled.</remarks>
            <param name="page">the page, on which this canvas will be rendered.</param>
        </member>
        <member name="M:iText.Layout.Canvas.IsAutoTaggingEnabled">
            <returns>true if autotagging of canvas content is enabled. Default value - false.</returns>
        </member>
        <member name="M:iText.Layout.Canvas.Relayout">
            <summary>
            Performs an entire recalculation of the element flow on the canvas,
            taking into account all its current child elements.
            </summary>
            <remarks>
            Performs an entire recalculation of the element flow on the canvas,
            taking into account all its current child elements. May become very
            resource-intensive for large documents.
            Do not use when you have set
            <see cref="F:iText.Layout.RootElement`1.immediateFlush"/>
            to <code>true</code>.
            </remarks>
        </member>
        <member name="M:iText.Layout.Canvas.Flush">
            <summary>
            Forces all registered renderers (including child element renderers) to
            flush their contents to the content stream.
            </summary>
        </member>
        <member name="M:iText.Layout.Canvas.Close">
            <summary>
            Closes the
            <see cref="T:iText.Layout.Canvas"/>
            . Although not completely necessary in all cases, it is still recommended to call this
            method when you are done working with
            <see cref="T:iText.Layout.Canvas"/>
            object, as due to some properties set there might be some
            'hanging' elements, which are waiting other elements to be added and processed.
            <see cref="M:iText.Layout.Canvas.Close"/>
            tells the
            <see cref="T:iText.Layout.Canvas"/>
            that no more elements will be added and it is time to finish processing all the elements.
            </summary>
        </member>
        <member name="T:iText.Layout.ColumnDocumentRenderer">
            <summary>This class is used for convenient multi-column Document Layouting</summary>
        </member>
        <member name="T:iText.Layout.Renderer.AbstractRenderer">
            <summary>
            Defines the most common properties and behavior that are shared by most
            <see cref="T:iText.Layout.Renderer.IRenderer"/>
            implementations. All default Renderers are subclasses of
            this default implementation.
            </summary>
        </member>
        <member name="T:iText.Layout.Renderer.IRenderer">
            <summary>
            A renderer object is responsible for drawing a corresponding layout object on
            a document or canvas.
            </summary>
            <remarks>
            A renderer object is responsible for drawing a corresponding layout object on
            a document or canvas. Every layout object has a renderer, by default one of
            the corresponding type, e.g. you can ask an
            <see cref="T:iText.Layout.Element.Image"/>
            for its
            <see cref="T:iText.Layout.Renderer.ImageRenderer"/>
            .
            Renderers are designed to be extensible, and custom implementations can be
            seeded to layout objects (or their custom subclasses) at runtime.
            </remarks>
        </member>
        <member name="M:iText.Layout.Renderer.IRenderer.AddChild(iText.Layout.Renderer.IRenderer)">
            <summary>Adds a child to the current renderer</summary>
            <param name="renderer">a child to be added</param>
        </member>
        <member name="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">
            <summary>
            This method simulates positioning of the renderer, including all of its children, and returns
            the
            <see cref="T:iText.Layout.Layout.LayoutResult"/>
            , representing the layout result, including occupied area, status, i.e.
            if there was enough place to fit the renderer subtree, etc.
            <see cref="T:iText.Layout.Layout.LayoutResult"/>
            can be extended to return custom layout results for custom elements, e.g.
            <see cref="T:iText.Layout.Renderer.TextRenderer"/>
            uses
            <see cref="T:iText.Layout.Layout.TextLayoutResult"/>
            as its result.
            This method can be called standalone to learn how much area the renderer subtree needs, or can be called
            before
            <see cref="M:iText.Layout.Renderer.IRenderer.Draw(iText.Layout.Renderer.DrawContext)"/>
            , to prepare the renderer to be flushed to the output stream.
            </summary>
            <param name="layoutContext">the description of layout area and any other additional information</param>
            <returns>result of the layout process</returns>
        </member>
        <member name="M:iText.Layout.Renderer.IRenderer.Draw(iText.Layout.Renderer.DrawContext)">
            <summary>Flushes the renderer subtree contents, i.e.</summary>
            <remarks>
            Flushes the renderer subtree contents, i.e. draws itself on canvas,
            adds necessary objects to the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            etc.
            </remarks>
            <param name="drawContext">
            contains the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            to which the renderer subtree if flushed,
            the
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvas"/>
            on which the renderer subtree is drawn and other additional parameters
            needed to perform drawing
            </param>
        </member>
        <member name="M:iText.Layout.Renderer.IRenderer.GetOccupiedArea">
            <summary>
            Gets the resultant occupied area after the last call to the
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            method.
            </summary>
            <returns>
            
            <see cref="T:iText.Layout.Layout.LayoutArea"/>
            instance
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.IRenderer.GetProperty``1(System.Int32,``0)">
            <summary>Gets a property from this entity or one of its hierarchical parents.</summary>
            <remarks>
            Gets a property from this entity or one of its hierarchical parents.
            If the property is not found,
            <paramref name="defaultValue"/>
            will be returned.
            </remarks>
            
            <param name="property">the property to be retrieved</param>
            <param name="defaultValue">a fallback value</param>
            <returns>the value of the given property</returns>
        </member>
        <member name="M:iText.Layout.Renderer.IRenderer.SetParent(iText.Layout.Renderer.IRenderer)">
            <summary>
            Explicitly sets this object as the child of another
            <see cref="T:iText.Layout.Renderer.IRenderer"/>
            in
            the renderer hierarchy. Some implementations also use this method
            internally to create a consistent hierarchy tree.
            </summary>
            <param name="parent">the object to place higher in the renderer hierarchy</param>
            <returns>by default, this object</returns>
        </member>
        <member name="M:iText.Layout.Renderer.IRenderer.GetModelElement">
            <summary>Gets the model element associated with this renderer.</summary>
            <returns>
            the model element, as a
            <see cref="T:iText.Layout.IPropertyContainer">container of properties</see>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.IRenderer.GetChildRenderers">
            <summary>
            Gets the child
            <see cref="T:iText.Layout.Renderer.IRenderer"/>
            s.
            </summary>
            <returns>
            a list of direct child
            <see cref="T:iText.Layout.Renderer.IRenderer">renderers</see>
            of this instance
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.IRenderer.IsFlushed">
            <summary>Indicates whether this renderer is flushed or not, i.e.</summary>
            <remarks>
            Indicates whether this renderer is flushed or not, i.e. if
            <see cref="M:iText.Layout.Renderer.IRenderer.Draw(iText.Layout.Renderer.DrawContext)"/>
            has already
            been called.
            </remarks>
            <returns>whether the renderer has been flushed</returns>
        </member>
        <member name="M:iText.Layout.Renderer.IRenderer.Move(System.Single,System.Single)">
            <summary>Moves the renderer subtree by the specified offset.</summary>
            <remarks>Moves the renderer subtree by the specified offset. This method affects occupied area of the renderer.
                </remarks>
            <param name="dx">the x-axis offset in points. Positive value will move the renderer subtree to the right.</param>
            <param name="dy">the y-axis offset in points. Positive value will move the renderer subtree to the top.</param>
        </member>
        <member name="M:iText.Layout.Renderer.IRenderer.GetNextRenderer">
            <summary>
            Gets a new instance of this class to be used as a next renderer, after this renderer is used, if
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            is called more than once.
            </summary>
            <returns>new renderer instance</returns>
        </member>
        <member name="F:iText.Layout.Renderer.AbstractRenderer.EPS">
            <summary>
            The maximum difference between
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            coordinates to consider rectangles equal
            </summary>
        </member>
        <member name="F:iText.Layout.Renderer.AbstractRenderer.INF">
            <summary>The infinity value which is used while layouting</summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.#ctor">
            <summary>Creates a renderer.</summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.#ctor(iText.Layout.Element.IElement)">
            <summary>Creates a renderer for the specified layout element.</summary>
            <param name="modelElement">the layout element that will be drawn by this renderer</param>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.AddChild(iText.Layout.Renderer.IRenderer)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetModelElement">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetChildRenderers">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.HasProperty(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.HasOwnProperty(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.HasOwnOrModelProperty(System.Int32)">
            <summary>
            Checks if this renderer or its model element have the specified property,
            i.e.
            </summary>
            <remarks>
            Checks if this renderer or its model element have the specified property,
            i.e. if it was set to this very element or its very model element earlier.
            </remarks>
            <param name="property">the property to be checked</param>
            <returns>
            
            <see langword="true"/>
            if this instance or its model element have given own property,
            <see langword="false"/>
            otherwise
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.DeleteOwnProperty(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.DeleteProperty(System.Int32)">
            <summary>
            Deletes property from this very renderer, or in case the property is specified on its model element, the
            property of the model element is deleted
            </summary>
            <param name="property">the property key to be deleted</param>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetProperty``1(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetOwnProperty``1(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetProperty``1(System.Int32,``0)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.SetProperty(System.Int32,System.Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetDefaultProperty``1(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetPropertyAsFont(System.Int32)">
            <summary>Returns a property with a certain key, as a font object.</summary>
            <param name="property">
            an
            <see cref="T:iText.Layout.Properties.Property">enum value</see>
            </param>
            <returns>
            a
            <see cref="T:iText.Kernel.Font.PdfFont"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetPropertyAsColor(System.Int32)">
            <summary>Returns a property with a certain key, as a color.</summary>
            <param name="property">
            an
            <see cref="T:iText.Layout.Properties.Property">enum value</see>
            </param>
            <returns>
            a
            <see cref="T:iText.Kernel.Colors.Color"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetPropertyAsTransparentColor(System.Int32)">
            <summary>
            Returns a property with a certain key, as a
            <see cref="T:iText.Layout.Properties.TransparentColor"/>
            .
            </summary>
            <param name="property">
            an
            <see cref="T:iText.Layout.Properties.Property">enum value</see>
            </param>
            <returns>
            a
            <see cref="T:iText.Layout.Properties.TransparentColor"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetPropertyAsFloat(System.Int32)">
            <summary>Returns a property with a certain key, as a floating point value.</summary>
            <param name="property">
            an
            <see cref="T:iText.Layout.Properties.Property">enum value</see>
            </param>
            <returns>
            a
            <see cref="!:float?"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetPropertyAsFloat(System.Int32,System.Nullable{System.Single})">
            <summary>Returns a property with a certain key, as a floating point value.</summary>
            <param name="property">
            an
            <see cref="T:iText.Layout.Properties.Property">enum value</see>
            </param>
            <param name="defaultValue">default value to be returned if property is not found</param>
            <returns>
            a
            <see cref="!:float?"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetPropertyAsBoolean(System.Int32)">
            <summary>Returns a property with a certain key, as a boolean value.</summary>
            <param name="property">
            an
            <see cref="T:iText.Layout.Properties.Property">enum value</see>
            </param>
            <returns>
            a
            <see cref="!:bool?"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetPropertyAsInteger(System.Int32)">
            <summary>Returns a property with a certain key, as an integer value.</summary>
            <param name="property">
            an
            <see cref="T:iText.Layout.Properties.Property">enum value</see>
            </param>
            <returns>
            a
            <see cref="!:int?"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.ToString">
            <summary>Returns a string representation of the renderer.</summary>
            <returns>
            a
            <see cref="T:System.String"/>
            </returns>
            <seealso cref="M:System.Object.ToString"/>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetOccupiedArea">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.Draw(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.DrawBackground(iText.Layout.Renderer.DrawContext)">
            <summary>
            Draws a background layer if it is defined by a key
            <see cref="F:iText.Layout.Properties.Property.BACKGROUND"/>
            in either the layout element or this
            <see cref="T:iText.Layout.Renderer.IRenderer"/>
            itself.
            </summary>
            <param name="drawContext">the context (canvas, document, etc) of this drawing operation.</param>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.DrawChildren(iText.Layout.Renderer.DrawContext)">
            <summary>
            Performs the drawing operation for all
            <see cref="T:iText.Layout.Renderer.IRenderer">children</see>
            of this renderer.
            </summary>
            <param name="drawContext">the context (canvas, document, etc) of this drawing operation.</param>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.DrawBorder(iText.Layout.Renderer.DrawContext)">
            <summary>
            Performs the drawing operation for the border of this renderer, if
            defined by any of the
            <see cref="F:iText.Layout.Properties.Property.BORDER"/>
            values in either the layout
            element or this
            <see cref="T:iText.Layout.Renderer.IRenderer"/>
            itself.
            </summary>
            <param name="drawContext">the context (canvas, document, etc) of this drawing operation.</param>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.IsFlushed">
            <summary>Indicates whether this renderer is flushed or not, i.e.</summary>
            <remarks>
            Indicates whether this renderer is flushed or not, i.e. if
            <see cref="M:iText.Layout.Renderer.AbstractRenderer.Draw(iText.Layout.Renderer.DrawContext)"/>
            has already
            been called.
            </remarks>
            <returns>whether the renderer has been flushed</returns>
            <seealso cref="M:iText.Layout.Renderer.AbstractRenderer.Draw(iText.Layout.Renderer.DrawContext)"/>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.SetParent(iText.Layout.Renderer.IRenderer)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetParent">
            <summary>
            Gets the parent of this
            <see cref="T:iText.Layout.Renderer.IRenderer"/>
            , if previously set by
            <see cref="M:iText.Layout.Renderer.AbstractRenderer.SetParent(iText.Layout.Renderer.IRenderer)"/>
            </summary>
            <returns>parent of the renderer</returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.Move(System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.InitElementAreas(iText.Layout.Layout.LayoutArea)">
            <summary>
            Gets all rectangles that this
            <see cref="T:iText.Layout.Renderer.IRenderer"/>
            can draw upon in the given area.
            </summary>
            <param name="area">
            a physical area on the
            <see cref="T:iText.Layout.Renderer.DrawContext"/>
            </param>
            <returns>
            a list of
            <see cref="T:iText.Kernel.Geom.Rectangle">rectangles</see>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetOccupiedAreaBBox">
            <summary>
            Gets the bounding box that contains all content written to the
            <see cref="T:iText.Layout.Renderer.DrawContext"/>
            by this
            <see cref="T:iText.Layout.Renderer.IRenderer"/>
            .
            </summary>
            <returns>
            the smallest
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            that surrounds the content
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetBorderAreaBBox">
            <summary>Gets the border box of a renderer.</summary>
            <remarks>
            Gets the border box of a renderer.
            This is a box used to draw borders.
            </remarks>
            <returns>border box of a renderer</returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.RetrieveWidth(System.Single)">
            <summary>Retrieves element's fixed content box width, if it's set.</summary>
            <remarks>
            Retrieves element's fixed content box width, if it's set.
            Takes into account
            <see cref="F:iText.Layout.Properties.Property.BOX_SIZING"/>
            ,
            <see cref="F:iText.Layout.Properties.Property.MIN_WIDTH"/>
            ,
            and
            <see cref="F:iText.Layout.Properties.Property.MAX_WIDTH"/>
            properties.
            </remarks>
            <param name="parentBoxWidth">
            width of the parent element content box.
            If element has relative width, it will be
            calculated relatively to this parameter.
            </param>
            <returns>element's fixed content box width or null if it's not set.</returns>
            <seealso cref="M:iText.Layout.Renderer.AbstractRenderer.HasAbsoluteUnitValue(System.Int32)"/>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.RetrieveMaxWidth(System.Single)">
            <summary>Retrieves element's fixed content box max width, if it's set.</summary>
            <remarks>
            Retrieves element's fixed content box max width, if it's set.
            Takes into account
            <see cref="F:iText.Layout.Properties.Property.BOX_SIZING"/>
            and
            <see cref="F:iText.Layout.Properties.Property.MIN_WIDTH"/>
            properties.
            </remarks>
            <param name="parentBoxWidth">
            width of the parent element content box.
            If element has relative width, it will be
            calculated relatively to this parameter.
            </param>
            <returns>element's fixed content box max width or null if it's not set.</returns>
            <seealso cref="M:iText.Layout.Renderer.AbstractRenderer.HasAbsoluteUnitValue(System.Int32)"/>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.RetrieveMinWidth(System.Single)">
            <summary>Retrieves element's fixed content box max width, if it's set.</summary>
            <remarks>
            Retrieves element's fixed content box max width, if it's set.
            Takes into account
            <see cref="F:iText.Layout.Properties.Property.BOX_SIZING"/>
            property value.
            </remarks>
            <param name="parentBoxWidth">
            width of the parent element content box.
            If element has relative width, it will be
            calculated relatively to this parameter.
            </param>
            <returns>element's fixed content box max width or null if it's not set.</returns>
            <seealso cref="M:iText.Layout.Renderer.AbstractRenderer.HasAbsoluteUnitValue(System.Int32)"/>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.UpdateWidth(iText.Layout.Properties.UnitValue)">
            <summary>Updates fixed content box width value for this renderer.</summary>
            <remarks>
            Updates fixed content box width value for this renderer.
            Takes into account
            <see cref="F:iText.Layout.Properties.Property.BOX_SIZING"/>
            property value.
            </remarks>
            <param name="updatedWidthValue">element's new fixed content box width.</param>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.RetrieveHeight">
            <summary>Retrieves element's fixed content box height, if it's set.</summary>
            <remarks>
            Retrieves element's fixed content box height, if it's set.
            Takes into account
            <see cref="F:iText.Layout.Properties.Property.BOX_SIZING"/>
            property value.
            </remarks>
            <returns>element's fixed content box height or null if it's not set.</returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.UpdateHeight(System.Nullable{System.Single})">
            <summary>Updates fixed content box height value for this renderer.</summary>
            <remarks>
            Updates fixed content box height value for this renderer.
            Takes into account
            <see cref="F:iText.Layout.Properties.Property.BOX_SIZING"/>
            property value.
            </remarks>
            <param name="updatedHeightValue">element's new fixed content box height, shall be not null.</param>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.RetrieveMaxHeight">
            <summary>Retrieves element's content box max-height, if it's set.</summary>
            <remarks>
            Retrieves element's content box max-height, if it's set.
            Takes into account
            <see cref="F:iText.Layout.Properties.Property.BOX_SIZING"/>
            property value.
            </remarks>
            <returns>element's content box max-height or null if it's not set.</returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.UpdateMaxHeight(System.Nullable{System.Single})">
            <summary>Updates content box max-height value for this renderer.</summary>
            <remarks>
            Updates content box max-height value for this renderer.
            Takes into account
            <see cref="F:iText.Layout.Properties.Property.BOX_SIZING"/>
            property value.
            </remarks>
            <param name="updatedMaxHeightValue">element's new content box max-height, shall be not null.</param>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.RetrieveMinHeight">
            <summary>Retrieves element's content box max-height, if it's set.</summary>
            <remarks>
            Retrieves element's content box max-height, if it's set.
            Takes into account
            <see cref="F:iText.Layout.Properties.Property.BOX_SIZING"/>
            property value.
            </remarks>
            <returns>element's content box min-height or null if it's not set.</returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.UpdateMinHeight(System.Nullable{System.Single})">
            <summary>Updates content box min-height value for this renderer.</summary>
            <remarks>
            Updates content box min-height value for this renderer.
            Takes into account
            <see cref="F:iText.Layout.Properties.Property.BOX_SIZING"/>
            property value.
            </remarks>
            <param name="updatedMinHeightValue">element's new content box min-height, shall be not null.</param>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetFirstYLineRecursively">
            <summary>Gets the first yLine of the nested children recursively.</summary>
            <remarks>
            Gets the first yLine of the nested children recursively. E.g. for a list, this will be the yLine of the
            first item (if the first item is indeed a paragraph).
            NOTE: this method will no go further than the first child.
            Returns null if there is no text found.
            </remarks>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.ApplyMargins(iText.Kernel.Geom.Rectangle,System.Boolean)">
            <summary>Applies margins of the renderer on the given rectangle</summary>
            <param name="rect">a rectangle margins will be applied on.</param>
            <param name="reverse">
            indicates whether margins will be applied
            inside (in case of false) or outside (in case of true) the rectangle.
            </param>
            <returns>
            a
            <see cref="T:iText.Kernel.Geom.Rectangle">border box</see>
            of the renderer
            </returns>
            <seealso cref="M:iText.Layout.Renderer.AbstractRenderer.GetMargins"/>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.ApplyMargins(iText.Kernel.Geom.Rectangle,System.Single[],System.Boolean)">
            <summary>Applies given margins on the given rectangle</summary>
            <param name="rect">a rectangle margins will be applied on.</param>
            <param name="margins">the margins to be applied on the given rectangle</param>
            <param name="reverse">
            indicates whether margins will be applied
            inside (in case of false) or outside (in case of true) the rectangle.
            </param>
            <returns>
            a
            <see cref="T:iText.Kernel.Geom.Rectangle">border box</see>
            of the renderer
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetMargins">
            <summary>Returns margins of the renderer</summary>
            <returns>
            a
            <c>float[]</c>
            margins of the renderer
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.GetPaddings">
            <summary>Returns paddings of the renderer</summary>
            <returns>
            a
            <c>float[]</c>
            paddings of the renderer
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.ApplyPaddings(iText.Kernel.Geom.Rectangle,System.Boolean)">
            <summary>Applies paddings of the renderer on the given rectangle</summary>
            <param name="rect">a rectangle paddings will be applied on.</param>
            <param name="reverse">
            indicates whether paddings will be applied
            inside (in case of false) or outside (in case of false) the rectangle.
            </param>
            <returns>
            a
            <see cref="T:iText.Kernel.Geom.Rectangle">border box</see>
            of the renderer
            </returns>
            <seealso cref="M:iText.Layout.Renderer.AbstractRenderer.GetPaddings"/>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.ApplyPaddings(iText.Kernel.Geom.Rectangle,System.Single[],System.Boolean)">
            <summary>Applies given paddings on the given rectangle</summary>
            <param name="rect">a rectangle paddings will be applied on.</param>
            <param name="paddings">the paddings to be applied on the given rectangle</param>
            <param name="reverse">
            indicates whether paddings will be applied
            inside (in case of false) or outside (in case of false) the rectangle.
            </param>
            <returns>
            a
            <see cref="T:iText.Kernel.Geom.Rectangle">border box</see>
            of the renderer
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.ApplyBorderBox(iText.Kernel.Geom.Rectangle,System.Boolean)">
            <summary>
            Applies the border box of the renderer on the given rectangle
            If the border of a certain side is null, the side will remain as it was.
            </summary>
            <param name="rect">a rectangle the border box will be applied on.</param>
            <param name="reverse">
            indicates whether the border box will be applied
            inside (in case of false) or outside (in case of false) the rectangle.
            </param>
            <returns>
            a
            <see cref="T:iText.Kernel.Geom.Rectangle">border box</see>
            of the renderer
            </returns>
            <seealso cref="M:iText.Layout.Renderer.AbstractRenderer.GetBorders"/>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.ApplyBorderBox(iText.Kernel.Geom.Rectangle,iText.Layout.Borders.Border[],System.Boolean)">
            <summary>Applies the given border box (borders) on the given rectangle</summary>
            <param name="rect">a rectangle paddings will be applied on.</param>
            <param name="borders">
            the
            <see cref="T:iText.Layout.Borders.Border">borders</see>
            to be applied on the given rectangle
            </param>
            <param name="reverse">
            indicates whether the border box will be applied
            inside (in case of false) or outside (in case of false) the rectangle.
            </param>
            <returns>
            a
            <see cref="T:iText.Kernel.Geom.Rectangle">border box</see>
            of the renderer
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.IsPositioned">
            <summary>Indicates whether the renderer's position is fixed or not.</summary>
            <returns>
            a
            <c>boolean</c>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.IsFixedLayout">
            <summary>Indicates whether the renderer's position is fixed or not.</summary>
            <returns>
            a
            <c>boolean</c>
            </returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Layout.Renderer.AbstractRenderer.GetBorders" -->
        <member name="M:iText.Layout.Renderer.AbstractRenderer.CalculateAbsolutePdfBBox">
            <summary>
            Calculates the bounding box of the content in the coordinate system of the pdf entity on which content is placed,
            e.g.
            </summary>
            <remarks>
            Calculates the bounding box of the content in the coordinate system of the pdf entity on which content is placed,
            e.g. document page or form xObject. This is particularly useful for the cases when element is nested in the rotated
            element.
            </remarks>
            <returns>
            a
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            which is a bbox of the content not relative to the parent's layout area but rather to
            the some pdf entity coordinate system.
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.CalculateBBox(System.Collections.Generic.IList{iText.Kernel.Geom.Point})">
            <summary>Calculates bounding box around points.</summary>
            <param name="points">list of the points calculated bbox will enclose.</param>
            <returns>array of float values which denote left, bottom, right, top lines of bbox in this specific order</returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.CalculateShiftToPositionBBoxOfPointsAt(System.Single,System.Single,System.Collections.Generic.IList{iText.Kernel.Geom.Point})">
            <summary>
            This method calculates the shift needed to be applied to the points in order to position
            upper and left borders of their bounding box at the given lines.
            </summary>
            <param name="left">x coordinate at which points bbox left border is to be aligned</param>
            <param name="top">y coordinate at which points bbox upper border is to be aligned</param>
            <param name="points">the points, which bbox will be aligned at the given position</param>
            <returns>
            array of two floats, where first element denotes x-coordinate shift and the second
            element denotes y-coordinate shift which are needed to align points bbox at the given lines.
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.HasAbsoluteUnitValue(System.Int32)">
            <summary>Check if corresponding property has point value.</summary>
            <param name="property">
            
            <see cref="T:iText.Layout.Properties.Property"/>
            </param>
            <returns>false if property value either null, or percent, otherwise true.</returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRenderer.CreateTransformationInsideOccupiedArea">
            <summary>
            This method creates
            <see cref="T:iText.Kernel.Geom.AffineTransform"/>
            instance that could be used
            to transform content inside the occupied area,
            considering the centre of the occupiedArea as the origin of a coordinate system for transformation.
            </summary>
            <returns>
            
            <see cref="T:iText.Kernel.Geom.AffineTransform"/>
            that transforms the content and places it inside occupied area.
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.RootRenderer.Flush">
            <summary>Draws (flushes) the content.</summary>
            <seealso cref="M:iText.Layout.Renderer.AbstractRenderer.Draw(iText.Layout.Renderer.DrawContext)"/>
        </member>
        <member name="M:iText.Layout.Renderer.RootRenderer.Close">
            <summary>
            This method correctly closes the
            <see cref="T:iText.Layout.Renderer.RootRenderer"/>
            instance.
            There might be hanging elements, like in case of
            <see cref="F:iText.Layout.Properties.Property.KEEP_WITH_NEXT"/>
            is set to true
            and when no consequent element has been added. This method addresses such situations.
            </summary>
        </member>
        <member name="M:iText.Layout.Renderer.RootRenderer.Layout(iText.Layout.Layout.LayoutContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.DocumentRenderer.GetNextRenderer">
            <summary>
            For
            <see cref="T:iText.Layout.Renderer.DocumentRenderer"/>
            , this has a meaning of the renderer that will be used for relayout.
            </summary>
            <returns>relayout renderer.</returns>
        </member>
        <member name="M:iText.Layout.Renderer.DocumentRenderer.EnsureDocumentHasNPages(System.Int32,iText.Kernel.Geom.PageSize)">
            <summary>Adds some pages so that the overall number is at least n.</summary>
            <remarks>
            Adds some pages so that the overall number is at least n.
            Returns the page size of the n'th page.
            </remarks>
        </member>
        <member name="M:iText.Layout.ColumnDocumentRenderer.#ctor(iText.Layout.Document,iText.Kernel.Geom.Rectangle[])">
            <summary>Creates a ColumnDocumentRenderer.</summary>
            <remarks>
            Creates a ColumnDocumentRenderer. Sets
            <see cref="F:iText.Layout.Renderer.RootRenderer.immediateFlush"/>
            to true.
            </remarks>
            <param name="document">
            the
            <see cref="T:iText.Layout.Document"/>
            on which this Renderer will calculate
            and execute element placements
            </param>
            <param name="columns">
            an array of
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            specifying the acceptable
            positions for elements on a page
            </param>
        </member>
        <member name="M:iText.Layout.ColumnDocumentRenderer.#ctor(iText.Layout.Document,System.Boolean,iText.Kernel.Geom.Rectangle[])">
            <summary>
            Creates a ColumnDocumentRenderer whose elements need not be flushed
            immediately.
            </summary>
            <param name="document">
            the
            <see cref="T:iText.Layout.Document"/>
            on which this Renderer will calculate
            and execute element placements
            </param>
            <param name="immediateFlush">whether or not to flush contents as soon as possible</param>
            <param name="columns">
            an array of
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            specifying the acceptable
            positions for elements on a page
            </param>
        </member>
        <member name="M:iText.Layout.ColumnDocumentRenderer.GetNextAreaNumber">
            <summary>
            Gets the array index of the next area that will be written on after the
            current one is full (overflowed).
            </summary>
            <returns>the array index of the next area that will be written on</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "T:iText.Layout.Document" -->
        <member name="M:iText.Layout.Document.#ctor(iText.Kernel.Pdf.PdfDocument)">
            <summary>
            Creates a document from a
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            . Initializes the first page
            with the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            's current default
            <see cref="T:iText.Kernel.Geom.PageSize"/>
            .
            </summary>
            <param name="pdfDoc">the in-memory representation of the PDF document</param>
        </member>
        <member name="M:iText.Layout.Document.#ctor(iText.Kernel.Pdf.PdfDocument,iText.Kernel.Geom.PageSize)">
            <summary>
            Creates a document from a
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            with a manually set
            <see cref="T:iText.Kernel.Geom.PageSize"/>
            .
            </summary>
            <param name="pdfDoc">the in-memory representation of the PDF document</param>
            <param name="pageSize">the page size</param>
        </member>
        <member name="M:iText.Layout.Document.#ctor(iText.Kernel.Pdf.PdfDocument,iText.Kernel.Geom.PageSize,System.Boolean)">
            <summary>
            Creates a document from a
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            with a manually set
            <see cref="T:iText.Kernel.Geom.PageSize"/>
            .
            </summary>
            <param name="pdfDoc">the in-memory representation of the PDF document</param>
            <param name="pageSize">the page size</param>
            <param name="immediateFlush">
            if true, write pages and page-related instructions
            to the
            <see cref="T:iText.Kernel.Pdf.PdfDocument"/>
            as soon as possible.
            </param>
        </member>
        <member name="M:iText.Layout.Document.Close">
            <summary>Closes the document and associated PdfDocument.</summary>
        </member>
        <member name="M:iText.Layout.Document.Add(iText.Layout.Element.AreaBreak)">
            <summary>Terminates the current element, usually a page.</summary>
            <remarks>
            Terminates the current element, usually a page. Sets the next element
            to be the size specified in the argument.
            </remarks>
            <param name="areaBreak">
            an
            <see cref="T:iText.Layout.Element.AreaBreak"/>
            , optionally with a specified size
            </param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Document.GetPdfDocument">
            <summary>Gets PDF document.</summary>
            <returns>the in-memory representation of the PDF document</returns>
        </member>
        <member name="M:iText.Layout.Document.SetRenderer(iText.Layout.Renderer.DocumentRenderer)">
            <summary>
            Changes the
            <see cref="T:iText.Layout.Renderer.DocumentRenderer"/>
            at runtime. Use this to customize
            the Document's
            <see cref="T:iText.Layout.Renderer.IRenderer"/>
            behavior.
            </summary>
            <param name="documentRenderer"/>
        </member>
        <member name="M:iText.Layout.Document.Flush">
            <summary>
            Forces all registered renderers (including child element renderers) to
            flush their contents to the content stream.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Layout.Document.Relayout" -->
        <member name="M:iText.Layout.Document.GetLeftMargin">
            <summary>Gets the left margin, measured in points</summary>
            <returns>a <code>float</code> containing the left margin value</returns>
        </member>
        <member name="M:iText.Layout.Document.SetLeftMargin(System.Single)">
            <summary>Sets the left margin, measured in points</summary>
            <param name="leftMargin">a <code>float</code> containing the new left margin value</param>
        </member>
        <member name="M:iText.Layout.Document.GetRightMargin">
            <summary>Gets the right margin, measured in points</summary>
            <returns>a <code>float</code> containing the right margin value</returns>
        </member>
        <member name="M:iText.Layout.Document.SetRightMargin(System.Single)">
            <summary>Sets the right margin, measured in points</summary>
            <param name="rightMargin">a <code>float</code> containing the new right margin value</param>
        </member>
        <member name="M:iText.Layout.Document.GetTopMargin">
            <summary>Gets the top margin, measured in points</summary>
            <returns>a <code>float</code> containing the top margin value</returns>
        </member>
        <member name="M:iText.Layout.Document.SetTopMargin(System.Single)">
            <summary>Sets the top margin, measured in points</summary>
            <param name="topMargin">a <code>float</code> containing the new top margin value</param>
        </member>
        <member name="M:iText.Layout.Document.GetBottomMargin">
            <summary>Gets the bottom margin, measured in points</summary>
            <returns>a <code>float</code> containing the bottom margin value</returns>
        </member>
        <member name="M:iText.Layout.Document.SetBottomMargin(System.Single)">
            <summary>Sets the bottom margin, measured in points</summary>
            <param name="bottomMargin">a <code>float</code> containing the new bottom margin value</param>
        </member>
        <member name="M:iText.Layout.Document.SetMargins(System.Single,System.Single,System.Single,System.Single)">
            <summary>Convenience method to set all margins with one method.</summary>
            <param name="topMargin">the upper margin</param>
            <param name="rightMargin">the right margin</param>
            <param name="leftMargin">the left margin</param>
            <param name="bottomMargin">the lower margin</param>
        </member>
        <member name="M:iText.Layout.Document.GetPageEffectiveArea(iText.Kernel.Geom.PageSize)">
            <summary>
            Returns the area that will actually be used to write on the page, given
            the current margins.
            </summary>
            <remarks>
            Returns the area that will actually be used to write on the page, given
            the current margins. Does not have any side effects on the document.
            </remarks>
            <param name="pageSize">the size of the page to</param>
            <returns>
            a
            <see cref="T:iText.Kernel.Geom.Rectangle"/>
            with the required dimensions and origin point
            </returns>
        </member>
        <member name="M:iText.Layout.Document.CheckClosingStatus">
            <summary>Checks whether a method is invoked at the closed document</summary>
        </member>
        <member name="T:iText.Layout.Element.AbstractElement`1">
            <summary>
            Defines the most common properties that most
            <see cref="T:iText.Layout.Element.IElement"/>
            implementations
            share.
            </summary>
            
        </member>
        <member name="T:iText.Layout.Element.IElement">
            <summary>This class represents a layout element, i.e.</summary>
            <remarks>
            This class represents a layout element, i.e. a piece of content that will
            take up 'physical' space on a canvas or document. Its presence and positioning
            may influence the position of other
            <see cref="T:iText.Layout.Element.IElement"/>
            s on the layout surface.
            </remarks>
        </member>
        <member name="M:iText.Layout.Element.IElement.SetNextRenderer(iText.Layout.Renderer.IRenderer)">
            <summary>
            Overrides the
            <see cref="T:iText.Layout.Renderer.IRenderer"/>
            instance which will be returned by the next call to the
            <see cref="M:iText.Layout.Element.IElement.GetRenderer"/>
            .
            </summary>
            <param name="renderer">the renderer instance</param>
        </member>
        <member name="M:iText.Layout.Element.IElement.GetRenderer">
            <summary>Gets a renderer for this element.</summary>
            <remarks>
            Gets a renderer for this element. Note that this method can be called more than once.
            By default each element should define its own renderer, but the renderer can be overridden by
            <see cref="M:iText.Layout.Element.IElement.SetNextRenderer(iText.Layout.Renderer.IRenderer)"/>
            method call.
            </remarks>
            <returns>a renderer for this element</returns>
        </member>
        <member name="M:iText.Layout.Element.IElement.CreateRendererSubTree">
            <summary>Creates a renderer subtree with root in the current element.</summary>
            <remarks>
            Creates a renderer subtree with root in the current element.
            Compared to
            <see cref="M:iText.Layout.Element.IElement.GetRenderer"/>
            , the renderer returned by this method should contain all the child
            renderers for children of the current element.
            </remarks>
            <returns>a renderer subtree for this element</returns>
        </member>
        <member name="M:iText.Layout.Element.AbstractElement`1.AddStyle(iText.Layout.Style)">
            <summary>Add a new style to this element.</summary>
            <remarks>
            Add a new style to this element. A style can be used as an effective way
            to define multiple equal properties to several elements.
            </remarks>
            <param name="style">the style to be added</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.AbstractElement`1.GetChildren">
            <summary>Gets the child elements of this elements</summary>
            <returns>a list of children</returns>
        </member>
        <member name="M:iText.Layout.Element.AbstractElement`1.PropagateArtifactRoleToChildElements">
            <summary>Marks all child elements as artifacts recursively.</summary>
        </member>
        <member name="M:iText.Layout.Element.AbstractElement`1.IsEmpty">
            <summary>Returns <code>true</code> if this list contains no elements.</summary>
            <returns><code>true</code> if this list contains no elements</returns>
        </member>
        <member name="M:iText.Layout.Element.AbstractElement`1.SetAction(iText.Kernel.Pdf.Action.PdfAction)">
            <summary>Sets an action on this Element.</summary>
            <remarks>
            Sets an action on this Element. An action is a general PDF concept that
            signifies anything that makes the document interactive, e.g. a hyperlink
            or a button.
            </remarks>
            <param name="action">
            the
            <see cref="T:iText.Kernel.Pdf.Action.PdfAction"/>
            that should be performed
            </param>
            <returns>this Element</returns>
        </member>
        <member name="M:iText.Layout.Element.AbstractElement`1.SetPageNumber(System.Int32)">
            <summary>Explicitly sets the page number this element should be put on.</summary>
            <remarks>
            Explicitly sets the page number this element should be put on. The location
            on the page will be the same as if it were added at the end of the document,
            but it will be located on the specified page.
            This method should be used very carefully in client code.
            </remarks>
            <param name="pageNumber">the page number of the page this element should be placed on</param>
            <returns>this Element</returns>
        </member>
        <member name="T:iText.Layout.Element.AreaBreak">
            <summary>
            A layout object that terminates the current content area and creates a new
            one.
            </summary>
            <remarks>
            A layout object that terminates the current content area and creates a new
            one. If no
            <see cref="T:iText.Kernel.Geom.PageSize"/>
            is given, the new content area will have the same
            size as the current one.
            </remarks>
        </member>
        <member name="M:iText.Layout.Element.AreaBreak.#ctor">
            <summary>Creates an AreaBreak.</summary>
            <remarks>
            Creates an AreaBreak. The new content area will have the same size as the
            current one.
            </remarks>
        </member>
        <member name="M:iText.Layout.Element.AreaBreak.#ctor(System.Nullable{iText.Layout.Properties.AreaBreakType})">
            <summary>Creates an AreaBreak that terminates a specified area type.</summary>
            <param name="areaBreakType">
            an
            <see cref="!:iText.Layout.Properties.AreaBreakType?">area break type</see>
            </param>
        </member>
        <member name="M:iText.Layout.Element.AreaBreak.#ctor(iText.Kernel.Geom.PageSize)">
            <summary>Creates an AreaBreak.</summary>
            <remarks>
            Creates an AreaBreak. The new content area will have the specified page
            size.
            </remarks>
            <param name="pageSize">the size of the new content area</param>
        </member>
        <member name="M:iText.Layout.Element.AreaBreak.GetPageSize">
            <summary>Gets the page size.</summary>
            <returns>
            the
            <see cref="T:iText.Kernel.Geom.PageSize">page size</see>
            of the next content area.
            </returns>
        </member>
        <member name="M:iText.Layout.Element.AreaBreak.SetPageSize(iText.Kernel.Geom.PageSize)">
            <summary>Sets the page size.</summary>
            <param name="pageSize">
            the new
            <see cref="T:iText.Kernel.Geom.PageSize">page size</see>
            of the next content area.
            </param>
        </member>
        <member name="M:iText.Layout.Element.AreaBreak.GetAreaType">
            <summary>Gets the type of area that this AreaBreak will terminate.</summary>
            <returns>
            the current
            <see cref="!:iText.Layout.Properties.AreaBreakType?">area break type</see>
            </returns>
        </member>
        <member name="T:iText.Layout.Element.BlockElement`1">
            <summary>
            A
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            will try to take up as much horizontal space as
            available to it on the canvas or page. The concept is comparable to the block
            element in HTML. Also like in HTML, the visual representation of the object
            can be delimited by padding, a border, and/or a margin.
            </summary>
            
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.#ctor">
            <summary>Creates a BlockElement.</summary>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.GetMarginLeft">
            <summary>Gets the current left margin width of the element.</summary>
            <returns>the left margin width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetMarginLeft(System.Single)">
            <summary>Sets the left margin width of the element.</summary>
            <param name="value">the new left margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.GetMarginRight">
            <summary>Gets the current right margin width of the element.</summary>
            <returns>the right margin width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetMarginRight(System.Single)">
            <summary>Sets the right margin width of the element.</summary>
            <param name="value">the new right margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.GetMarginTop">
            <summary>Gets the current top margin width of the element.</summary>
            <returns>the top margin width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetMarginTop(System.Single)">
            <summary>Sets the top margin width of the element.</summary>
            <param name="value">the new top margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.GetMarginBottom">
            <summary>Gets the current bottom margin width of the element.</summary>
            <returns>the bottom margin width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetMarginBottom(System.Single)">
            <summary>Sets the bottom margin width of the element.</summary>
            <param name="value">the new bottom margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetMargin(System.Single)">
            <summary>Sets all margins around the element to the same width.</summary>
            <param name="commonMargin">the new margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetMargins(System.Single,System.Single,System.Single,System.Single)">
            <summary>Sets the margins around the element to a series of new widths.</summary>
            <param name="marginTop">the new margin top width</param>
            <param name="marginRight">the new margin right width</param>
            <param name="marginBottom">the new margin bottom width</param>
            <param name="marginLeft">the new margin left width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.GetPaddingLeft">
            <summary>Gets the current left padding width of the element.</summary>
            <returns>the left padding width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetPaddingLeft(System.Single)">
            <summary>Sets the left padding width of the element.</summary>
            <param name="value">the new left padding width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.GetPaddingRight">
            <summary>Gets the current right padding width of the element.</summary>
            <returns>the right padding width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetPaddingRight(System.Single)">
            <summary>Sets the right padding width of the element.</summary>
            <param name="value">the new right padding width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.GetPaddingTop">
            <summary>Gets the current top padding width of the element.</summary>
            <returns>the top padding width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetPaddingTop(System.Single)">
            <summary>Sets the top padding width of the element.</summary>
            <param name="value">the new top padding width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.GetPaddingBottom">
            <summary>Gets the current bottom padding width of the element.</summary>
            <returns>the bottom padding width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetPaddingBottom(System.Single)">
            <summary>Sets the bottom padding width of the element.</summary>
            <param name="value">the new bottom padding width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetPadding(System.Single)">
            <summary>Sets all paddings around the element to the same width.</summary>
            <param name="commonPadding">the new padding width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetPaddings(System.Single,System.Single,System.Single,System.Single)">
            <summary>Sets the paddings around the element to a series of new widths.</summary>
            <param name="paddingTop">the new padding top width</param>
            <param name="paddingRight">the new padding right width</param>
            <param name="paddingBottom">the new padding bottom width</param>
            <param name="paddingLeft">the new padding left width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetVerticalAlignment(System.Nullable{iText.Layout.Properties.VerticalAlignment})">
            <summary>Sets the vertical alignment of the element.</summary>
            <param name="verticalAlignment">the vertical alignment setting</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetSpacingRatio(System.Single)">
            <summary>
            Sets a ratio which determines in which proportion will word spacing and character spacing
            be applied when horizontal alignment is justified.
            </summary>
            <param name="ratio">
            the ratio coefficient. It must be between 0 and 1, inclusive.
            It means that <b>ratio</b> part of the free space will
            be compensated by word spacing, and <b>1-ratio</b> part of the free space will
            be compensated by character spacing.
            If <b>ratio</b> is 1, additional character spacing will not be applied.
            If <b>ratio</b> is 0, additional word spacing will not be applied.
            </param>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.IsKeepTogether">
            <summary>
            Returns whether the
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            should be kept together as much
            as possible.
            </summary>
            <returns>
            the current value of the
            <see cref="F:iText.Layout.Properties.Property.KEEP_TOGETHER"/>
            property
            </returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetKeepTogether(System.Boolean)">
            <summary>
            Sets whether the
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            should be kept together as much
            as possible.
            </summary>
            <param name="keepTogether">
            the new value of the
            <see cref="F:iText.Layout.Properties.Property.KEEP_TOGETHER"/>
            property
            </param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.IsKeepWithNext">
            <summary>
            Returns whether the end of this
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            and the start of the next sibling of this element
            should be placed in the same area.
            </summary>
            <returns>
            the current value of the
            <see cref="F:iText.Layout.Properties.Property.KEEP_WITH_NEXT"/>
            property
            </returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetKeepWithNext(System.Boolean)">
            <summary>
            Sets whether the end of this
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            and the start of the next sibling of this element
            should be placed in the same area.
            Note that this will only work for high-level elements, i.e. elements added to the
            <see cref="T:iText.Layout.RootElement`1"/>
            .
            </summary>
            <param name="keepWithNext">
            the new value of the
            <see cref="F:iText.Layout.Properties.Property.KEEP_WITH_NEXT"/>
            property
            </param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetRotationAngle(System.Single)">
            <summary>Sets the rotation radAngle.</summary>
            <param name="angleInRadians">the new rotation radAngle, as a <code>float</code>, in radians</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.BlockElement`1.SetRotationAngle(System.Double)">
            <summary>Sets the rotation angle.</summary>
            <param name="angleInRadians">the new rotation angle, as a <code>double</code>, in radians</param>
            <returns>this element</returns>
        </member>
        <member name="T:iText.Layout.Element.Cell">
            <summary>
            A
            <see cref="T:iText.Layout.Element.Cell"/>
            is one piece of data in an enclosing grid, the
            <see cref="T:iText.Layout.Element.Table"/>
            .
            This object is a
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            , giving it a number of visual layout
            properties.
            A cell can act as a container for a number of layout elements; it can only
            contain other
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            objects or images. Other types of layout
            elements must be wrapped in a
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            .
            </summary>
        </member>
        <member name="M:iText.Layout.Element.Cell.#ctor(System.Int32,System.Int32)">
            <summary>Creates a cell which takes a custom amount of cell spaces in the table.</summary>
            <param name="rowspan">the number of rows this cell must occupy. Negative numbers will make the argument default to 1.
                </param>
            <param name="colspan">the number of columns this cell must occupy. Negative numbers will make the argument default to 1.
                </param>
        </member>
        <member name="M:iText.Layout.Element.Cell.#ctor">
            <summary>Creates a cell.</summary>
        </member>
        <member name="M:iText.Layout.Element.Cell.GetRenderer">
            <summary>Gets a cell renderer for this element.</summary>
            <remarks>
            Gets a cell renderer for this element. Note that this method can be called more than once.
            By default each element should define its own renderer, but the renderer can be overridden by
            <see cref="M:iText.Layout.Element.AbstractElement`1.SetNextRenderer(iText.Layout.Renderer.IRenderer)"/>
            method call.
            </remarks>
            <returns>a cell renderer for this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Cell.GetRow">
            <summary>
            Gets
            <see cref="F:iText.Layout.Element.Cell.row">the number of the row</see>
            in which the cell is located.
            </summary>
            <returns>the row number</returns>
        </member>
        <member name="M:iText.Layout.Element.Cell.GetCol">
            <summary>
            Gets
            <see cref="F:iText.Layout.Element.Cell.row">the number of the column</see>
            in which the cell is located.
            </summary>
            <returns>the column number</returns>
        </member>
        <member name="M:iText.Layout.Element.Cell.GetRowspan">
            <summary>
            Gets the
            <see cref="F:iText.Layout.Element.Cell.rowspan">rowspan</see>
            of the cell.
            </summary>
            <returns>the rowspan</returns>
        </member>
        <member name="M:iText.Layout.Element.Cell.GetColspan">
            <summary>
            Gets the
            <see cref="F:iText.Layout.Element.Cell.colspan">colspan</see>
            of the cell.
            </summary>
            <returns>the colspan</returns>
        </member>
        <member name="M:iText.Layout.Element.Cell.Add(iText.Layout.Element.IBlockElement)">
            <summary>Adds any block element to the cell's contents.</summary>
            <param name="element">
            a
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            </param>
            <returns>this Element</returns>
        </member>
        <member name="M:iText.Layout.Element.Cell.Add(iText.Layout.Element.Image)">
            <summary>Adds an image to the cell's contents.</summary>
            <param name="element">
            an
            <see cref="T:iText.Layout.Element.Image"/>
            </param>
            <returns>this Element</returns>
        </member>
        <member name="M:iText.Layout.Element.Cell.Add(System.String)">
            <summary>Directly adds a String of text to this cell.</summary>
            <remarks>
            Directly adds a String of text to this cell. The content is wrapped in a
            layout element.
            </remarks>
            <param name="content">
            a
            <see cref="T:System.String"/>
            </param>
            <returns>this Element</returns>
        </member>
        <member name="M:iText.Layout.Element.Cell.Clone(System.Boolean)">
            <summary>Clones a cell with its position, properties, and optionally its contents.</summary>
            <param name="includeContent">whether or not to also include the contents of the cell.</param>
            <returns>a clone of this Element</returns>
        </member>
        <member name="T:iText.Layout.Element.Div">
            <summary>
            A
            <see cref="T:iText.Layout.Element.Div"/>
            is a container object that defines a section in a document,
            which will have some shared layout properties. Like all
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            types, it will try to take up as much horizontal space as possible.
            The concept is very similar to that of the div tag in HTML.
            </summary>
        </member>
        <member name="M:iText.Layout.Element.Div.Add(iText.Layout.Element.IBlockElement)">
            <summary>Adds any block element to the div's contents.</summary>
            <param name="element">
            a
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            </param>
            <returns>this Element</returns>
        </member>
        <member name="M:iText.Layout.Element.Div.Add(iText.Layout.Element.Image)">
            <summary>Adds an image to the div's contents.</summary>
            <param name="element">
            an
            <see cref="T:iText.Layout.Element.Image"/>
            </param>
            <returns>this Element</returns>
        </member>
        <member name="M:iText.Layout.Element.Div.Add(iText.Layout.Element.AreaBreak)">
            <summary>Adds an area break to the div's contents.</summary>
            <param name="areaBreak">
            an
            <see cref="T:iText.Layout.Element.AreaBreak"/>
            </param>
            <returns>this Element</returns>
        </member>
        <member name="T:iText.Layout.Element.ILargeElement">
            <summary>
            A
            <see cref="T:iText.Layout.Element.ILargeElement"/>
            is a layout element which may get added to
            indefinitely, making the object prohibitively large.
            In order to avoid consuming and holding on to undesirable amounts of
            resources, the contents of a
            <see cref="T:iText.Layout.Element.ILargeElement"/>
            can be flushed regularly
            by client code, e.g. at page boundaries or after a certain amount of additions.
            </summary>
        </member>
        <member name="M:iText.Layout.Element.ILargeElement.IsComplete">
            <summary>Checks whether an element has already been marked as complete.</summary>
            <returns>the completion marker boolean</returns>
        </member>
        <member name="M:iText.Layout.Element.ILargeElement.Complete">
            <summary>Indicates that all the desired content has been added to this large element.</summary>
        </member>
        <member name="M:iText.Layout.Element.ILargeElement.Flush">
            <summary>Writes the newly added content to the document.</summary>
        </member>
        <member name="M:iText.Layout.Element.ILargeElement.FlushContent">
            <summary>Flushes the content which has just been added to the document.</summary>
            <remarks>
            Flushes the content which has just been added to the document.
            This is a method for internal usage and is called automatically by the document.
            </remarks>
        </member>
        <member name="M:iText.Layout.Element.ILargeElement.SetDocument(iText.Layout.Document)">
            <summary>Sets the document this element is bound to.</summary>
            <remarks>
            Sets the document this element is bound to.
            We cannot write a large element into several documents simultaneously because we would need
            more bulky interfaces for this feature. For now we went for simplicity.
            </remarks>
            <param name="document">the document</param>
        </member>
        <member name="T:iText.Layout.Element.ILeafElement">
            <summary>
            A marker subinterface of
            <see cref="T:iText.Layout.Element.IElement"/>
            that specifies that the layout object
            is, by definition, on the lowest tier in the object hierarchy. A
            <see cref="T:iText.Layout.Element.ILeafElement">leaf element</see>
            must not act as a container for other
            elements.
            </summary>
        </member>
        <member name="T:iText.Layout.Element.Image">
            <summary>A layout element that represents an image for inclusion in the document model.</summary>
        </member>
        <member name="M:iText.Layout.Element.Image.#ctor(iText.Kernel.Pdf.Xobject.PdfImageXObject)">
            <summary>
            Creates an
            <see cref="T:iText.Layout.Element.Image"/>
            from an image XObject, the representation of an
            image in PDF syntax.
            </summary>
            <param name="xObject">
            an internal
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfImageXObject"/>
            </param>
        </member>
        <member name="M:iText.Layout.Element.Image.#ctor(iText.Kernel.Pdf.Xobject.PdfFormXObject)">
            <summary>
            Creates an
            <see cref="T:iText.Layout.Element.Image"/>
            from a form XObject, the representation of a
            form in PDF syntax.
            </summary>
            <param name="xObject">
            an internal
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject"/>
            </param>
        </member>
        <member name="M:iText.Layout.Element.Image.#ctor(iText.Kernel.Pdf.Xobject.PdfImageXObject,System.Single)">
            <summary>
            Creates an
            <see cref="T:iText.Layout.Element.Image"/>
            from an image XObject, the representation of an
            image in PDF syntax, with a custom width.
            </summary>
            <param name="xObject">
            an internal
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfImageXObject"/>
            </param>
            <param name="width">a float value</param>
        </member>
        <member name="M:iText.Layout.Element.Image.#ctor(iText.Kernel.Pdf.Xobject.PdfImageXObject,System.Single,System.Single,System.Single)">
            <summary>
            Creates an
            <see cref="T:iText.Layout.Element.Image"/>
            from an image XObject, the representation of an
            image in PDF syntax, with a custom width and on a fixed position.
            </summary>
            <param name="xObject">
            an internal
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfImageXObject"/>
            </param>
            <param name="x">a float value representing the horizontal offset of the lower left corner of the image</param>
            <param name="y">a float value representing the vertical offset of the lower left corner of the image</param>
            <param name="width">a float value</param>
        </member>
        <member name="M:iText.Layout.Element.Image.#ctor(iText.Kernel.Pdf.Xobject.PdfImageXObject,System.Single,System.Single)">
            <summary>
            Creates an
            <see cref="T:iText.Layout.Element.Image"/>
            from an image XObject, the representation of an
            image in PDF syntax, on a fixed position.
            </summary>
            <param name="xObject">
            an internal
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfImageXObject"/>
            </param>
            <param name="x">a float value representing the horizontal offset of the lower left corner of the image</param>
            <param name="y">a float value representing the vertical offset of the lower left corner of the image</param>
        </member>
        <member name="M:iText.Layout.Element.Image.#ctor(iText.Kernel.Pdf.Xobject.PdfFormXObject,System.Single,System.Single)">
            <summary>
            Creates an
            <see cref="T:iText.Layout.Element.Image"/>
            from a form XObject, the representation of a
            form in PDF syntax.
            </summary>
            <param name="xObject">
            an internal
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfFormXObject"/>
            </param>
            <param name="x">a float value representing the horizontal offset of the lower left corner of the form</param>
            <param name="y">a float value representing the vertical offset of the lower left corner of the form</param>
        </member>
        <member name="M:iText.Layout.Element.Image.#ctor(iText.IO.Image.ImageData)">
            <summary>
            Creates an
            <see cref="T:iText.Layout.Element.Image"/>
            from an image resource, read in from a file
            with the iText I/O module.
            </summary>
            <param name="img">
            an internal representation of the
            <see cref="T:iText.IO.Image.ImageData">image resource</see>
            </param>
        </member>
        <member name="M:iText.Layout.Element.Image.#ctor(iText.IO.Image.ImageData,System.Single,System.Single)">
            <summary>
            Creates an
            <see cref="T:iText.Layout.Element.Image"/>
            from an image resource, read in from a file
            with the iText I/O module, on a fixed position.
            </summary>
            <param name="img">
            an internal representation of the
            <see cref="T:iText.IO.Image.ImageData">image resource</see>
            </param>
            <param name="x">a float value representing the horizontal offset of the lower left corner of the image</param>
            <param name="y">a float value representing the vertical offset of the lower left corner of the image</param>
        </member>
        <member name="M:iText.Layout.Element.Image.#ctor(iText.IO.Image.ImageData,System.Single,System.Single,System.Single)">
            <summary>
            Creates an
            <see cref="T:iText.Layout.Element.Image"/>
            from an image resource, read in from a file
            with the iText I/O module, with a custom width and on a fixed position.
            </summary>
            <param name="img">
            an internal representation of the
            <see cref="T:iText.IO.Image.ImageData">image resource</see>
            </param>
            <param name="x">a float value representing the horizontal offset of the lower left corner of the image</param>
            <param name="y">a float value representing the vertical offset of the lower left corner of the image</param>
            <param name="width">a float value</param>
        </member>
        <member name="M:iText.Layout.Element.Image.GetXObject">
            <summary>Gets the XObject contained in this image object</summary>
            <returns>
            a
            <see cref="T:iText.Kernel.Pdf.Xobject.PdfXObject"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Element.Image.SetRotationAngle(System.Double)">
            <summary>Sets the rotation radAngle.</summary>
            <param name="radAngle">a value in radians</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.GetMarginLeft">
            <summary>Gets the current left margin width of the element.</summary>
            <returns>the left margin width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Element.Image.SetMarginLeft(System.Single)">
            <summary>Sets the left margin width of the element.</summary>
            <param name="value">the new left margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.GetMarginRight">
            <summary>Gets the current right margin width of the element.</summary>
            <returns>the right margin width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Element.Image.SetMarginRight(System.Single)">
            <summary>Sets the right margin width of the element.</summary>
            <param name="value">the new right margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.GetMarginTop">
            <summary>Gets the current top margin width of the element.</summary>
            <returns>the top margin width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Element.Image.SetMarginTop(System.Single)">
            <summary>Sets the top margin width of the element.</summary>
            <param name="value">the new top margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.GetMarginBottom">
            <summary>Gets the current bottom margin width of the element.</summary>
            <returns>the bottom margin width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Element.Image.SetMarginBottom(System.Single)">
            <summary>Sets the bottom margin width of the element.</summary>
            <param name="value">the new bottom margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.SetMargins(System.Single,System.Single,System.Single,System.Single)">
            <summary>Sets the margins around the element to a series of new widths.</summary>
            <param name="marginTop">the new margin top width</param>
            <param name="marginRight">the new margin right width</param>
            <param name="marginBottom">the new margin bottom width</param>
            <param name="marginLeft">the new margin left width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.Scale(System.Single,System.Single)">
            <summary>Scale the image relative to its default size.</summary>
            <param name="horizontalScaling">the horizontal scaling coefficient. default value 1 = 100%</param>
            <param name="verticalScaling">the vertical scaling coefficient. default value 1 = 100%</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.ScaleToFit(System.Single,System.Single)">
            <summary>Scale the image to an absolute size.</summary>
            <remarks>
            Scale the image to an absolute size. This method will preserve the
            width-height ratio of the image.
            </remarks>
            <param name="fitWidth">the new maximum width of the image</param>
            <param name="fitHeight">the new maximum height of the image</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.ScaleAbsolute(System.Single,System.Single)">
            <summary>Scale the image to an absolute size.</summary>
            <remarks>
            Scale the image to an absolute size. This method will <em>not</em>
            preserve the width-height ratio of the image.
            </remarks>
            <param name="fitWidth">the new absolute width of the image</param>
            <param name="fitHeight">the new absolute height of the image</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.SetAutoScale(System.Boolean)">
            <summary>Sets the autoscale property for both width and height.</summary>
            <param name="autoScale">whether or not to let the image resize automatically</param>
            <returns>this image</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.SetAutoScaleHeight(System.Boolean)">
            <summary>Sets the autoscale property for the height of the image.</summary>
            <param name="autoScale">whether or not to let the image height resize automatically</param>
            <returns>this image</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.SetAutoScaleWidth(System.Boolean)">
            <summary>Sets the autoscale property for the width of the image.</summary>
            <param name="autoScale">whether or not to let the image width resize automatically</param>
            <returns>this image</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.SetFixedPosition(System.Single,System.Single)">
            <summary>Sets values for a absolute repositioning of the Element.</summary>
            <remarks>
            Sets values for a absolute repositioning of the Element. Also has as a
            side effect that the Element's
            <see cref="F:iText.Layout.Properties.Property.POSITION"/>
            is changed to
            <see cref="F:iText.Layout.Layout.LayoutPosition.FIXED">fixed</see>
            .
            </remarks>
            <param name="x">horizontal position on the page</param>
            <param name="y">vertical position on the page</param>
            <returns>this image.</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.SetFixedPosition(System.Int32,System.Single,System.Single)">
            <summary>
            Sets values for a absolute repositioning of the Element, on a specific
            page.
            </summary>
            <remarks>
            Sets values for a absolute repositioning of the Element, on a specific
            page. Also has as a side effect that the Element's
            <see cref="F:iText.Layout.Properties.Property.POSITION"/>
            is changed to
            <see cref="F:iText.Layout.Layout.LayoutPosition.FIXED">fixed</see>
            .
            </remarks>
            <param name="pageNumber">the page where the element must be positioned</param>
            <param name="x">horizontal position on the page</param>
            <param name="y">vertical position on the page</param>
            <returns>this Element.</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.GetImageWidth">
            <summary>Gets width of the image.</summary>
            <remarks>
            Gets width of the image. It returns width of image or form XObject,
            not the width set by one of the #setWidth methods
            </remarks>
            <returns>the original width of the image</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.GetImageHeight">
            <summary>Gets height of the image.</summary>
            <remarks>
            Gets height of the image. It returns height of image or form XObject,
            not the height set by one of the #setHeight methods
            </remarks>
            <returns>the original height of the image</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.GetImageScaledWidth">
            <summary>Gets scaled width of the image.</summary>
            <returns>the current scaled width</returns>
        </member>
        <member name="M:iText.Layout.Element.Image.GetImageScaledHeight">
            <summary>Gets scaled height of the image.</summary>
            <returns>the current scaled height</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "T:iText.Layout.Element.LineSeparator" -->
        <member name="M:iText.Layout.Element.LineSeparator.#ctor(iText.Kernel.Pdf.Canvas.Draw.ILineDrawer)">
            <summary>
            Creates a custom line separator with line style defined by custom
            <see cref="T:iText.Kernel.Pdf.Canvas.Draw.ILineDrawer"/>
            interface instance
            </summary>
            <param name="lineDrawer">line drawer instance</param>
        </member>
        <member name="T:iText.Layout.Element.Link">
            <summary>
            A clickable piece of
            <see cref="T:iText.Layout.Element.Text"/>
            which contains a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfLinkAnnotation">link annotation dictionary</see>
            . The concept is largely similar to that of the
            HTML anchor tag.
            </summary>
        </member>
        <member name="T:iText.Layout.Element.Text">
            <summary>
            A
            <see cref="T:iText.Layout.Element.Text"/>
            is a piece of text of any length. As a
            <see cref="T:iText.Layout.Element.ILeafElement">leaf element</see>
            ,
            it is the smallest piece of content that may bear specific layout attributes.
            </summary>
        </member>
        <member name="M:iText.Layout.Element.Text.#ctor(System.String)">
            <summary>Constructs a Text with its role initialized.</summary>
            <param name="text">
            the contents, as a
            <see cref="T:System.String"/>
            </param>
        </member>
        <member name="M:iText.Layout.Element.Text.GetText">
            <summary>Gets the contents of the Text object that will be rendered.</summary>
            <returns>the string with the contents</returns>
        </member>
        <member name="M:iText.Layout.Element.Text.SetText(System.String)">
            <summary>Sets the contents of the Text object.</summary>
            <param name="text">the new contents</param>
        </member>
        <member name="M:iText.Layout.Element.Text.GetTextRise">
            <summary>Gets the text rise.</summary>
            <returns>the vertical distance from the text's default base line, as a float.</returns>
        </member>
        <member name="M:iText.Layout.Element.Text.SetTextRise(System.Single)">
            <summary>Sets the text rise.</summary>
            <param name="textRise">a vertical distance from the text's default base line.</param>
            <returns>this Text</returns>
        </member>
        <member name="M:iText.Layout.Element.Text.GetHorizontalScaling">
            <summary>
            Gets the horizontal scaling property, which determines how wide the text
            should be stretched.
            </summary>
            <returns>the horizontal spacing, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Element.Text.SetSkew(System.Single,System.Single)">
            <summary>Skews the text to simulate italic and other effects.</summary>
            <remarks>
            Skews the text to simulate italic and other effects. Try <CODE>alpha=0
            </CODE> and <CODE>beta=12</CODE>.
            </remarks>
            <param name="alpha">the first angle in degrees</param>
            <param name="beta">the second angle in degrees</param>
            <returns>this <CODE>Text</CODE></returns>
        </member>
        <member name="M:iText.Layout.Element.Text.SetHorizontalScaling(System.Single)">
            <summary>
            The horizontal scaling parameter adjusts the width of glyphs by stretching or
            compressing them in the horizontal direction.
            </summary>
            <param name="horizontalScaling">
            the scaling parameter. 1 means no scaling will be applied,
            0.5 means the text will be scaled by half.
            2 means the text will be twice as wide as normal one.
            </param>
            <returns>this Text</returns>
        </member>
        <member name="M:iText.Layout.Element.Link.#ctor(System.String,iText.Kernel.Pdf.Annot.PdfLinkAnnotation)">
            <summary>Creates a Link with a fully constructed link annotation dictionary.</summary>
            <param name="text">the textual contents of the link</param>
            <param name="linkAnnotation">
            a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfLinkAnnotation"/>
            </param>
        </member>
        <member name="M:iText.Layout.Element.Link.#ctor(System.String,iText.Kernel.Pdf.Action.PdfAction)">
            <summary>Creates a Link which can execute an action.</summary>
            <param name="text">the textual contents of the link</param>
            <param name="action">
            a
            <see cref="T:iText.Kernel.Pdf.Action.PdfAction"/>
            </param>
        </member>
        <member name="M:iText.Layout.Element.Link.#ctor(System.String,iText.Kernel.Pdf.Navigation.PdfDestination)">
            <summary>Creates a Link to another location in the document.</summary>
            <param name="text">the textual contents of the link</param>
            <param name="destination">
            a
            <see cref="T:iText.Kernel.Pdf.Navigation.PdfDestination"/>
            </param>
        </member>
        <member name="M:iText.Layout.Element.Link.GetLinkAnnotation">
            <summary>Gets the link annotation dictionary associated with this link.</summary>
            <returns>
            a
            <see cref="T:iText.Kernel.Pdf.Annot.PdfLinkAnnotation"/>
            </returns>
        </member>
        <member name="T:iText.Layout.Element.List">
            <summary>
            A List is a layout element representing a series of objects that are vertically
            outlined with the same or very similar layout properties, giving it a sense
            of unity.
            </summary>
            <remarks>
            A List is a layout element representing a series of objects that are vertically
            outlined with the same or very similar layout properties, giving it a sense
            of unity. It contains
            <see cref="T:iText.Layout.Element.ListItem"/>
            objects that can optionally be prefixed
            with a symbol and/or numbered.
            </remarks>
        </member>
        <member name="M:iText.Layout.Element.List.#ctor">
            <summary>
            Creates a List with the
            <see cref="F:iText.Layout.Element.List.DEFAULT_LIST_SYMBOL"/>
            as a prefix.
            </summary>
        </member>
        <member name="M:iText.Layout.Element.List.#ctor(iText.Layout.Properties.ListNumberingType)">
            <summary>Creates a List with a custom numbering type.</summary>
            <param name="listNumberingType">a prefix style</param>
        </member>
        <member name="M:iText.Layout.Element.List.Add(iText.Layout.Element.ListItem)">
            <summary>
            Adds a new
            <see cref="T:iText.Layout.Element.ListItem"/>
            to the bottom of the List.
            </summary>
            <param name="listItem">a new list item</param>
            <returns>this list.</returns>
        </member>
        <member name="M:iText.Layout.Element.List.Add(System.String)">
            <summary>
            Adds a new
            <see cref="T:iText.Layout.Element.ListItem"/>
            to the bottom of the List.
            </summary>
            <param name="text">textual contents of the new list item</param>
            <returns>this list.</returns>
        </member>
        <member name="M:iText.Layout.Element.List.SetItemStartIndex(System.Int32)">
            <summary>Customizes the index of the first item in the list.</summary>
            <param name="start">the custom index, as an <code>int</code></param>
            <returns>this list.</returns>
        </member>
        <member name="M:iText.Layout.Element.List.SetListSymbol(System.String)">
            <summary>Sets the list symbol to be used.</summary>
            <remarks>
            Sets the list symbol to be used. This will create an unordered list, i.e.
            all
            <see cref="T:iText.Layout.Element.ListItem">list items</see>
            will be shown with the same prefix.
            </remarks>
            <param name="symbol">the textual symbol to be used for all items.</param>
            <returns>this list.</returns>
        </member>
        <member name="M:iText.Layout.Element.List.SetListSymbol(iText.Layout.Element.Text)">
            <summary>Sets the list symbol to be used.</summary>
            <remarks>
            Sets the list symbol to be used. This will create an unordered list, i.e.
            all
            <see cref="T:iText.Layout.Element.ListItem">list items</see>
            will be shown with the same prefix.
            </remarks>
            <param name="text">
            the
            <see cref="T:iText.Layout.Element.Text"/>
            object to be used for all items.
            </param>
            <returns>this list.</returns>
        </member>
        <member name="M:iText.Layout.Element.List.SetListSymbol(iText.Layout.Element.Image)">
            <summary>Sets the list symbol to be used.</summary>
            <remarks>
            Sets the list symbol to be used. This will create an unordered list, i.e.
            all
            <see cref="T:iText.Layout.Element.ListItem">list items</see>
            will be shown with the same prefix.
            </remarks>
            <param name="image">
            the
            <see cref="T:iText.Layout.Element.Image"/>
            object to be used for all items.
            </param>
            <returns>this list.</returns>
        </member>
        <member name="M:iText.Layout.Element.List.SetListSymbol(iText.Layout.Properties.ListNumberingType)">
            <summary>Sets the list numbering type to be used.</summary>
            <remarks>
            Sets the list numbering type to be used. This will create an ordered list,
            i.e. every
            <see cref="T:iText.Layout.Element.ListItem"/>
            will have a unique prefix.
            </remarks>
            <param name="listNumberingType">
            the
            <see cref="T:iText.Layout.Properties.ListNumberingType"/>
            that will generate appropriate prefixes for the
            <see cref="T:iText.Layout.Element.ListItem"/>
            s.
            </param>
            <returns>this list.</returns>
        </member>
        <member name="M:iText.Layout.Element.List.SetListSymbolAlignment(iText.Layout.Properties.ListSymbolAlignment)">
            <summary>A specialized enum containing alignment properties for list symbols.</summary>
            <remarks>
            A specialized enum containing alignment properties for list symbols.
            <see cref="F:iText.Layout.Properties.ListSymbolAlignment.LEFT"/>
            means that the items will be aligned as follows:
            9.  Item 9
            10. Item 10
            Whereas
            <see cref="F:iText.Layout.Properties.ListSymbolAlignment.RIGHT"/>
            means the items will be aligned as follows:
            9. Item 9
            10. Item 10
            </remarks>
        </member>
        <member name="M:iText.Layout.Element.List.GetSymbolIndent">
            <summary>
            Gets the indent offset of the
            <see cref="T:iText.Layout.Element.ListItem"/>
            symbols.
            </summary>
            <returns>the indent offset as a <code>float</code>.</returns>
        </member>
        <member name="M:iText.Layout.Element.List.SetSymbolIndent(System.Single)">
            <summary>
            Sets the indent offset of the
            <see cref="T:iText.Layout.Element.ListItem"/>
            symbols.
            </summary>
            <param name="symbolIndent">the new indent offset.</param>
            <returns>this list.</returns>
        </member>
        <member name="M:iText.Layout.Element.List.GetPostSymbolText">
            <summary>
            Gets the piece of text that is added after the
            <see cref="T:iText.Layout.Element.ListItem"/>
            symbol.
            </summary>
            <returns>the post symbol text</returns>
        </member>
        <member name="M:iText.Layout.Element.List.SetPostSymbolText(System.String)">
            <summary>
            Sets a piece of text that should be added after the
            <see cref="T:iText.Layout.Element.ListItem"/>
            symbol.
            </summary>
            <param name="postSymbolText">the post symbol text</param>
        </member>
        <member name="M:iText.Layout.Element.List.GetPreSymbolText">
            <summary>
            Gets the piece of text that is added before the
            <see cref="T:iText.Layout.Element.ListItem"/>
            symbol.
            </summary>
            <returns>the pre symbol text</returns>
        </member>
        <member name="M:iText.Layout.Element.List.SetPreSymbolText(System.String)">
            <summary>
            Sets a piece of text that should be added before the
            <see cref="T:iText.Layout.Element.ListItem"/>
            symbol.
            </summary>
            <param name="preSymbolText">the pre symbol text</param>
        </member>
        <member name="T:iText.Layout.Element.ListItem">
            <summary>
            A list item is a layout element that is one entry in a
            <see cref="T:iText.Layout.Element.List"/>
            . The list
            object controls the prefix, postfix, and numbering of the list items.
            </summary>
        </member>
        <member name="M:iText.Layout.Element.ListItem.#ctor">
            <summary>Creates a ListItem.</summary>
        </member>
        <member name="M:iText.Layout.Element.ListItem.#ctor(System.String)">
            <summary>Creates a list item with text.</summary>
            <param name="text">the textual contents of the list item</param>
        </member>
        <member name="M:iText.Layout.Element.ListItem.#ctor(iText.Layout.Element.Image)">
            <summary>Creates a list item with an image.</summary>
            <param name="image">the graphical contents of the list item</param>
        </member>
        <member name="M:iText.Layout.Element.ListItem.SetListSymbol(System.String)">
            <summary>Sets the list item symbol to be used.</summary>
            <param name="symbol">the textual symbol to be used for the item.</param>
            <returns>this list item.</returns>
        </member>
        <member name="M:iText.Layout.Element.ListItem.SetListSymbol(iText.Layout.Element.Text)">
            <summary>Sets the list item symbol to be used.</summary>
            <param name="text">
            the
            <see cref="T:iText.Layout.Element.Text"/>
            object to be used for the item.
            </param>
            <returns>this list item.</returns>
        </member>
        <member name="M:iText.Layout.Element.ListItem.SetListSymbol(iText.Layout.Element.Image)">
            <summary>Sets the list item symbol to be used.</summary>
            <param name="image">
            the
            <see cref="T:iText.Layout.Element.Image"/>
            object to be used for the item.
            </param>
            <returns>this list.</returns>
        </member>
        <member name="M:iText.Layout.Element.ListItem.SetListSymbol(iText.Layout.Properties.ListNumberingType)">
            <summary>Sets the list item numbering type to be used.</summary>
            <param name="listNumberingType">
            the
            <see cref="T:iText.Layout.Properties.ListNumberingType"/>
            that will generate appropriate prefixes for the
            <see cref="T:iText.Layout.Element.ListItem"/>
            .
            </param>
            <returns>this list item.</returns>
        </member>
        <member name="T:iText.Layout.Element.Paragraph">
            <summary>
            A layout element that represents a self-contained block of textual and
            graphical information.
            </summary>
            <remarks>
            A layout element that represents a self-contained block of textual and
            graphical information.
            It is a
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            which essentially acts as a container for
            <see cref="T:iText.Layout.Element.ILeafElement">leaf elements</see>
            .
            </remarks>
        </member>
        <member name="M:iText.Layout.Element.Paragraph.#ctor">
            <summary>Creates a Paragraph.</summary>
        </member>
        <member name="M:iText.Layout.Element.Paragraph.#ctor(System.String)">
            <summary>Creates a Paragraph, initialized with a piece of text.</summary>
            <param name="text">
            the initial textual content, as a
            <see cref="T:System.String"/>
            </param>
        </member>
        <member name="M:iText.Layout.Element.Paragraph.#ctor(iText.Layout.Element.Text)">
            <summary>Creates a Paragraph, initialized with a piece of text.</summary>
            <param name="text">
            the initial textual content, as a
            <see cref="T:iText.Layout.Element.Text"/>
            </param>
        </member>
        <member name="M:iText.Layout.Element.Paragraph.Add(System.String)">
            <summary>Adds a piece of text to the Paragraph</summary>
            <param name="text">
            the content to be added, as a
            <see cref="T:System.String"/>
            </param>
            <returns>this Paragraph</returns>
        </member>
        <member name="M:iText.Layout.Element.Paragraph.Add(iText.Layout.Element.ILeafElement)">
            <summary>Adds a layout element to the Paragraph.</summary>
            <param name="element">
            the content to be added, any
            <see cref="T:iText.Layout.Element.ILeafElement"/>
            </param>
            <returns>this Paragraph</returns>
        </member>
        <member name="M:iText.Layout.Element.Paragraph.AddAll``1(System.Collections.Generic.IList{``0})">
            <summary>
            Adds a
            <see cref="!:System.Collections.IList&lt;E&gt;"/>
            of layout elements to the Paragraph.
            </summary>
            <param name="elements">
            the content to be added, any
            <see cref="T:iText.Layout.Element.ILeafElement"/>
            </param>
            <returns>this Paragraph</returns>
        </member>
        <member name="M:iText.Layout.Element.Paragraph.AddTabStops(iText.Layout.Element.TabStop[])">
            <summary>Adds an unspecified amount of tabstop elements as properties to the Paragraph.</summary>
            <param name="tabStops">
            the
            <see cref="T:iText.Layout.Element.TabStop">tabstop(s)</see>
            to be added as properties
            </param>
            <returns>this Paragraph</returns>
            <seealso cref="T:iText.Layout.Element.TabStop"/>
        </member>
        <member name="M:iText.Layout.Element.Paragraph.AddTabStops(System.Collections.Generic.IList{iText.Layout.Element.TabStop})">
            <summary>
            Adds a
            <see cref="!:System.Collections.IList&lt;E&gt;"/>
            of tabstop elements as properties to the Paragraph.
            </summary>
            <param name="tabStops">
            the list of
            <see cref="T:iText.Layout.Element.TabStop"/>
            s to be added as properties
            </param>
            <returns>this Paragraph</returns>
            <seealso cref="T:iText.Layout.Element.TabStop"/>
        </member>
        <member name="M:iText.Layout.Element.Paragraph.RemoveTabStop(System.Single)">
            <summary>
            Removes a tabstop position from the Paragraph, if it is present in the
            <see cref="F:iText.Layout.Properties.Property.TAB_STOPS"/>
            property.
            </summary>
            <param name="tabStopPosition">
            the
            <see cref="T:iText.Layout.Element.TabStop"/>
            position to be removed.
            </param>
            <returns>this Paragraph</returns>
            <seealso cref="T:iText.Layout.Element.TabStop"/>
        </member>
        <member name="M:iText.Layout.Element.Paragraph.SetFirstLineIndent(System.Single)">
            <summary>
            Sets the indent value for the first line of the
            <see cref="T:iText.Layout.Element.Paragraph"/>
            .
            </summary>
            <param name="indent">
            the indent value that must be applied to the first line of
            the Paragraph, as a <code>float</code>
            </param>
            <returns>this Paragraph</returns>
        </member>
        <member name="M:iText.Layout.Element.Paragraph.SetFixedLeading(System.Single)">
            <summary>
            Sets the leading value, using the
            <see cref="F:iText.Layout.Properties.Leading.FIXED"/>
            strategy.
            </summary>
            <param name="leading">the new leading value</param>
            <returns>this Paragraph</returns>
            <seealso cref="T:iText.Layout.Properties.Leading"/>
        </member>
        <member name="M:iText.Layout.Element.Paragraph.SetMultipliedLeading(System.Single)">
            <summary>
            Sets the leading value, using the
            <see cref="F:iText.Layout.Properties.Leading.MULTIPLIED"/>
            strategy.
            </summary>
            <param name="leading">the new leading value</param>
            <returns>this Paragraph</returns>
            <seealso cref="T:iText.Layout.Properties.Leading"/>
        </member>
        <member name="T:iText.Layout.Element.Tab">
            <summary>
            This class represents the empty space from a
            <see cref="T:iText.Layout.Element.Text"/>
            to the following
            <see cref="T:iText.Layout.Element.TabStop"/>
            , if any. Using this class will not have any effect unless
            there are
            <see cref="T:iText.Layout.Element.TabStop"/>
            objects defined for the enveloping element.
            </summary>
        </member>
        <member name="T:iText.Layout.Element.Table">
            <summary>
            A
            <see cref="T:iText.Layout.Element.Table"/>
            is a layout element that represents data in a two-dimensional
            grid. It is filled with
            <see cref="T:iText.Layout.Element.Cell">cells</see>
            , ordered in rows and columns.
            <p/>
            It is an implementation of
            <see cref="T:iText.Layout.Element.ILargeElement"/>
            , which means it can be flushed
            to the canvas, in order to reclaim memory that is locked up.
            </summary>
        </member>
        <member name="M:iText.Layout.Element.Table.#ctor(System.Single[],System.Boolean)">
            <summary>
            Constructs a
            <c>Table</c>
            with the preferable column widths.
            <br/>
            Since 7.0.2 table layout algorithms were introduced. Auto layout is default, except large tables.
            For large table 100% width and fixed layout set implicitly.
            <br/>
            Note, the eventual columns width depends on selected layout, table width,
            cell's width, cell's min-widths, and cell's max-widths.
            Table layout algorithm has the same behaviour as expected for CSS table-layout property,
            where
            <paramref name="columnWidths"/>
            is &lt;colgroup&gt;'s widths.
            For more information see
            <see cref="M:iText.Layout.Element.Table.SetAutoLayout"/>
            and
            <see cref="M:iText.Layout.Element.Table.SetFixedLayout"/>
            .
            </summary>
            <param name="columnWidths">
            preferable column widths in points.  Values must be greater than or equal to zero,
            otherwise it will be interpreted as undefined.
            </param>
            <param name="largeTable">
            whether parts of the table will be written before all data is added.
            Note, large table does not support auto layout, table width shall not be removed.
            </param>
            <seealso cref="M:iText.Layout.Element.Table.SetAutoLayout"/>
            <seealso cref="M:iText.Layout.Element.Table.SetFixedLayout"/>
        </member>
        <member name="M:iText.Layout.Element.Table.#ctor(iText.Layout.Properties.UnitValue[],System.Boolean)">
            <summary>
            Constructs a
            <c>Table</c>
            with the preferable column widths.
            <br/>
            Since 7.0.2 table layout algorithms were introduced. Auto layout is default, except large tables.
            For large table 100% width and fixed layout set implicitly.
            <br/>
            Note, the eventual columns width depends on selected layout, table width,
            cell's width, cell's min-widths, and cell's max-widths.
            Table layout algorithm has the same behaviour as expected for CSS table-layout property,
            where
            <paramref name="columnWidths"/>
            is &lt;colgroup&gt;'s widths.
            For more information see
            <see cref="M:iText.Layout.Element.Table.SetAutoLayout"/>
            and
            <see cref="M:iText.Layout.Element.Table.SetFixedLayout"/>
            .
            </summary>
            <param name="columnWidths">
            preferable column widths, points and/or percents.  Values must be greater than or equal to zero,
            otherwise it will be interpreted as undefined.
            </param>
            <param name="largeTable">
            whether parts of the table will be written before all data is added.
            Note, large table does not support auto layout, table width shall not be removed.
            </param>
            <seealso cref="M:iText.Layout.Element.Table.SetAutoLayout"/>
            <seealso cref="M:iText.Layout.Element.Table.SetFixedLayout"/>
        </member>
        <member name="M:iText.Layout.Element.Table.#ctor(iText.Layout.Properties.UnitValue[])">
            <summary>
            Constructs a
            <c>Table</c>
            with the preferable column widths.
            <br/>
            Since 7.0.2 table layout algorithms were introduced. Auto layout is default.
            <br/>
            Note, the eventual columns width depends on selected layout, table width,
            cell's width, cell's min-widths, and cell's max-widths.
            Table layout algorithm has the same behaviour as expected for CSS table-layout property,
            where
            <paramref name="columnWidths"/>
            is &lt;colgroup&gt;'s widths.
            For more information see
            <see cref="M:iText.Layout.Element.Table.SetAutoLayout"/>
            and
            <see cref="M:iText.Layout.Element.Table.SetFixedLayout"/>
            .
            </summary>
            <param name="columnWidths">
            preferable column widths, points and/or percents. Values must be greater than or equal to zero,
            otherwise it will be interpreted as undefined.
            </param>
            <seealso cref="M:iText.Layout.Element.Table.SetAutoLayout"/>
            <seealso cref="M:iText.Layout.Element.Table.SetFixedLayout"/>
        </member>
        <member name="M:iText.Layout.Element.Table.#ctor(System.Single[])">
            <summary>
            Constructs a
            <c>Table</c>
            with the preferable column widths.
            <br/>
            Since 7.0.2 table layout algorithms were introduced. Auto layout is default.
            <br/>
            Note, the eventual columns width depends on selected layout, table width,
            cell's width, cell's min-widths, and cell's max-widths.
            Table layout algorithm has the same behaviour as expected for CSS table-layout property,
            where
            <c>columnWidths</c>
            is &lt;colgroup&gt;'s widths.
            For more information see
            <see cref="M:iText.Layout.Element.Table.SetAutoLayout"/>
            and
            <see cref="M:iText.Layout.Element.Table.SetFixedLayout"/>
            .
            </summary>
            <param name="pointColumnWidths">
            preferable column widths in points. Values must be greater than or equal to zero,
            otherwise it will be interpreted as undefined.
            </param>
            <seealso cref="M:iText.Layout.Element.Table.SetAutoLayout"/>
            <seealso cref="M:iText.Layout.Element.Table.SetFixedLayout"/>
        </member>
        <member name="M:iText.Layout.Element.Table.#ctor(System.Int32,System.Boolean)">
            <summary>
            Constructs a
            <c>Table</c>
            with specified number of columns. Each column will get equal percent width,
            the final column widths depend on selected table layout. 100% table width set implicitly for backward compatibility.
            <br/>
            Since 7.0.2 table layout algorithms were introduced. Auto layout is default, except large tables.
            For large table fixed layout set implicitly.
            <br/>
            Note, the eventual columns width depends on selected layout, table width,
            cell's width, cell's min-widths, and cell's max-widths.
            Table layout algorithm has the same behaviour as expected for CSS table-layout property,
            where
            <c>columnWidths</c>
            is &lt;colgroup&gt;'s widths.
            For more information see
            <see cref="M:iText.Layout.Element.Table.SetAutoLayout"/>
            and
            <see cref="M:iText.Layout.Element.Table.SetFixedLayout"/>
            .
            </summary>
            <param name="numColumns">the number of columns, each column will have equal percent width.</param>
            <param name="largeTable">
            whether parts of the table will be written before all data is added.
            Note, large table does not support auto layout, table width shall not be removed.
            </param>
            <seealso cref="M:iText.Layout.Element.Table.SetAutoLayout"/>
            <seealso cref="M:iText.Layout.Element.Table.SetFixedLayout"/>
        </member>
        <member name="M:iText.Layout.Element.Table.#ctor(System.Int32)">
            <summary>
            Constructs a
            <c>Table</c>
            with specified number of columns. Each column will get equal percent width,
            the final column widths depend on selected table layout. 100% table width set implicitly for backward compatibility.
            <br/>
            Since 7.0.2 table layout was introduced. Auto layout is default, except large tables.
            For large table fixed layout set implicitly.
            <br/>
            Note, the eventual columns width depends on selected layout, table width,
            cell's width, cell's min-widths, and cell's max-widths.
            Table layout algorithm has the same behaviour as expected for CSS table-layout property,
            where
            <c>columnWidths</c>
            is &lt;colgroup&gt;'s widths.
            For more information see
            <see cref="M:iText.Layout.Element.Table.SetAutoLayout"/>
            and
            <see cref="M:iText.Layout.Element.Table.SetFixedLayout"/>
            .
            </summary>
            <param name="numColumns">the number of columns, each column will have equal percent width.</param>
            <seealso cref="M:iText.Layout.Element.Table.SetAutoLayout"/>
            <seealso cref="M:iText.Layout.Element.Table.SetFixedLayout"/>
        </member>
        <member name="M:iText.Layout.Element.Table.SetFixedLayout">
            <summary>Set fixed layout.</summary>
            <remarks>
            Set fixed layout. Analog of
            <c>table-layout:fixed</c>
            CSS property.
            Note, the table must have width property, otherwise auto layout will be used.
            <p/>
            Algorithm description
            <br/>
            1. Scan columns for width property and set it. All the rest columns get undefined value.
            Column width includes borders and paddings. Columns have set in constructor, analog of
            <c>&lt;colgroup&gt;</c>
            element in HTML.
            <br/>
            2. Scan the very first row of table for width property and set it to undefined columns.
            Cell width has lower priority in comparing with column. Cell width doesn't include borders and paddings.
            <br/>
            2.1 If cell has colspan and all columns are undefined, each column will get equal width:
            <c>width/colspan</c>
            .
            <br/>
            2.2 If some columns already have width, equal remain (original width minus existed) width will be added
            <c>remainWidth/colspan</c>
            to each column.
            <br/>
            3. If sum of columns is less, than table width, there are two options:
            <br/>
            3.1. If undefined columns still exist, they will get the rest remaining width.
            <br/>
            3.2. Otherwise all columns will be expanded proportionally based on its width.
            <br/>
            4. If sum of columns is greater, than table width, nothing to do.
            </remarks>
            <returns>this element.</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.SetAutoLayout">
            <summary>Set auto layout.</summary>
            <remarks>
            Set auto layout. Analog of
            <c>table-layout:auto</c>
            CSS property. <br />
            Note, large table does not support auto layout.
            <p/>
            Algorithm principles.
            <br/>
            1. Column width cannot be less, than min-width of any cell in the column (calculated by layout).
            <br/>
            2. Specified table width has higher priority, than sum of column and cell widths.
            <br/>
            3. Percent value of cell and column width has higher priority, than point value.
            <br/>
            4. Cell width has higher priority, than column width.
            <br/>
            5. If column has no width, it will try to reach max-value (calculated by layout).
            </remarks>
            <returns>this element.</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.UseAllAvailableWidth">
            <summary>
            Set
            <see cref="F:iText.Layout.Properties.Property.WIDTH"/>
            = 100%.
            </summary>
        </member>
        <member name="M:iText.Layout.Element.Table.GetColumnWidth(System.Int32)">
            <summary>Returns the column width for the specified column.</summary>
            <param name="column">index of the column</param>
            <returns>the width of the column</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.GetNumberOfColumns">
            <summary>Returns the number of columns.</summary>
            <returns>the number of columns.</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.GetNumberOfRows">
            <summary>Returns the number of rows.</summary>
            <returns>the number of rows.</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.AddHeaderCell(iText.Layout.Element.Cell)">
            <summary>Adds a new cell to the header of the table.</summary>
            <remarks>
            Adds a new cell to the header of the table.
            The header will be displayed in the top of every area of this table.
            See also
            <see cref="M:iText.Layout.Element.Table.SetSkipFirstHeader(System.Boolean)"/>
            .
            </remarks>
            <param name="headerCell">a header cell to be added</param>
        </member>
        <member name="M:iText.Layout.Element.Table.AddHeaderCell``1(iText.Layout.Element.BlockElement{``0})">
            <summary>Adds a new cell with received blockElement as a content to the header of the table.</summary>
            <remarks>
            Adds a new cell with received blockElement as a content to the header of the table.
            The header will be displayed in the top of every area of this table.
            See also
            <see cref="M:iText.Layout.Element.Table.SetSkipFirstHeader(System.Boolean)"/>
            .
            </remarks>
            <param name="blockElement">an element to be added to a header cell</param>
        </member>
        <member name="M:iText.Layout.Element.Table.AddHeaderCell(iText.Layout.Element.Image)">
            <summary>Adds a new cell with received image to the header of the table.</summary>
            <remarks>
            Adds a new cell with received image to the header of the table.
            The header will be displayed in the top of every area of this table.
            See also
            <see cref="M:iText.Layout.Element.Table.SetSkipFirstHeader(System.Boolean)"/>
            .
            </remarks>
            <param name="image">an element to be added to a header cell</param>
        </member>
        <member name="M:iText.Layout.Element.Table.AddHeaderCell(System.String)">
            <summary>Adds a new cell with received string as a content to the header of the table.</summary>
            <remarks>
            Adds a new cell with received string as a content to the header of the table.
            The header will be displayed in the top of every area of this table.
            See also
            <see cref="M:iText.Layout.Element.Table.SetSkipFirstHeader(System.Boolean)"/>
            .
            </remarks>
            <param name="content">a string to be added to a header cell</param>
        </member>
        <member name="M:iText.Layout.Element.Table.GetHeader">
            <summary>Gets the header of the table.</summary>
            <remarks>Gets the header of the table. The header is represented as a distinct table and might have its own properties.
                </remarks>
            <returns>
            table header or
            <see langword="null"/>
            , if
            <see cref="M:iText.Layout.Element.Table.AddHeaderCell(iText.Layout.Element.Cell)"/>
            hasn't been called.
            </returns>
        </member>
        <member name="M:iText.Layout.Element.Table.AddFooterCell(iText.Layout.Element.Cell)">
            <summary>Adds a new cell to the footer of the table.</summary>
            <remarks>
            Adds a new cell to the footer of the table.
            The footer will be displayed in the bottom of every area of this table.
            See also
            <see cref="M:iText.Layout.Element.Table.SetSkipLastFooter(System.Boolean)"/>
            .
            </remarks>
            <param name="footerCell">a footer cell</param>
        </member>
        <member name="M:iText.Layout.Element.Table.AddFooterCell``1(iText.Layout.Element.BlockElement{``0})">
            <summary>Adds a new cell with received blockElement as a content to the footer of the table.</summary>
            <remarks>
            Adds a new cell with received blockElement as a content to the footer of the table.
            The footer will be displayed in the bottom of every area of this table.
            See also
            <see cref="M:iText.Layout.Element.Table.SetSkipLastFooter(System.Boolean)"/>
            .
            </remarks>
            <param name="blockElement">an element to be added to a footer cell</param>
        </member>
        <member name="M:iText.Layout.Element.Table.AddFooterCell(iText.Layout.Element.Image)">
            <summary>Adds a new cell with received image as a content to the footer of the table.</summary>
            <remarks>
            Adds a new cell with received image as a content to the footer of the table.
            The footer will be displayed in the bottom of every area of this table.
            See also
            <see cref="M:iText.Layout.Element.Table.SetSkipLastFooter(System.Boolean)"/>
            .
            </remarks>
            <param name="image">an image to be added to a footer cell</param>
        </member>
        <member name="M:iText.Layout.Element.Table.AddFooterCell(System.String)">
            <summary>Adds a new cell with received string as a content to the footer of the table.</summary>
            <remarks>
            Adds a new cell with received string as a content to the footer of the table.
            The footer will be displayed in the bottom of every area of this table.
            See also
            <see cref="M:iText.Layout.Element.Table.SetSkipLastFooter(System.Boolean)"/>
            .
            </remarks>
            <param name="content">a content string to be added to a footer cell</param>
        </member>
        <member name="M:iText.Layout.Element.Table.GetFooter">
            <summary>Gets the footer of the table.</summary>
            <remarks>Gets the footer of the table. The footer is represented as a distinct table and might have its own properties.
                </remarks>
            <returns>
            table footer or
            <see langword="null"/>
            , if
            <see cref="M:iText.Layout.Element.Table.AddFooterCell(iText.Layout.Element.Cell)"/>
            hasn't been called.
            </returns>
        </member>
        <member name="M:iText.Layout.Element.Table.IsSkipFirstHeader">
            <summary>
            Tells you if the first header needs to be skipped (for instance if the
            header says "continued from the previous page").
            </summary>
            <returns>Value of property skipFirstHeader.</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.SetSkipFirstHeader(System.Boolean)">
            <summary>Skips the printing of the first header.</summary>
            <remarks>
            Skips the printing of the first header. Used when printing tables in
            succession belonging to the same printed table aspect.
            </remarks>
            <param name="skipFirstHeader">New value of property skipFirstHeader.</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.IsSkipLastFooter">
            <summary>
            Tells you if the last footer needs to be skipped (for instance if the
            footer says "continued on the next page")
            </summary>
            <returns>Value of property skipLastFooter.</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.SetSkipLastFooter(System.Boolean)">
            <summary>Skips the printing of the last footer.</summary>
            <remarks>
            Skips the printing of the last footer. Used when printing tables in
            succession belonging to the same printed table aspect.
            </remarks>
            <param name="skipLastFooter">New value of property skipLastFooter.</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.StartNewRow">
            <summary>Starts new row.</summary>
            <remarks>Starts new row. This mean that next cell will be added at the beginning of next line.</remarks>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.AddCell(iText.Layout.Element.Cell)">
            <summary>Adds a new cell to the table.</summary>
            <remarks>
            Adds a new cell to the table. The implementation decides for itself which
            row the cell will be placed on.
            </remarks>
            <param name="cell">
            
            <c>Cell</c>
            to add.
            </param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.AddCell``1(iText.Layout.Element.BlockElement{``0})">
            <summary>Adds a new cell with received blockElement as a content.</summary>
            <param name="blockElement">a blockElement to add to the cell and then to the table</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.AddCell(iText.Layout.Element.Image)">
            <summary>Adds a new cell with received image as a content.</summary>
            <param name="image">an image to add to the cell and then to the table</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.AddCell(System.String)">
            <summary>Adds a new cell with received string as a content.</summary>
            <param name="content">a string to add to the cell and then to the table</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.GetCell(System.Int32,System.Int32)">
            <summary>Returns a cell as specified by its location.</summary>
            <remarks>
            Returns a cell as specified by its location. If the cell is in a col-span
            or row-span and is not the top left cell, then <code>null</code> is returned.
            </remarks>
            <param name="row">the row of the cell. indexes are zero-based</param>
            <param name="column">the column of the cell. indexes are zero-based</param>
            <returns>the cell at the specified position.</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.CreateRendererSubTree">
            <summary>Creates a renderer subtree with root in the current table element.</summary>
            <remarks>
            Creates a renderer subtree with root in the current table element.
            Compared to
            <see cref="M:iText.Layout.Element.Table.GetRenderer"/>
            , the renderer returned by this method should contain all the child
            renderers for children of the current element.
            </remarks>
            <returns>
            a
            <see cref="T:iText.Layout.Renderer.TableRenderer"/>
            subtree for this element
            </returns>
        </member>
        <member name="M:iText.Layout.Element.Table.GetRenderer">
            <summary>Gets a table renderer for this element.</summary>
            <remarks>
            Gets a table renderer for this element. Note that this method can be called more than once.
            By default each element should define its own renderer, but the renderer can be overridden by
            <see cref="M:iText.Layout.Element.AbstractElement`1.SetNextRenderer(iText.Layout.Renderer.IRenderer)"/>
            method call.
            </remarks>
            <returns>a table renderer for this element</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.Complete">
            <summary>Indicates that all the desired content has been added to this large element and no more content will be added.
                </summary>
            <remarks>
            Indicates that all the desired content has been added to this large element and no more content will be added.
            After this method is called, more precise rendering is activated.
            For instance, a table may have a
            <see cref="M:iText.Layout.Element.Table.SetSkipLastFooter(System.Boolean)"/>
            method set to true,
            and in case of large table on
            <see cref="M:iText.Layout.Element.Table.Flush"/>
            we do not know if any more content will be added,
            so we might not place the content in the bottom of the page where it would fit, but instead add a footer, and
            place that content in the start of the page. Technically such result would look all right, but it would be
            more concise if we placed the content in the bottom and did not start new page. For such cases to be
            renderered more accurately, one can call
            <see cref="M:iText.Layout.Element.Table.Complete"/>
            when some content is still there and not flushed.
            </remarks>
        </member>
        <member name="M:iText.Layout.Element.Table.Flush">
            <summary>Writes the newly added content to the document.</summary>
        </member>
        <member name="M:iText.Layout.Element.Table.FlushContent">
            <summary>Flushes the content which has just been added to the document.</summary>
            <remarks>
            Flushes the content which has just been added to the document.
            This is a method for internal usage and is called automatically by the document.
            </remarks>
        </member>
        <member name="M:iText.Layout.Element.Table.GetLastRowBottomBorder">
            <summary>Gets the markup properties of the bottom border of the (current) last row.</summary>
            <returns>
            an array of
            <see cref="T:iText.Layout.Borders.Border"/>
            objects
            </returns>
        </member>
        <member name="T:iText.Layout.Element.Table.RowRange">
            <summary>A simple object which holds the row numbers of a section of a table.</summary>
        </member>
        <member name="M:iText.Layout.Element.Table.RowRange.#ctor(System.Int32,System.Int32)">
            <summary>
            Creates a
            <see cref="T:iText.Layout.Element.Table.RowRange"/>
            </summary>
            <param name="startRow">the start number of the row group, inclusive</param>
            <param name="finishRow">the finish number of the row group, inclusive</param>
        </member>
        <member name="M:iText.Layout.Element.Table.RowRange.GetStartRow">
            <summary>Gets the starting row number of the table section</summary>
            <returns>the start number of the row group, inclusive</returns>
        </member>
        <member name="M:iText.Layout.Element.Table.RowRange.GetFinishRow">
            <summary>Gets the finishing row number of the table section</summary>
            <returns>the finish number of the row group, inclusive</returns>
        </member>
        <member name="T:iText.Layout.Element.TabStop">
            <summary>
            A TabStop is the closest location on a line of text that the text will jump
            to if a
            <see cref="T:iText.Layout.Element.Tab"/>
            is inserted. At least one TabStop must be defined on an
            element if you want to use
            <see cref="T:iText.Layout.Element.Tab">Tabs</see>
            .
            This object can be added to a
            <see cref="T:iText.Layout.Element.Paragraph"/>
            with the method
            <see cref="M:iText.Layout.Element.Paragraph.AddTabStops(iText.Layout.Element.TabStop[])"/>
            .
            </summary>
        </member>
        <member name="M:iText.Layout.Element.TabStop.#ctor(System.Single)">
            <summary>Creates a TabStop at the appropriate position.</summary>
            <param name="tabPosition">a <code>float</code>, measured in points</param>
        </member>
        <member name="M:iText.Layout.Element.TabStop.#ctor(System.Single,iText.Layout.Properties.TabAlignment)">
            <summary>
            Creates a TabStop at the appropriate position, with a specified tab
            alignment.
            </summary>
            <remarks>
            Creates a TabStop at the appropriate position, with a specified tab
            alignment. A tab alignment defines the way the textual content should be
            positioned with regards to this tab stop.
            </remarks>
            <param name="tabPosition">a <code>float</code>, measured in points</param>
            <param name="tabAlignment">
            a
            <see cref="T:iText.Layout.Properties.TabAlignment"/>
            value
            </param>
        </member>
        <member name="M:iText.Layout.Element.TabStop.#ctor(System.Single,iText.Layout.Properties.TabAlignment,iText.Kernel.Pdf.Canvas.Draw.ILineDrawer)">
            <summary>
            Creates a TabStop at the appropriate position, with a specified tab
            alignment and an explicitly given line pattern.
            </summary>
            <remarks>
            Creates a TabStop at the appropriate position, with a specified tab
            alignment and an explicitly given line pattern. A tab alignment defines
            the way the textual content should be positioned with regards to this tab
            stop. The line pattern defines a pattern that should be repeated until
            the TabStop is reached. If null, the space leading up to the TabStop will
            be empty.
            </remarks>
            <param name="tabPosition">a <code>float</code>, measured in points</param>
            <param name="tabAlignment">
            a
            <see cref="T:iText.Layout.Properties.TabAlignment"/>
            value
            </param>
            <param name="tabLeader">
            the
            <see cref="T:iText.Kernel.Pdf.Canvas.Draw.ILineDrawer"/>
            value, a pattern drawing object
            </param>
        </member>
        <member name="T:iText.Layout.Font.ComplexFontSelectorStrategy">
            <summary>
            Complex FontSelectorStrategy split text based on
            <see cref="!:iText.IO.Util.UnicodeScript?"/>
            .
            If unicode script changes, a new font will be found.
            If there is no suitable font, only one notdef glyph from
            <see cref="M:iText.Layout.Font.FontSelector.BestMatch"/>
            will be added.
            </summary>
        </member>
        <member name="T:iText.Layout.Font.FontSelectorStrategy">
            <summary>
            <see cref="T:iText.Layout.Font.FontSelectorStrategy"/>
            is responsible for splitting text into sub texts with one particular font.
            <see cref="M:iText.Layout.Font.FontSelectorStrategy.NextGlyphs"/>
            will create next sub text and set current font.
            </summary>
        </member>
        <member name="M:iText.Layout.Font.FontSelectorStrategy.GetPdfFont(iText.Layout.Font.FontInfo)">
            <summary>Utility method to create PdfFont.</summary>
            <param name="fontInfo">instance of FontInfo.</param>
            <returns>cached or just created PdfFont on success, otherwise null.</returns>
            <seealso cref="M:iText.Layout.Font.FontProvider.GetPdfFont(iText.Layout.Font.FontInfo,iText.Layout.Font.FontSet)"/>
        </member>
        <member name="M:iText.Layout.Font.FontCharacteristics.SetFontStyle(System.String)">
            <summary>Set font style</summary>
            <param name="fs">shall be 'normal', 'italic' or 'oblique'.</param>
        </member>
        <member name="T:iText.Layout.Font.FontFamilySplitter">
            <summary>
            Split css font-family string into list of font-families or generic-families
            </summary>
        </member>
        <member name="T:iText.Layout.Font.FontInfo">
            <summary>
            Contains all font related data to create
            <see cref="T:iText.IO.Font.FontProgram"/>
            and
            <see cref="T:iText.Kernel.Font.PdfFont"/>
            .
            <see cref="T:iText.IO.Font.FontProgramDescriptor"/>
            fetches with
            <see cref="T:iText.IO.Font.FontProgramDescriptorFactory"/>
            .
            </summary>
            <seealso cref="M:iText.Layout.Font.FontProvider.GetPdfFont(iText.Layout.Font.FontInfo)"/>
            <seealso cref="M:iText.Layout.Font.FontProvider.GetPdfFont(iText.Layout.Font.FontInfo,iText.Layout.Font.FontSet)">
            <p/>
            Note,
            <see cref="M:iText.Layout.Font.FontInfo.GetAlias"/>
            and
            <see cref="M:iText.Layout.Font.FontInfo.GetDescriptor"/>
            do not taken into account in
            <see cref="M:iText.Layout.Font.FontInfo.Equals(System.Object)"/>
            ,
            the same font with different aliases will have equal FontInfo's,
            and therefore the same
            <see cref="T:iText.Kernel.Font.PdfFont"/>
            in the end document.
            </seealso>
        </member>
        <member name="M:iText.Layout.Font.FontInfo.GetFontName">
            <summary>
            Gets path to font, if
            <see cref="T:iText.Layout.Font.FontInfo"/>
            was created by String.
            Note, to get PostScript or full name, use
            <see cref="M:iText.Layout.Font.FontInfo.GetDescriptor"/>
            .
            </summary>
        </member>
        <member name="M:iText.Layout.Font.FontInfo.GetFontProgram">
            <summary>
            Gets font data, if
            <see cref="T:iText.Layout.Font.FontInfo"/>
            was created with
            <c>byte[]</c>
            .
            </summary>
        </member>
        <member name="M:iText.Layout.Font.FontInfo.GetFontData">
            <summary>
            Gets font data, if
            <see cref="T:iText.Layout.Font.FontInfo"/>
            was created with
            <c>byte[]</c>
            .
            </summary>
        </member>
        <member name="M:iText.Layout.Font.FontInfo.GetAlias">
            <summary>Gets font alias.</summary>
            <returns>alias if exist, otherwise null.</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "T:iText.Layout.Font.FontProvider" -->
        <member name="M:iText.Layout.Font.FontProvider.#ctor(iText.Layout.Font.FontSet)">
            <summary>Creates a new instance of FontProvider</summary>
            <param name="fontSet">predefined set of fonts, could be null.</param>
        </member>
        <member name="M:iText.Layout.Font.FontProvider.#ctor">
            <summary>Creates a new instance of FontProvider.</summary>
        </member>
        <member name="M:iText.Layout.Font.FontProvider.GetFontSet">
            <summary>
            Gets
            <see cref="T:iText.Layout.Font.FontSet"/>
            .
            </summary>
        </member>
        <member name="M:iText.Layout.Font.FontProvider.GetFontSelector(System.Collections.Generic.IList{System.String},iText.Layout.Font.FontCharacteristics)">
            <summary>
            Create
            <see cref="T:iText.Layout.Font.FontSelector"/>
            or get from cache.
            </summary>
            <param name="fontFamilies">target font families</param>
            <param name="fc">
            instance of
            <see cref="T:iText.Layout.Font.FontCharacteristics"/>
            .
            </param>
            <returns>
            an instance of
            <see cref="T:iText.Layout.Font.FontSelector"/>
            .
            </returns>
            <seealso cref="!:CreateFontSelector(System.Collections.Generic.ICollection&lt;E&gt;, System.Collections.Generic.IList&lt;E&gt;, FontCharacteristics)&#xA;                "/>
            <seealso cref="!:GetFontSelector(System.Collections.Generic.IList&lt;E&gt;, FontCharacteristics, FontSet)"/>
        </member>
        <member name="M:iText.Layout.Font.FontProvider.GetFontSelector(System.Collections.Generic.IList{System.String},iText.Layout.Font.FontCharacteristics,iText.Layout.Font.FontSet)">
            <summary>
            Create
            <see cref="T:iText.Layout.Font.FontSelector"/>
            or get from cache.
            </summary>
            <param name="fontFamilies">target font families</param>
            <param name="fc">
            instance of
            <see cref="T:iText.Layout.Font.FontCharacteristics"/>
            .
            </param>
            <param name="tempFonts">set of temporary fonts.</param>
            <returns>
            an instance of
            <see cref="T:iText.Layout.Font.FontSelector"/>
            .
            </returns>
            <seealso cref="!:CreateFontSelector(System.Collections.Generic.ICollection&lt;E&gt;, System.Collections.Generic.IList&lt;E&gt;, FontCharacteristics)&#xA;                ">}</seealso>
        </member>
        <member name="M:iText.Layout.Font.FontProvider.CreateFontSelector(System.Collections.Generic.ICollection{iText.Layout.Font.FontInfo},System.Collections.Generic.IList{System.String},iText.Layout.Font.FontCharacteristics)">
            <summary>
            Create a new instance of
            <see cref="T:iText.Layout.Font.FontSelector"/>
            . While caching is main responsibility of
            <see cref="!:GetFontSelector(System.Collections.Generic.IList&lt;E&gt;, FontCharacteristics, FontSet)"/>
            .
            This method just create a new instance of
            <see cref="T:iText.Layout.Font.FontSelector"/>
            .
            </summary>
            <param name="fonts">Set of all available fonts in current context.</param>
            <param name="fontFamilies">target font families</param>
            <param name="fc">
            instance of
            <see cref="T:iText.Layout.Font.FontCharacteristics"/>
            .
            </param>
            <returns>
            an instance of
            <see cref="T:iText.Layout.Font.FontSelector"/>
            .
            </returns>
        </member>
        <member name="M:iText.Layout.Font.FontProvider.GetPdfFont(iText.Layout.Font.FontInfo)">
            <summary>
            Get from cache or create a new instance of
            <see cref="T:iText.Kernel.Font.PdfFont"/>
            .
            </summary>
            <param name="fontInfo">
            font info, to create
            <see cref="T:iText.IO.Font.FontProgram"/>
            and
            <see cref="T:iText.Kernel.Font.PdfFont"/>
            .
            </param>
            <returns>
            cached or new instance of
            <see cref="T:iText.Kernel.Font.PdfFont"/>
            .
            </returns>
            <exception cref="T:System.IO.IOException">
            on I/O exceptions in
            <see cref="T:iText.IO.Font.FontProgramFactory"/>
            .
            </exception>
        </member>
        <member name="M:iText.Layout.Font.FontProvider.GetPdfFont(iText.Layout.Font.FontInfo,iText.Layout.Font.FontSet)">
            <summary>
            Get from cache or create a new instance of
            <see cref="T:iText.Kernel.Font.PdfFont"/>
            .
            </summary>
            <param name="fontInfo">
            font info, to create
            <see cref="T:iText.IO.Font.FontProgram"/>
            and
            <see cref="T:iText.Kernel.Font.PdfFont"/>
            .
            </param>
            <param name="tempFonts">Set of temporary fonts.</param>
            <returns>
            cached or new instance of
            <see cref="T:iText.Kernel.Font.PdfFont"/>
            .
            </returns>
        </member>
        <member name="T:iText.Layout.Font.FontSelector">
            <summary>Sort given set of fonts according to font name and style.</summary>
        </member>
        <member name="M:iText.Layout.Font.FontSelector.#ctor(System.Collections.Generic.ICollection{iText.Layout.Font.FontInfo},System.Collections.Generic.IList{System.String},iText.Layout.Font.FontCharacteristics)">
            <summary>Create new FontSelector instance.</summary>
            <param name="allFonts">Unsorted set of all available fonts.</param>
            <param name="fontFamilies">sorted list of preferred font families.</param>
        </member>
        <member name="M:iText.Layout.Font.FontSelector.BestMatch">
            <summary>The best font match.</summary>
            <remarks>
            The best font match.
            If any font from
            <see cref="M:iText.Layout.Font.FontSelector.GetFonts"/>
            doesn't contain requested glyphs, this font will be used.
            </remarks>
        </member>
        <member name="M:iText.Layout.Font.FontSelector.GetFonts">
            <summary>Sorted set of fonts.</summary>
        </member>
        <member name="T:iText.Layout.Font.FontSelectorKey">
            <summary>
            Key for
            <see cref="T:iText.Layout.Font.FontSelector"/>
            caching.
            </summary>
            <seealso cref="T:iText.Layout.Font.FontSelectorCache"/>
        </member>
        <member name="T:iText.Layout.Font.FontSet">
            <summary>Reusable font set for FontProgram related data.</summary>
            <remarks>
            Reusable font set for FontProgram related data.
            Add and search fonts.
            <p/>
            A FontSet instance could be shared for multiple threads.
            However FontSet filling is not thread safe operation.
            </remarks>
            <seealso cref="T:iText.Layout.Font.FontProvider"/>
        </member>
        <member name="M:iText.Layout.Font.FontSet.#ctor">
            <summary>
            Creates a new instance of
            <see cref="T:iText.Layout.Font.FontSet"/>
            .
            </summary>
        </member>
        <member name="M:iText.Layout.Font.FontSet.AddDirectory(System.String,System.Boolean)">
            <summary>Add all the fonts in a directory and possibly its subdirectories.</summary>
            <param name="dir">path to directory.</param>
            <param name="scanSubdirectories">
            recursively scan subdirectories if
            <see langword="true"/>
            .
            </param>
            <returns>number of added fonts.</returns>
        </member>
        <member name="M:iText.Layout.Font.FontSet.AddDirectory(System.String)">
            <summary>Add all the fonts in a directory.</summary>
            <param name="dir">path to directory.</param>
            <returns>number of added fonts.</returns>
        </member>
        <member name="M:iText.Layout.Font.FontSet.AddFont(iText.IO.Font.FontProgram,System.String,System.String)">
            <summary>Add not supported for auto creating FontPrograms.</summary>
            <remarks>
            Add not supported for auto creating FontPrograms.
            <p/>
            Note,
            <see cref="M:iText.Layout.Font.FontInfo.GetAlias"/>
            do not taken into account in
            <see cref="M:iText.Layout.Font.FontInfo.Equals(System.Object)"/>
            .
            The same font with different alias will not be replaced.
            </remarks>
            <param name="fontProgram">
            
            <see cref="T:iText.IO.Font.FontProgram"/>
            </param>
            <param name="encoding">
            FontEncoding for creating
            <see cref="T:iText.Kernel.Font.PdfFont"/>
            </param>
            <param name="alias">font alias.</param>
            <returns>true, if font was successfully added, otherwise false.</returns>
        </member>
        <member name="M:iText.Layout.Font.FontSet.AddFont(iText.IO.Font.FontProgram,System.String)">
            <summary>Add not supported for auto creating FontPrograms.</summary>
            <remarks>
            Add not supported for auto creating FontPrograms.
            <p/>
            Note,
            <see cref="M:iText.Layout.Font.FontInfo.GetAlias"/>
            do not taken into account in
            <see cref="M:iText.Layout.Font.FontInfo.Equals(System.Object)"/>
            .
            The same font with different alias will not be replaced.
            </remarks>
            <param name="fontProgram">
            
            <see cref="T:iText.IO.Font.FontProgram"/>
            </param>
            <param name="encoding">
            FontEncoding for creating
            <see cref="T:iText.Kernel.Font.PdfFont"/>
            .
            </param>
            <returns>true, if font was successfully added, otherwise false.</returns>
        </member>
        <member name="M:iText.Layout.Font.FontSet.AddFont(System.String,System.String,System.String)">
            <summary>
            Creates
            <see cref="T:iText.Layout.Font.FontInfo"/>
            , fetches
            <see cref="T:iText.IO.Font.FontProgramDescriptor"/>
            and adds just created
            <see cref="T:iText.Layout.Font.FontInfo"/>
            to
            <see cref="T:iText.Layout.Font.FontSet"/>
            .
            <p/>
            Note,
            <see cref="M:iText.Layout.Font.FontInfo.GetAlias"/>
            do not taken into account in
            <see cref="M:iText.Layout.Font.FontInfo.Equals(System.Object)"/>
            .
            The same font with different alias will not be replaced.
            </summary>
            <param name="fontPath">path to font data.</param>
            <param name="encoding">preferred font encoding.</param>
            <param name="alias">font alias.</param>
            <returns>true, if font was successfully added, otherwise false.</returns>
            <seealso cref="T:iText.IO.Font.PdfEncodings"/>
        </member>
        <member name="M:iText.Layout.Font.FontSet.AddFont(System.String,System.String)">
            <summary>
            Creates
            <see cref="T:iText.Layout.Font.FontInfo"/>
            , fetches
            <see cref="T:iText.IO.Font.FontProgramDescriptor"/>
            and adds just created
            <see cref="T:iText.Layout.Font.FontInfo"/>
            to
            <see cref="T:iText.Layout.Font.FontSet"/>
            .
            <p/>
            Note,
            <see cref="M:iText.Layout.Font.FontInfo.GetAlias"/>
            do not taken into account in
            <see cref="M:iText.Layout.Font.FontInfo.Equals(System.Object)"/>
            .
            The same font with different alias will not be replaced.
            </summary>
            <param name="fontPath">path to font data.</param>
            <param name="encoding">preferred font encoding.</param>
            <returns>true, if font was successfully added, otherwise false.</returns>
            <seealso cref="T:iText.IO.Font.PdfEncodings"/>
        </member>
        <member name="M:iText.Layout.Font.FontSet.AddFont(System.Byte[],System.String,System.String)">
            <summary>
            Creates
            <see cref="T:iText.Layout.Font.FontInfo"/>
            , fetches
            <see cref="T:iText.IO.Font.FontProgramDescriptor"/>
            and adds just created
            <see cref="T:iText.Layout.Font.FontInfo"/>
            to
            <see cref="T:iText.Layout.Font.FontSet"/>
            .
            <p/>
            Note,
            <see cref="M:iText.Layout.Font.FontInfo.GetAlias"/>
            do not taken into account in
            <see cref="M:iText.Layout.Font.FontInfo.Equals(System.Object)"/>
            .
            The same font with different alias will not be replaced.
            </summary>
            <param name="fontData">font data.</param>
            <param name="encoding">preferred font encoding.</param>
            <param name="alias">font alias.</param>
            <returns>true, if font was successfully added, otherwise false.</returns>
            <seealso cref="T:iText.IO.Font.PdfEncodings"/>
        </member>
        <member name="M:iText.Layout.Font.FontSet.AddFont(System.Byte[],System.String)">
            <summary>
            Creates
            <see cref="T:iText.Layout.Font.FontInfo"/>
            , fetches
            <see cref="T:iText.IO.Font.FontProgramDescriptor"/>
            and adds just created
            <see cref="T:iText.Layout.Font.FontInfo"/>
            to
            <see cref="T:iText.Layout.Font.FontSet"/>
            .
            <p/>
            Note,
            <see cref="M:iText.Layout.Font.FontInfo.GetAlias"/>
            do not taken into account in
            <see cref="M:iText.Layout.Font.FontInfo.Equals(System.Object)"/>
            .
            The same font with different alias will not be replaced.
            </summary>
            <param name="fontData">font data.</param>
            <param name="encoding">preferred font encoding.</param>
            <returns>true, if font was successfully added, otherwise false.</returns>
            <seealso cref="T:iText.IO.Font.PdfEncodings"/>
        </member>
        <member name="M:iText.Layout.Font.FontSet.AddFont(System.String)">
            <summary>
            Creates
            <see cref="T:iText.Layout.Font.FontInfo"/>
            , fetches
            <see cref="T:iText.IO.Font.FontProgramDescriptor"/>
            and adds just created
            <see cref="T:iText.Layout.Font.FontInfo"/>
            to
            <see cref="T:iText.Layout.Font.FontSet"/>
            .
            <see cref="M:iText.Layout.Font.FontProvider.GetDefaultEncoding(iText.IO.Font.FontProgram)"/>
            will be used to determine encoding.
            <p/>
            Note,
            <see cref="M:iText.Layout.Font.FontInfo.GetAlias"/>
            do not taken into account in
            <see cref="M:iText.Layout.Font.FontInfo.Equals(System.Object)"/>
            .
            The same font with different alias will not be replaced.
            </summary>
            <param name="fontPath">path to font data.</param>
            <returns>true, if font was successfully added, otherwise false.</returns>
        </member>
        <member name="M:iText.Layout.Font.FontSet.AddFont(System.Byte[])">
            <summary>
            Creates
            <see cref="T:iText.Layout.Font.FontInfo"/>
            , fetches
            <see cref="T:iText.IO.Font.FontProgramDescriptor"/>
            and adds just created
            <see cref="T:iText.Layout.Font.FontInfo"/>
            to
            <see cref="T:iText.Layout.Font.FontSet"/>
            .
            <see cref="M:iText.Layout.Font.FontProvider.GetDefaultEncoding(iText.IO.Font.FontProgram)"/>
            will be used to determine encoding.
            <p/>
            Note,
            <see cref="M:iText.Layout.Font.FontInfo.GetAlias"/>
            do not taken into account in
            <see cref="M:iText.Layout.Font.FontInfo.Equals(System.Object)"/>
            .
            The same font with different alias will not be replaced.
            </summary>
            <param name="fontData">font data.</param>
            <returns>true, if font was successfully added, otherwise false.</returns>
        </member>
        <member name="M:iText.Layout.Font.FontSet.AddFont(iText.Layout.Font.FontInfo,System.String)">
            <summary>
            Adds
            <see cref="T:iText.Layout.Font.FontInfo"/>
            with alias. Could be used to fill temporary font set.
            <p/>
            Note,
            <see cref="M:iText.Layout.Font.FontInfo.GetAlias"/>
            do not taken into account in
            <see cref="M:iText.Layout.Font.FontInfo.Equals(System.Object)"/>
            .
            The same font with different alias will not be replaced.
            </summary>
            <param name="fontInfo">font info.</param>
            <param name="alias">font alias.</param>
            <returns>true, if font was successfully added, otherwise false.</returns>
        </member>
        <member name="M:iText.Layout.Font.FontSet.AddFont(iText.Layout.Font.FontInfo)">
            <summary>
            Adds
            <see cref="T:iText.Layout.Font.FontInfo"/>
            . Could be used to fill temporary font set.
            <p/>
            Note,
            <see cref="M:iText.Layout.Font.FontInfo.GetAlias"/>
            do not taken into account in
            <see cref="M:iText.Layout.Font.FontInfo.Equals(System.Object)"/>
            .
            The same font with different alias will not be replaced.
            </summary>
            <param name="fontInfo">font info.</param>
            <returns>true, if font was successfully added, otherwise false.</returns>
        </member>
        <member name="M:iText.Layout.Font.FontSet.Contains(System.String)">
            <summary>Search in existed fonts for PostScript name or full font name.</summary>
            <remarks>
            Search in existed fonts for PostScript name or full font name.
            <p/>
            Note, this method has O(n) complexity.
            </remarks>
            <param name="fontName">PostScript or full name.</param>
            <returns>
            true, if
            <see cref="T:iText.Layout.Font.FontSet"/>
            contains font with given name.
            </returns>
        </member>
        <member name="M:iText.Layout.Font.FontSet.Get(System.String)">
            <summary>Search in existed fonts for PostScript name or full font name.</summary>
            <remarks>
            Search in existed fonts for PostScript name or full font name.
            <p/>
            Note, this method has O(n) complexity.
            </remarks>
            <param name="fontName">PostScript or full name.</param>
            <returns>
            Collection of
            <see cref="T:iText.Layout.Font.FontInfo"/>
            from set of fonts with given PostScript or full name.
            </returns>
        </member>
        <member name="M:iText.Layout.Font.FontSet.GetFonts">
            <summary>Gets available fonts.</summary>
            <remarks>
            Gets available fonts.
            <p/>
            Note, the collection is unmodifiable.
            </remarks>
        </member>
        <member name="M:iText.Layout.Font.FontSet.GetFonts(iText.Layout.Font.FontSet)">
            <summary>Gets union of available and temporary fonts.</summary>
            <remarks>
            Gets union of available and temporary fonts.
            <p/>
            Note, the collection is unmodifiable.
            </remarks>
        </member>
        <member name="M:iText.Layout.Font.FontSet.IsEmpty">
            <summary>
            Returns
            <see langword="true"/>
            if this set contains no elements.
            </summary>
            <returns>
            
            <see langword="true"/>
            if this set contains no elements
            </returns>
        </member>
        <member name="M:iText.Layout.Font.FontSet.Size">
            <summary>Returns the number of elements in this set.</summary>
            <returns>the number of elements in this set</returns>
        </member>
        <member name="T:iText.Layout.Hyphenation.ByteVector">
            <summary>
            <p>This class implements a simple byte vector with access to the
            underlying array.</p>
            <p>This work was authored by Carlos Villegas (<EMAIL>).</p>
            </summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.ByteVector.DEFAULT_BLOCK_SIZE">
            <summary>Capacity increment size</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.ByteVector.array">
            <summary>The encapsulated array</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.ByteVector.n">
            <summary>Points to next free item</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.ByteVector.#ctor">
            <summary>Construct byte vector instance with default block size.</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.ByteVector.#ctor(System.Int32)">
            <summary>Construct byte vector instance.</summary>
            <param name="capacity">initial block size</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.ByteVector.#ctor(System.Byte[])">
            <summary>Construct byte vector instance.</summary>
            <param name="a">
            byte array to use
            TODO should n should be initialized to a.length to be consistent with
            CharVector behavior? [GA]
            </param>
        </member>
        <member name="M:iText.Layout.Hyphenation.ByteVector.#ctor(System.Byte[],System.Int32)">
            <summary>Construct byte vector instance.</summary>
            <param name="a">byte array to use</param>
            <param name="capacity">
            initial block size
            TODO should n should be initialized to a.length to be consistent with
            CharVector behavior? [GA]
            </param>
        </member>
        <member name="M:iText.Layout.Hyphenation.ByteVector.GetArray">
            <summary>Obtain byte vector array.</summary>
            <returns>byte array</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.ByteVector.Length">
            <summary>Obtain number of items in array.</summary>
            <returns>number of items</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.ByteVector.Capacity">
            <summary>Obtain capacity of array.</summary>
            <returns>current capacity of array</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.ByteVector.Put(System.Int32,System.Byte)">
            <summary>Pet byte at index.</summary>
            <param name="index">the index</param>
            <param name="val">a byte</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.ByteVector.Get(System.Int32)">
            <summary>Get byte at index.</summary>
            <param name="index">the index</param>
            <returns>a byte</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.ByteVector.Alloc(System.Int32)">
            <summary>This is to implement memory allocation in the array.</summary>
            <remarks>This is to implement memory allocation in the array. Like malloc().</remarks>
            <param name="size">to allocate</param>
            <returns>previous length</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.ByteVector.TrimToSize">
            <summary>Trim byte vector to current length.</summary>
        </member>
        <member name="T:iText.Layout.Hyphenation.CharVector">
            <summary>
            <p>This class implements a simple char vector with access to the
            underlying array.</p>
            <p>This work was authored by Carlos Villegas (<EMAIL>).</p>
            </summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.CharVector.DEFAULT_BLOCK_SIZE">
            <summary>Capacity increment size</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.CharVector.array">
            <summary>The encapsulated array</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.CharVector.n">
            <summary>Points to next free item</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.CharVector.#ctor">
            <summary>Construct char vector instance with default block size.</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.CharVector.#ctor(System.Int32)">
            <summary>Construct char vector instance.</summary>
            <param name="capacity">initial block size</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.CharVector.#ctor(System.Char[])">
            <summary>Construct char vector instance.</summary>
            <param name="a">char array to use</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.CharVector.#ctor(System.Char[],System.Int32)">
            <summary>Construct char vector instance.</summary>
            <param name="a">char array to use</param>
            <param name="capacity">initial block size</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.CharVector.#ctor(iText.Layout.Hyphenation.CharVector)">
            <summary>Copy constructor</summary>
            <param name="cv">the CharVector that should be cloned</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.CharVector.Clear">
            <summary>Reset length of vector, but don't clear contents.</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.CharVector.GetArray">
            <summary>Obtain char vector array.</summary>
            <returns>char array</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.CharVector.Length">
            <summary>Obtain number of items in array.</summary>
            <returns>number of items</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.CharVector.Capacity">
            <summary>Obtain capacity of array.</summary>
            <returns>current capacity of array</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.CharVector.Put(System.Int32,System.Char)">
            <summary>Pet char at index.</summary>
            <param name="index">the index</param>
            <param name="val">a char</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.CharVector.Get(System.Int32)">
            <summary>Get char at index.</summary>
            <param name="index">the index</param>
            <returns>a char</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.CharVector.Alloc(System.Int32)">
            <summary>This is to implement memory allocation in the array.</summary>
            <remarks>This is to implement memory allocation in the array. Like malloc().</remarks>
            <param name="size">to allocate</param>
            <returns>previous length</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.CharVector.TrimToSize">
            <summary>Trim char vector to current length.</summary>
        </member>
        <member name="T:iText.Layout.Hyphenation.Hyphen">
            <summary>Represents a hyphen.</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.Hyphen.preBreak">
            <summary>pre break string</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.Hyphen.noBreak">
            <summary>no break string</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.Hyphen.postBreak">
            <summary>post break string</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphen.#ctor(System.String,System.String,System.String)">
            <summary>Construct a hyphen.</summary>
            <param name="pre">break string</param>
            <param name="no">break string</param>
            <param name="post">break string</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphen.#ctor(System.String)">
            <summary>Construct a hyphen.</summary>
            <param name="pre">break string</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphen.ToString">
            <summary>
            <inheritDoc/>
            
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "T:iText.Layout.Hyphenation.Hyphenation" -->
        <member name="F:iText.Layout.Hyphenation.Hyphenation.len">
            <summary>number of hyphenation points in word</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenation.#ctor(System.String,System.Int32[])">
            <summary>
            rawWord as made of alternating strings and
            <see cref="T:iText.Layout.Hyphenation.Hyphen">Hyphen</see>
            instances
            </summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenation.Length">
            <returns>the number of hyphenation points in the word</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenation.GetPreHyphenText(System.Int32)">
            <param name="index">an index position</param>
            <returns>the pre-break text, not including the hyphen character</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenation.GetPostHyphenText(System.Int32)">
            <param name="index">an index position</param>
            <returns>the post-break text</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenation.GetHyphenationPoints">
            <returns>the hyphenation points</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenation.ToString">
            <summary>
            <inheritDoc/>
            
            </summary>
        </member>
        <member name="T:iText.Layout.Hyphenation.HyphenationConfig">
            <summary>This is the class used to configure hyphenation on layout level</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.HyphenationConfig.hyphenator">
            <summary>The Hyphenator object.</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.HyphenationConfig.hyphenSymbol">
            <summary>The hyphenation symbol used when hyphenating.</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationConfig.#ctor(System.Int32,System.Int32)">
            <summary>
            Constructs a new
            <see cref="T:iText.Layout.Hyphenation.HyphenationConfig"/>
            . No language hyphenation files will be used.
            Only soft hyphen symbols ('\u00ad') will be taken into account.
            </summary>
            <param name="leftMin">the minimum number of characters before the hyphenation point</param>
            <param name="rightMin">the minimum number of characters after the hyphenation point</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationConfig.#ctor(iText.Layout.Hyphenation.Hyphenator)">
            <summary>
            Constructs a new
            <see cref="T:iText.Layout.Hyphenation.HyphenationConfig"/>
            by a
            <see cref="T:iText.Layout.Hyphenation.Hyphenator"/>
            which will be used to
            find hyphenation points.
            </summary>
            <param name="hyphenator">
            the
            <see cref="T:iText.Layout.Hyphenation.Hyphenator"/>
            instance
            </param>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationConfig.#ctor(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Constructs a new
            <see cref="T:iText.Layout.Hyphenation.HyphenationConfig"/>
            instance.
            </summary>
            <param name="lang">the language</param>
            <param name="country">the optional country code (may be null or "none")</param>
            <param name="leftMin">the minimum number of characters before the hyphenation point</param>
            <param name="rightMin">the minimum number of characters after the hyphenation point</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationConfig.Hyphenate(System.String)">
            <summary>Hyphenates a given word.</summary>
            <returns>
            
            <see cref="T:iText.Layout.Hyphenation.Hyphenation"/>
            object representing possible hyphenation points
            or
            <see langword="null"/>
            if no hyphenation points are found.
            </returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationConfig.GetHyphenSymbol">
            <summary>Gets the hyphenation symbol.</summary>
            <returns>the hyphenation symbol</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationConfig.SetHyphenSymbol(System.Char)">
            <summary>Sets the hyphenation symbol to the specified value.</summary>
            <param name="hyphenSymbol">the new hyphenation symbol</param>
        </member>
        <member name="T:iText.Layout.Hyphenation.HyphenationException">
            <summary>A hyphenation exception.</summary>
            <remarks>
            A hyphenation exception.
            <p>This work was authored by Carlos Villegas (<EMAIL>).</p>
            </remarks>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationException.#ctor(System.String)">
            <summary>Construct a hyphenation exception.</summary>
            <param name="msg">a message string</param>
        </member>
        <!-- Badly formed XML comment ignored for member "T:iText.Layout.Hyphenation.HyphenationTree" -->
        <!-- Badly formed XML comment ignored for member "T:iText.Layout.Hyphenation.TernaryTree" -->
        <member name="F:iText.Layout.Hyphenation.TernaryTree.BLOCK_SIZE">
            <summary>allocation size for arrays</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.TernaryTree.lo">
            <summary>
            Pointer to low branch and to rest of the key when it is
            stored directly in this node, we don't have unions in java!
            </summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.TernaryTree.hi">
            <summary>Pointer to high branch.</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.TernaryTree.eq">
            <summary>Pointer to equal branch and to data when this node is a string terminator.</summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:iText.Layout.Hyphenation.TernaryTree.sc" -->
        <member name="F:iText.Layout.Hyphenation.TernaryTree.kv">
            <summary>This vector holds the trailing of the keys when the branch is compressed.</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.TernaryTree.root">
            <summary>root</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.TernaryTree.freenode">
            <summary>free node</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.TernaryTree.length">
            <summary>number of items in tree</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.#ctor">
            <summary>default constructor</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Init">
            <summary>initialize</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Insert(System.String,System.Char)">
            <summary>
            Branches are initially compressed, needing
            one node per key plus the size of the string
            key.
            </summary>
            <remarks>
            Branches are initially compressed, needing
            one node per key plus the size of the string
            key. They are decompressed as needed when
            another key with same prefix
            is inserted. This saves a lot of space,
            specially for long keys.
            </remarks>
            <param name="key">the key</param>
            <param name="val">a value</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Insert(System.Char[],System.Int32,System.Char)">
            <summary>Insert key.</summary>
            <param name="key">the key</param>
            <param name="start">offset into key array</param>
            <param name="val">a value</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Insert(System.Char,System.Char[],System.Int32,System.Char)">
            <summary>The actual insertion function, recursive version.</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Strcmp(System.Char[],System.Int32,System.Char[],System.Int32)">
            <summary>Compares 2 null terminated char arrays</summary>
            <param name="a">a character array</param>
            <param name="startA">an index into character array</param>
            <param name="b">a character array</param>
            <param name="startB">an index into character array</param>
            <returns>an integer</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Strcmp(System.String,System.Char[],System.Int32)">
            <summary>Compares a string with null terminated char array</summary>
            <param name="str">a string</param>
            <param name="a">a character array</param>
            <param name="start">an index into character array</param>
            <returns>an integer</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Strcpy(System.Char[],System.Int32,System.Char[],System.Int32)">
            <param name="dst">a character array</param>
            <param name="di">an index into character array</param>
            <param name="src">a character array</param>
            <param name="si">an index into character array</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Strlen(System.Char[],System.Int32)">
            <param name="a">a character array</param>
            <param name="start">an index into character array</param>
            <returns>an integer</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Strlen(System.Char[])">
            <param name="a">a character array</param>
            <returns>an integer</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Find(System.String)">
            <summary>Find key.</summary>
            <param name="key">the key</param>
            <returns>result</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Find(System.Char[],System.Int32)">
            <summary>Find key.</summary>
            <param name="key">the key</param>
            <param name="start">offset into key array</param>
            <returns>result</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Knows(System.String)">
            <param name="key">a key</param>
            <returns>trye if key present</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Size">
            <returns>length</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.InsertBalanced(System.String[],System.Char[],System.Int32,System.Int32)">
            <summary>
            Recursively insert the median first and then the median of the
            lower and upper halves, and so on in order to get a balanced
            tree.
            </summary>
            <remarks>
            Recursively insert the median first and then the median of the
            lower and upper halves, and so on in order to get a balanced
            tree. The array of keys is assumed to be sorted in ascending
            order.
            </remarks>
            <param name="k">array of keys</param>
            <param name="v">array of values</param>
            <param name="offset">where to insert</param>
            <param name="n">count to insert</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Balance">
            <summary>Balance the tree for best search performance</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.TrimToSize">
            <summary>
            Each node stores a character (splitchar) which is part of
            some key(s).
            </summary>
            <remarks>
            Each node stores a character (splitchar) which is part of
            some key(s). In a compressed branch (one that only contain
            a single string key) the trailer of the key which is not
            already in nodes is stored  externally in the kv array.
            As items are inserted, key substrings decrease.
            Some substrings may completely  disappear when the whole
            branch is totally decompressed.
            The tree is traversed to find the key substrings actually
            used. In addition, duplicate substrings are removed using
            a map (implemented with a TernaryTree!).
            </remarks>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTree.Keys">
            <returns>the keys</returns>
        </member>
        <member name="T:iText.Layout.Hyphenation.IPatternConsumer">
            <summary>
            <p>This interface is used to connect the XML pattern file parser to
            the hyphenation tree.</p>
            <p>This work was authored by Carlos Villegas (<EMAIL>).</p>
            </summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.IPatternConsumer.AddClass(System.String)">
            <summary>Add a character class.</summary>
            <remarks>
            Add a character class.
            A character class defines characters that are considered
            equivalent for the purpose of hyphenation (e.g. "aA"). It
            usually means to ignore case.
            </remarks>
            <param name="chargroup">character group</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.IPatternConsumer.AddException(System.String,System.Collections.IList)">
            <summary>Add a hyphenation exception.</summary>
            <remarks>
            Add a hyphenation exception. An exception replaces the
            result obtained by the algorithm for cases for which this
            fails or the user wants to provide his own hyphenation.
            A hyphenatedword is a vector of alternating String's and
            <see cref="T:iText.Layout.Hyphenation.Hyphen">Hyphen</see>
            instances
            </remarks>
            <param name="word">word to add as an exception</param>
            <param name="hyphenatedword">pre-hyphenated word</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.IPatternConsumer.AddPattern(System.String,System.String)">
            <summary>Add hyphenation patterns.</summary>
            <param name="pattern">the pattern</param>
            <param name="values">
            interletter values expressed as a string of
            digit characters.
            </param>
        </member>
        <member name="F:iText.Layout.Hyphenation.HyphenationTree.vspace">
            <summary>value space: stores the interletter values</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.HyphenationTree.stoplist">
            <summary>This map stores hyphenation exceptions</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.HyphenationTree.classmap">
            <summary>This map stores the character classes</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.HyphenationTree.ivalues">
            <summary>Temporary map to store interletter values on pattern loading.</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTree.#ctor">
            <summary>Default constructor.</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTree.PackValues(System.String)">
            <summary>
            Packs the values by storing them in 4 bits, two values into a byte
            Values range is from 0 to 9.
            </summary>
            <remarks>
            Packs the values by storing them in 4 bits, two values into a byte
            Values range is from 0 to 9. We use zero as terminator,
            so we'll add 1 to the value.
            </remarks>
            <param name="values">
            a string of digits from '0' to '9' representing the
            interletter values.
            </param>
            <returns>
            the index into the vspace array where the packed values
            are stored.
            </returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTree.UnpackValues(System.Int32)">
            <summary>Unpack values.</summary>
            <param name="k">an integer</param>
            <returns>a string</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTree.LoadPatterns(System.String)">
            <summary>Read hyphenation patterns from an XML file.</summary>
            <param name="filename">the filename</param>
            <exception cref="T:iText.Layout.Hyphenation.HyphenationException">In case the parsing fails</exception>
            <exception cref="T:iText.Layout.Hyphenation.HyphenationException"/>
            <exception cref="T:System.IO.FileNotFoundException"/>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTree.LoadPatterns(System.IO.Stream,System.String)">
            <summary>Read hyphenation patterns from an XML file.</summary>
            <param name="stream">the InputSource for the file</param>
            <param name="name">unique key representing country-language combination</param>
            <exception cref="T:iText.Layout.Hyphenation.HyphenationException">In case the parsing fails</exception>
            <exception cref="T:iText.Layout.Hyphenation.HyphenationException"/>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTree.FindPattern(System.String)">
            <summary>Find pattern.</summary>
            <param name="pat">a pattern</param>
            <returns>a string</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTree.Hstrcmp(System.Char[],System.Int32,System.Char[],System.Int32)">
            <summary>
            String compare, returns 0 if equal or
            t is a substring of s.
            </summary>
            <param name="s">first character array</param>
            <param name="si">starting index into first array</param>
            <param name="t">second character array</param>
            <param name="ti">starting index into second array</param>
            <returns>an integer</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTree.GetValues(System.Int32)">
            <summary>Get values.</summary>
            <param name="k">an integer</param>
            <returns>a byte array</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:iText.Layout.Hyphenation.HyphenationTree.SearchPatterns(System.Char[],System.Int32,System.Byte[])" -->
        <member name="M:iText.Layout.Hyphenation.HyphenationTree.Hyphenate(System.String,System.Int32,System.Int32)">
            <summary>Hyphenate word and return a Hyphenation object.</summary>
            <param name="word">the word to be hyphenated</param>
            <param name="remainCharCount">
            Minimum number of characters allowed
            before the hyphenation point.
            </param>
            <param name="pushCharCount">
            Minimum number of characters allowed after
            the hyphenation point.
            </param>
            <returns>
            a
            <see cref="T:iText.Layout.Hyphenation.Hyphenation">Hyphenation</see>
            object representing
            the hyphenated word or null if word is not hyphenated.
            </returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTree.Hyphenate(System.Char[],System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>Hyphenate word and return an array of hyphenation points.</summary>
            <param name="w">char array that contains the word</param>
            <param name="offset">Offset to first character in word</param>
            <param name="len">Length of word</param>
            <param name="remainCharCount">
            Minimum number of characters allowed
            before the hyphenation point.
            </param>
            <param name="pushCharCount">
            Minimum number of characters allowed after
            the hyphenation point.
            </param>
            <returns>
            a
            <see cref="T:iText.Layout.Hyphenation.Hyphenation">Hyphenation</see>
            object representing
            the hyphenated word or null if word is not hyphenated.
            </returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTree.AddClass(System.String)">
            <summary>Add a character class to the tree.</summary>
            <remarks>
            Add a character class to the tree. It is used by
            <see cref="T:iText.Layout.Hyphenation.PatternParser">PatternParser</see>
            as callback to
            add character classes. Character classes define the
            valid word characters for hyphenation. If a word contains
            a character not defined in any of the classes, it is not hyphenated.
            It also defines a way to normalize the characters in order
            to compare them with the stored patterns. Usually pattern
            files use only lower case characters, in this case a class
            for letter 'a', for example, should be defined as "aA", the first
            character being the normalization char.
            </remarks>
            <param name="chargroup">a character class (group)</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTree.AddException(System.String,System.Collections.IList)">
            <summary>Add an exception to the tree.</summary>
            <remarks>
            Add an exception to the tree. It is used by
            <see cref="T:iText.Layout.Hyphenation.PatternParser">PatternParser</see>
            class as callback to
            store the hyphenation exceptions.
            </remarks>
            <param name="word">normalized word</param>
            <param name="hyphenatedword">
            a vector of alternating strings and
            <see cref="T:iText.Layout.Hyphenation.Hyphen">hyphen</see>
            objects.
            </param>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTree.AddPattern(System.String,System.String)">
            <summary>Add a pattern to the tree.</summary>
            <remarks>
            Add a pattern to the tree. Mainly, to be used by
            <see cref="T:iText.Layout.Hyphenation.PatternParser">PatternParser</see>
            class as callback to
            add a pattern to the tree.
            </remarks>
            <param name="pattern">the hyphenation pattern</param>
            <param name="ivalue">
            interletter weight values indicating the
            desirability and priority of hyphenating at a given point
            within the pattern. It should contain only digit characters.
            (i.e. '0' to '9').
            </param>
        </member>
        <member name="T:iText.Layout.Hyphenation.HyphenationTreeCache">
            <summary><p>This is a cache for HyphenationTree instances.</p></summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.HyphenationTreeCache.hyphenTrees">
            <summary>Contains the cached hyphenation trees</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.HyphenationTreeCache.missingHyphenationTrees">
            <summary>Used to avoid multiple error messages for the same language if a pattern file is missing.</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTreeCache.GetHyphenationTree(System.String,System.String)">
            <summary>Looks in the cache if a hyphenation tree is available and returns it if it is found.</summary>
            <param name="lang">the language</param>
            <param name="country">the country (may be null or "none")</param>
            <returns>the HyhenationTree instance or null if it's not in the cache</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTreeCache.ConstructLlccKey(System.String,System.String)">
            <summary>Constructs the key for the hyphenation pattern file.</summary>
            <param name="lang">the language</param>
            <param name="country">the country (may be null or "none")</param>
            <returns>the resulting key</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTreeCache.ConstructUserKey(System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            If the user configured a hyphenation pattern file name
            for this (lang,country) value, return it.
            </summary>
            <remarks>
            If the user configured a hyphenation pattern file name
            for this (lang,country) value, return it. If not, return null.
            </remarks>
            <param name="lang">the language</param>
            <param name="country">the country (may be null or "none")</param>
            <param name="hyphPatNames">the map of user-configured hyphenation pattern file names</param>
            <returns>the hyphenation pattern file name or null</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTreeCache.Cache(System.String,iText.Layout.Hyphenation.HyphenationTree)">
            <summary>Cache a hyphenation tree under its key.</summary>
            <param name="key">the key (ex. "de_CH" or "en")</param>
            <param name="hTree">the hyphenation tree</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTreeCache.NoteMissing(System.String)">
            <summary>Notes a key to a hyphenation tree as missing.</summary>
            <remarks>
            Notes a key to a hyphenation tree as missing.
            This is to avoid searching a second time for a hyphenation pattern file which is not
            available.
            </remarks>
            <param name="key">the key (ex. "de_CH" or "en")</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.HyphenationTreeCache.IsMissing(System.String)">
            <summary>Indicates whether a hyphenation file has been requested before but it wasn't available.</summary>
            <remarks>
            Indicates whether a hyphenation file has been requested before but it wasn't available.
            This is to avoid searching a second time for a hyphenation pattern file which is not
            available.
            </remarks>
            <param name="key">the key (ex. "de_CH" or "en")</param>
            <returns>true if the hyphenation tree is unavailable</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "T:iText.Layout.Hyphenation.Hyphenator" -->
        <member name="F:iText.Layout.Hyphenation.Hyphenator.log">
            <summary>Logging instance.</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenator.#ctor(System.String,System.String,System.Int32,System.Int32)">
            <summary>Creates a new hyphenator.</summary>
            <param name="lang">the language</param>
            <param name="country">the optional country code (may be null or "none")</param>
            <param name="leftMin">the minimum number of characters before the hyphenation point</param>
            <param name="rightMin">the minimum number of characters after the hyphenation point</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenator.#ctor(System.String,System.String,System.Int32,System.Int32,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>Creates a new hyphenator.</summary>
            <param name="lang">the language</param>
            <param name="country">the optional country code (may be null or "none")</param>
            <param name="hyphPathNames">the map with user-configured hyphenation pattern file names</param>
            <param name="leftMin">the minimum number of characters before the hyphenation point</param>
            <param name="rightMin">the minimum number of characters after the hyphenation point</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenator.RegisterAdditionalHyphenationFileDirectory(System.String)">
            <summary>Registers additional file directories.</summary>
            <param name="directory">directory to register</param>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenator.GetHyphenationTreeCache">
            <summary>Returns the default hyphenation tree cache.</summary>
            <returns>the default (static) hyphenation tree cache</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenator.ClearHyphenationTreeCache">
            <summary>Clears the default hyphenation tree cache.</summary>
            <remarks>Clears the default hyphenation tree cache. This method can be used if the underlying data files are changed at runtime.
                </remarks>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenator.GetHyphenationTree(System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Returns a hyphenation tree for a given language and country,
            with fallback from (lang,country) to (lang).
            </summary>
            <remarks>
            Returns a hyphenation tree for a given language and country,
            with fallback from (lang,country) to (lang).
            The hyphenation trees are cached.
            </remarks>
            <param name="lang">the language</param>
            <param name="country">the country (may be null or "none")</param>
            <param name="hyphPathNames">the map with user-configured hyphenation pattern file names</param>
            <returns>the hyphenation tree</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenator.GetHyphenationTree2(System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>Returns a hyphenation tree for a given language and country.</summary>
            <remarks>Returns a hyphenation tree for a given language and country. The hyphenation trees are cached.</remarks>
            <param name="lang">the language</param>
            <param name="country">the country (may be null or "none")</param>
            <param name="hyphPathNames">the map with user-configured hyphenation pattern file names</param>
            <returns>the hyphenation tree</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenator.GetHyphenationTree(System.String,System.String)">
            <summary>Load tree from xml file using configuration settings.</summary>
            <param name="searchDirectory">the directory to search the file into</param>
            <param name="key">language key for the requested hyphenation file</param>
            <returns>the requested HyphenationTree or null if it is not available</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenator.GetHyphenationTree(System.IO.Stream,System.String)">
            <summary>Load tree from the stream.</summary>
            <param name="in">the input stream to load the tree from</param>
            <param name="name">unique key representing country-language combination</param>
            <returns>the requested HyphenationTree or null if it is not available</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenator.Hyphenate(System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.String,System.Int32,System.Int32)">
            <summary>Hyphenates a word.</summary>
            <param name="lang">the language</param>
            <param name="country">the optional country code (may be null or "none")</param>
            <param name="hyphPathNames">the map with user-configured hyphenation pattern file names</param>
            <param name="word">the word to hyphenate</param>
            <param name="leftMin">the minimum number of characters before the hyphenation point</param>
            <param name="rightMin">the minimum number of characters after the hyphenation point</param>
            <returns>the hyphenation result</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenator.Hyphenate(System.String,System.String,System.String,System.Int32,System.Int32)">
            <summary>Hyphenates a word.</summary>
            <param name="lang">the language</param>
            <param name="country">the optional country code (may be null or "none")</param>
            <param name="word">the word to hyphenate</param>
            <param name="leftMin">the minimum number of characters before the hyphenation point</param>
            <param name="rightMin">the minimum number of characters after the hyphenation point</param>
            <returns>the hyphenation result</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.Hyphenator.Hyphenate(System.String)">
            <summary>Hyphenates a word.</summary>
            <param name="word">the word to hyphenate</param>
            <returns>the hyphenation result</returns>
        </member>
        <member name="T:iText.Layout.Hyphenation.PatternParser">
            <summary>
            <p>A SAX document handler to read and parse hyphenation patterns
            from a XML file.</p>
            <p>This work was authored by Carlos Villegas (<EMAIL>).</p>
            </summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.PatternParser.#ctor">
            <summary>Construct a pattern parser.</summary>
            <exception cref="T:iText.Layout.Hyphenation.HyphenationException">if a hyphenation exception is raised</exception>
            <exception cref="T:iText.Layout.Hyphenation.HyphenationException"/>
        </member>
        <member name="M:iText.Layout.Hyphenation.PatternParser.#ctor(iText.Layout.Hyphenation.IPatternConsumer)">
            <summary>Construct a pattern parser.</summary>
            <param name="consumer">a pattern consumer</param>
            <exception cref="T:iText.Layout.Hyphenation.HyphenationException">if a hyphenation exception is raised</exception>
            <exception cref="T:iText.Layout.Hyphenation.HyphenationException"/>
        </member>
        <member name="M:iText.Layout.Hyphenation.PatternParser.Parse(System.String)">
            <summary>Parses a hyphenation pattern file.</summary>
            <param name="filename">the filename</param>
            <exception cref="T:iText.Layout.Hyphenation.HyphenationException">In case of an exception while parsing</exception>
            <exception cref="T:iText.Layout.Hyphenation.HyphenationException"/>
            <exception cref="T:System.IO.FileNotFoundException"/>
        </member>
        <member name="M:iText.Layout.Hyphenation.PatternParser.Parse(System.IO.Stream,System.String)">
            <summary>Parses a hyphenation pattern file.</summary>
            <param name="stream">the InputStream for the file</param>
            <param name="name">unique key representing country-language combination</param>
            <exception cref="T:iText.Layout.Hyphenation.HyphenationException">In case of an exception while parsing</exception>
            <exception cref="T:iText.Layout.Hyphenation.HyphenationException"/>
        </member>
        <member name="M:iText.Layout.Hyphenation.PatternParser.GetExternalClasses">
            <exception cref="!:Org.Xml.Sax.SAXException">if not caught</exception>
        </member>
        <member name="T:iText.Layout.Hyphenation.TernaryTreeIterator">
            <summary>
            An object that iterates over the
            <see cref="T:iText.Layout.Hyphenation.TernaryTree"/>
            .
            </summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.TernaryTreeIterator.cur">
            <summary>current node index</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.TernaryTreeIterator.curkey">
            <summary>current key</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.TernaryTreeIterator.ns">
            <summary>Node stack</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.TernaryTreeIterator.ks">
            <summary>key stack implemented with a StringBuffer</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTreeIterator.#ctor(iText.Layout.Hyphenation.TernaryTree)">
            <summary>default constructor</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTreeIterator.Rewind">
            <summary>rewind iterator</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTreeIterator.Reset">
            <summary>Resets the Iterator to its initial state.</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTreeIterator.GetValue">
            <returns>value</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTreeIterator.MoveNext">
            <returns>true if more elements</returns>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTreeIterator.Up">
            <summary>traverse upwards</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTreeIterator.Run">
            <summary>traverse the tree to find next key</summary>
        </member>
        <member name="P:iText.Layout.Hyphenation.TernaryTreeIterator.Current">
            <returns>next element</returns>
        </member>
        <member name="F:iText.Layout.Hyphenation.TernaryTreeIterator.Item.parent">
            <summary>parent</summary>
        </member>
        <member name="F:iText.Layout.Hyphenation.TernaryTreeIterator.Item.child">
            <summary>child</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTreeIterator.Item.#ctor(iText.Layout.Hyphenation.TernaryTreeIterator)">
            <summary>default constructor</summary>
        </member>
        <member name="M:iText.Layout.Hyphenation.TernaryTreeIterator.Item.#ctor(iText.Layout.Hyphenation.TernaryTreeIterator,System.Char,System.Char)">
            <summary>Construct item.</summary>
            <param name="p">a char</param>
            <param name="c">a char</param>
        </member>
        <member name="T:iText.Layout.Layout.LayoutArea">
            <summary>
            Represents the area for content
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            .
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutArea.pageNumber">
            <summary>The number of page on which the area is located.</summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutArea.bBox">
            <summary>The area's bounding box</summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutArea.emptyArea">
            <summary>Indicates whether the area already has some placed content or not.</summary>
        </member>
        <member name="M:iText.Layout.Layout.LayoutArea.#ctor(System.Int32,iText.Kernel.Geom.Rectangle)">
            <summary>
            Creates the area for content
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            .
            </summary>
            <param name="pageNumber">the number of page on which the area is located.</param>
            <param name="bBox">the area's bounding box</param>
        </member>
        <member name="M:iText.Layout.Layout.LayoutArea.GetPageNumber">
            <summary>Gets the number of page on which the area is located.</summary>
            <returns>page number</returns>
        </member>
        <member name="M:iText.Layout.Layout.LayoutArea.GetBBox">
            <summary>
            Gets the
            <see cref="T:iText.Kernel.Geom.Rectangle">box</see>
            which bounds the area.
            </summary>
            <returns>the bounding box</returns>
        </member>
        <member name="M:iText.Layout.Layout.LayoutArea.SetBBox(iText.Kernel.Geom.Rectangle)">
            <summary>
            Sets the
            <see cref="T:iText.Kernel.Geom.Rectangle">box</see>
            which bounds the area.
            </summary>
        </member>
        <member name="M:iText.Layout.Layout.LayoutArea.IsEmptyArea">
            <summary>Indicates whether the area already has some placed content or not.</summary>
            <returns>whether the area is empty or not</returns>
        </member>
        <member name="M:iText.Layout.Layout.LayoutArea.SetEmptyArea(System.Boolean)">
            <summary>Defines whether the area already has some placed content or not.</summary>
        </member>
        <member name="M:iText.Layout.Layout.LayoutArea.Clone">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Layout.LayoutArea.Equals(System.Object)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Layout.LayoutArea.GetHashCode">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Layout.LayoutArea.ToString">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Layout.Layout.LayoutContext">
            <summary>
            Represents the context for content
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            .
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutContext.area">
            <summary>
            The
            <see cref="T:iText.Layout.Layout.LayoutArea">area</see>
            the content to be placed on.
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutContext.clippedHeight">
            <summary>Indicates whether the height is clipped or not.</summary>
        </member>
        <member name="M:iText.Layout.Layout.LayoutContext.GetArea">
            <summary>
            Gets the
            <see cref="T:iText.Layout.Layout.LayoutArea">area</see>
            the content to be placed on.
            </summary>
            <returns>the area for content layouting.</returns>
        </member>
        <member name="M:iText.Layout.Layout.LayoutContext.IsClippedHeight">
            <summary>Indicates whether the layout area's height is clipped or not.</summary>
            <returns>whether the layout area's height is clipped or not.</returns>
        </member>
        <member name="M:iText.Layout.Layout.LayoutContext.SetClippedHeight(System.Boolean)">
            <summary>Defines whether the layout area's height is clipped or not.</summary>
        </member>
        <member name="M:iText.Layout.Layout.LayoutContext.ToString">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Layout.Layout.LayoutPosition">
            <summary>We use a simplified version of CSS positioning.</summary>
            <remarks>
            We use a simplified version of CSS positioning.
            See https://www.webkit.org/blog/117/webcore-rendering-iv-absolutefixed-and-relative-positioning
            </remarks>
        </member>
        <member name="F:iText.Layout.Layout.LayoutPosition.STATIC">
            <summary>Default positioning by normal rules of block and line layout.</summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutPosition.RELATIVE">
            <summary>
            Relative positioning is exactly like static positioning except that the left, top, right and bottom properties
            can be used to apply a translation to the object.
            </summary>
            <remarks>
            Relative positioning is exactly like static positioning except that the left, top, right and bottom properties
            can be used to apply a translation to the object. Relative positioning is literally nothing more than a paint-time translation.
            As far as layout is concerned, the object is at its original position.
            </remarks>
        </member>
        <member name="F:iText.Layout.Layout.LayoutPosition.ABSOLUTE">
            <summary>
            Absolute positioned objects are positioned relative to the containing block, which is the nearest enclosing
            ancestor block with a position other than 'static'.
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutPosition.FIXED">
            <summary>Fixed positioned objects are positioned relative to the viewport, i.e., the page area of the current page.
                </summary>
        </member>
        <member name="M:com.itextpdf.layout.layout.LayoutRect.GetCommonRectangle(com.itextpdf.layout.layout.LayoutRect[])">
            <summary>Calculates the common rectangle which includes all the input rectangles.
            	</summary>
            <param name="rectangles">list of input rectangles.</param>
            <returns>common rectangle.</returns>
        </member>
        <member name="T:iText.Layout.Layout.LayoutResult">
            <summary>
            Represents the result of content
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            .
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutResult.FULL">
            <summary>
            The status of
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            which indicates that the content was fully placed.
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutResult.PARTIAL">
            <summary>
            The status of
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            which indicates that the content was placed partially.
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutResult.NOTHING">
            <summary>
            The status of
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            which indicates that the content was not placed.
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutResult.status">
            <summary>
            The status of
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            which indicates whether the content was added or not
            and, if yes, was it added fully or partially.
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutResult.occupiedArea">
            <summary>
            The area occupied by the content during its
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            .
            which indicates whether the content was added or not and, if yes, was it added fully or partially.
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutResult.splitRenderer">
            <summary>
            The split renderer created during
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            .
            This renderer will be used to draw the splitted part of content.
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutResult.overflowRenderer">
            <summary>
            The overflow renderer created during
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            .
            This renderer will be used to draw the overflowed part of content.
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.LayoutResult.causeOfNothing">
            <summary>
            The first renderer to produce
            <see cref="F:iText.Layout.Layout.LayoutResult.NOTHING"/>
            during
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            .
            </summary>
        </member>
        <member name="M:iText.Layout.Layout.LayoutResult.#ctor(System.Int32,iText.Layout.Layout.LayoutArea,iText.Layout.Renderer.IRenderer,iText.Layout.Renderer.IRenderer)">
            <summary>
            Creates the
            <see cref="T:iText.Layout.Layout.LayoutResult"/>
            result of
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            }.
            The
            <see cref="F:iText.Layout.Layout.LayoutResult.causeOfNothing"/>
            will be set as null.
            </summary>
            <param name="status">
            the status of
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            </param>
            <param name="occupiedArea">the area occupied by the content</param>
            <param name="splitRenderer">the renderer to draw the splitted part of the content</param>
            <param name="overflowRenderer">the renderer to draw the overflowed part of the content</param>
        </member>
        <member name="M:iText.Layout.Layout.LayoutResult.#ctor(System.Int32,iText.Layout.Layout.LayoutArea,iText.Layout.Renderer.IRenderer,iText.Layout.Renderer.IRenderer,iText.Layout.Renderer.IRenderer)">
            <summary>
            Creates the
            <see cref="T:iText.Layout.Layout.LayoutResult"/>
            result of
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            }.
            </summary>
            <param name="status">
            the status of
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            </param>
            <param name="occupiedArea">the area occupied by the content</param>
            <param name="splitRenderer">the renderer to draw the splitted part of the content</param>
            <param name="overflowRenderer">the renderer to draw the overflowed part of the content</param>
            <param name="cause">
            the first renderer to produce
            <see cref="F:iText.Layout.Layout.LayoutResult.NOTHING"/>
            </param>
        </member>
        <member name="M:iText.Layout.Layout.LayoutResult.GetStatus">
            <summary>
            Gets the status of
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            .
            </summary>
            <returns>the status</returns>
        </member>
        <member name="M:iText.Layout.Layout.LayoutResult.SetStatus(System.Int32)">
            <summary>
            Sets the status of
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            .
            </summary>
        </member>
        <member name="M:iText.Layout.Layout.LayoutResult.GetOccupiedArea">
            <summary>
            Gets the
            <see cref="T:iText.Layout.Layout.LayoutArea">layout area</see>
            occupied by the content during
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            .
            </summary>
            <returns>
            the
            <see cref="T:iText.Layout.Layout.LayoutArea">layout area</see>
            occupied by the content
            </returns>
        </member>
        <member name="M:iText.Layout.Layout.LayoutResult.GetSplitRenderer">
            <summary>
            Gets the split
            <see cref="T:iText.Layout.Renderer.IRenderer">renderer</see>
            created during
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            .
            </summary>
            <returns>
            the
            <see cref="T:iText.Layout.Renderer.IRenderer">renderer</see>
            </returns>
        </member>
        <member name="M:iText.Layout.Layout.LayoutResult.SetSplitRenderer(iText.Layout.Renderer.IRenderer)">
            <summary>
            Sets the split
            <see cref="T:iText.Layout.Renderer.IRenderer">renderer</see>
            .
            </summary>
        </member>
        <member name="M:iText.Layout.Layout.LayoutResult.GetOverflowRenderer">
            <summary>
            Gets the overflow renderer created during
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            .
            </summary>
            <returns>
            the
            <see cref="T:iText.Layout.Renderer.IRenderer">renderer</see>
            </returns>
        </member>
        <member name="M:iText.Layout.Layout.LayoutResult.SetOverflowRenderer(iText.Layout.Renderer.IRenderer)">
            <summary>
            Sets the overflow
            <see cref="T:iText.Layout.Renderer.IRenderer">renderer</see>
            .
            </summary>
        </member>
        <member name="M:iText.Layout.Layout.LayoutResult.GetCauseOfNothing">
            <summary>
            Gets the first renderer to produce
            <see cref="F:iText.Layout.Layout.LayoutResult.NOTHING"/>
            during
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            </summary>
            <returns>
            the
            <see cref="T:iText.Layout.Renderer.IRenderer">renderer</see>
            </returns>
        </member>
        <member name="M:iText.Layout.Layout.LayoutResult.ToString">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Layout.Layout.LineLayoutResult">
            <summary>
            Represents the result of a line
            <see cref="M:iText.Layout.Renderer.LineRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            .
            </summary>
        </member>
        <member name="T:iText.Layout.Layout.MinMaxWidthLayoutResult">
            <summary>
            Represents the result of content
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            .
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.LineLayoutResult.splitForcedByNewline">
            <summary>Indicates whether split was forced by new line symbol or not.</summary>
        </member>
        <member name="M:iText.Layout.Layout.LineLayoutResult.#ctor(System.Int32,iText.Layout.Layout.LayoutArea,iText.Layout.Renderer.IRenderer,iText.Layout.Renderer.IRenderer)">
            <summary>
            Creates the
            <see cref="T:iText.Layout.Layout.LayoutResult"/>
            result of
            <see cref="M:iText.Layout.Renderer.LineRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            }.
            The
            <see cref="F:iText.Layout.Layout.LayoutResult.causeOfNothing"/>
            will be set as null.
            </summary>
            <param name="status">
            the status of
            <see cref="M:iText.Layout.Renderer.LineRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            </param>
            <param name="occupiedArea">the area occupied by the content</param>
            <param name="splitRenderer">the renderer to draw the splitted part of the content</param>
            <param name="overflowRenderer">the renderer to draw the overflowed part of the content</param>
        </member>
        <member name="M:iText.Layout.Layout.LineLayoutResult.#ctor(System.Int32,iText.Layout.Layout.LayoutArea,iText.Layout.Renderer.IRenderer,iText.Layout.Renderer.IRenderer,iText.Layout.Renderer.IRenderer)">
            <summary>
            Creates the
            <see cref="T:iText.Layout.Layout.LayoutResult"/>
            result of
            <see cref="M:iText.Layout.Renderer.LineRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            }.
            </summary>
            <param name="status">
            the status of
            <see cref="M:iText.Layout.Renderer.LineRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            </param>
            <param name="occupiedArea">the area occupied by the content</param>
            <param name="splitRenderer">the renderer to draw the splitted part of the content</param>
            <param name="overflowRenderer">the renderer to draw the overflowed part of the content</param>
            <param name="cause">
            the first renderer to produce
            <see cref="F:iText.Layout.Layout.LayoutResult.NOTHING"/>
            </param>
        </member>
        <member name="M:iText.Layout.Layout.LineLayoutResult.IsSplitForcedByNewline">
            <summary>Indicates whether split was forced by new line symbol in rendered text.</summary>
            <remarks>
            Indicates whether split was forced by new line symbol in rendered text.
            The value will be set as true if, for example,
            the rendered text of one of the child renderers contains '\n' symbol.
            </remarks>
            <returns>whether split was forced by new line or not</returns>
        </member>
        <member name="M:iText.Layout.Layout.LineLayoutResult.SetSplitForcedByNewline(System.Boolean)">
            <summary>
            Sets
            <see cref="M:iText.Layout.Layout.LineLayoutResult.SetSplitForcedByNewline(System.Boolean)"/>
            </summary>
            <param name="isSplitForcedByNewline">indicates that split was forced by new line symbol in rendered text.</param>
            <returns>
            
            <see cref="T:iText.Layout.Layout.LineLayoutResult">this layout result</see>
            the setting was applied on.
            </returns>
            <seealso cref="M:iText.Layout.Layout.LineLayoutResult.SetSplitForcedByNewline(System.Boolean)"/>
        </member>
        <member name="T:iText.Layout.Layout.TextLayoutResult">
            <summary>
            Represents the result of a text
            <see cref="M:iText.Layout.Renderer.TextRenderer.Layout(iText.Layout.Layout.LayoutContext)">layout</see>
            .
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.TextLayoutResult.wordHasBeenSplit">
            <summary>
            Indicates whether some word was splitted during
            <see cref="M:iText.Layout.Renderer.TextRenderer.Layout(iText.Layout.Layout.LayoutContext)">layout</see>
            .
            </summary>
        </member>
        <member name="F:iText.Layout.Layout.TextLayoutResult.splitForcedByNewline">
            <summary>Indicates whether split was forced by new line symbol in text or not.</summary>
        </member>
        <member name="M:iText.Layout.Layout.TextLayoutResult.#ctor(System.Int32,iText.Layout.Layout.LayoutArea,iText.Layout.Renderer.IRenderer,iText.Layout.Renderer.IRenderer)">
            <summary>
            Creates the
            <see cref="T:iText.Layout.Layout.LayoutResult"/>
            result of
            <see cref="M:iText.Layout.Renderer.TextRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            }.
            The
            <see cref="F:iText.Layout.Layout.LayoutResult.causeOfNothing"/>
            will be set as null.
            </summary>
            <param name="status">
            the status of
            <see cref="M:iText.Layout.Renderer.TextRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            </param>
            <param name="occupiedArea">the area occupied by the content</param>
            <param name="splitRenderer">the renderer to draw the splitted part of the content</param>
            <param name="overflowRenderer">the renderer to draw the overflowed part of the content</param>
        </member>
        <member name="M:iText.Layout.Layout.TextLayoutResult.#ctor(System.Int32,iText.Layout.Layout.LayoutArea,iText.Layout.Renderer.IRenderer,iText.Layout.Renderer.IRenderer,iText.Layout.Renderer.IRenderer)">
            <summary>
            Creates the
            <see cref="T:iText.Layout.Layout.LayoutResult"/>
            result of
            <see cref="M:iText.Layout.Renderer.TextRenderer.Layout(iText.Layout.Layout.LayoutContext)">layouting</see>
            }.
            </summary>
            <param name="status">
            the status of
            <see cref="M:iText.Layout.Renderer.TextRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            </param>
            <param name="occupiedArea">the area occupied by the content</param>
            <param name="splitRenderer">the renderer to draw the splitted part of the content</param>
            <param name="overflowRenderer">the renderer to draw the overflowed part of the content</param>
            <param name="cause">
            the first renderer to produce
            <see cref="F:iText.Layout.Layout.LayoutResult.NOTHING"/>
            </param>
        </member>
        <member name="M:iText.Layout.Layout.TextLayoutResult.IsWordHasBeenSplit">
            <summary>
            Indicates whether some word in a rendered text was splitted during
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layout</see>
            .
            The value will be set as true if, for example, the rendered words width is bigger than the width of layout area.
            </summary>
            <returns>whether some word was splitted or not.</returns>
        </member>
        <member name="M:iText.Layout.Layout.TextLayoutResult.SetWordHasBeenSplit(System.Boolean)">
            <summary>
            Sets
            <see cref="F:iText.Layout.Layout.TextLayoutResult.wordHasBeenSplit"/>
            </summary>
            <param name="wordHasBeenSplit">
            indicates that some word was splitted during
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)">layout</see>
            .
            </param>
            <returns>
            
            <see cref="T:iText.Layout.Layout.TextLayoutResult">this layout result</see>
            the setting was applied on
            </returns>
            <seealso cref="F:iText.Layout.Layout.TextLayoutResult.wordHasBeenSplit"/>
        </member>
        <member name="M:iText.Layout.Layout.TextLayoutResult.IsSplitForcedByNewline">
            <summary>Indicates whether split was forced by new line symbol in rendered text.</summary>
            <remarks>
            Indicates whether split was forced by new line symbol in rendered text.
            The value will be set as true if, for example, the rendered text contains '\n' symbol.
            This value can also be true even if the text was fully placed, but had line break at the end.
            </remarks>
            <returns>whether split was forced by new line or not.</returns>
        </member>
        <member name="M:iText.Layout.Layout.TextLayoutResult.SetSplitForcedByNewline(System.Boolean)">
            <summary>
            Sets
            <see cref="M:iText.Layout.Layout.TextLayoutResult.IsSplitForcedByNewline"/>
            </summary>
            <param name="isSplitForcedByNewline">indicates that split was forced by new line symbol in rendered text.</param>
            <returns>
            
            <see cref="T:iText.Layout.Layout.TextLayoutResult">this layout result</see>
            the setting was applied on.
            </returns>
            <seealso cref="M:iText.Layout.Layout.TextLayoutResult.SetSplitForcedByNewline(System.Boolean)"/>
        </member>
        <member name="T:iText.Layout.Margincollapse.MarginsCollapseHandler">
            <summary>
            Rules of the margins collapsing are taken from Mozilla Developer Network:
            https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Box_Model/Mastering_margin_collapsing
            See also:
            https://www.w3.org/TR/CSS2/box.html#collapsing-margins
            </summary>
        </member>
        <member name="T:iText.Layout.Minmaxwidth.RotationMinMaxWidth">
            <summary>Class for min-max-width of rotated elements.</summary>
            <remarks>
            Class for min-max-width of rotated elements.
            Also contains heuristic methods for it calculation based on the assumption that area of element stays the same
            when we try to layout it with different available width (available width is between min-width and max-width).
            </remarks>
        </member>
        <member name="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.#ctor(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)">
            <summary>Create new instance</summary>
            <param name="minWidth">min-width of rotated element</param>
            <param name="maxWidth">max-width of rotated element</param>
            <param name="minWidthOrigin">the width of not rotated element, that will have min-width after rotation</param>
            <param name="maxWidthOrigin">the width of not rotated element, that will have max-width after rotation</param>
            <param name="minWidthHeight">the height of rotated element, that have min-width as its rotated width</param>
            <param name="maxWidthHeight">the height of rotated element, that have min-width as its rotated width</param>
        </member>
        <member name="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.Calculate(System.Double,System.Double,iText.Layout.Minmaxwidth.MinMaxWidth)">
            <summary>
            Heuristic method, based on the assumption that area of element stays the same, when we try to
            layout it with different available width (available width is between min-width and max-width).
            </summary>
            <param name="angle">rotation angle in radians</param>
            <param name="area">the constant area</param>
            <param name="elementMinMaxWidth">NOT rotated element min-max-width</param>
            <returns>possible min-max-width of element after rotation</returns>
        </member>
        <member name="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.Calculate(System.Double,System.Double,iText.Layout.Minmaxwidth.MinMaxWidth,System.Double)">
            <summary>
            Heuristic method, based on the assumption that area of element stays the same, when we try to
            layout it with different available width (available width is between min-width and max-width).
            </summary>
            <param name="angle">rotation angle in radians</param>
            <param name="area">the constant area</param>
            <param name="elementMinMaxWidth">NOT rotated element min-max-width</param>
            <param name="availableWidth">the maximum width of area the element will occupy after rotation.</param>
            <returns>possible min-max-width of element after rotation</returns>
        </member>
        <member name="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.CalculateRotatedWidth(iText.Kernel.Geom.Rectangle,System.Double)">
            <summary>Utility method for calculating rotated width of area in a similar way to other calculations in this class.
                </summary>
            <param name="area">the initial area</param>
            <param name="angle">the rotation angle in radians</param>
            <returns>width of rotated area</returns>
        </member>
        <member name="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.Calculate(iText.Layout.Minmaxwidth.RotationMinMaxWidth.WidthFunction,System.Double,System.Double)">
            <summary>This method use derivative of function defined on interval: [xMin, xMax] to find its local minimum and maximum.
                </summary>
            <remarks>
            This method use derivative of function defined on interval: [xMin, xMax] to find its local minimum and maximum.
            It also calculate other handy values needed for the creation of
            <see cref="T:iText.Layout.Minmaxwidth.RotationMinMaxWidth"/>
            .
            </remarks>
            <param name="func">
            the
            <see cref="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.WidthFunction.GetRotatedWidth(System.Double)"/>
            of this instance is used as analysed function
            </param>
            <param name="xMin">the smallest possible value of function argument</param>
            <param name="xMax">the biggest possible value of function argument</param>
            <returns>
            the calculated
            <see cref="T:iText.Layout.Minmaxwidth.RotationMinMaxWidth"/>
            </returns>
        </member>
        <member name="T:iText.Layout.Minmaxwidth.RotationMinMaxWidth.WidthFunction">
            <summary>
            Class that represents functions used, for calculation of width of element after rotation
            based on it's NOT rotated width and assumption, that area of element stays the same when
            we try to layout it with different available width.
            </summary>
            <remarks>
            Class that represents functions used, for calculation of width of element after rotation
            based on it's NOT rotated width and assumption, that area of element stays the same when
            we try to layout it with different available width.
            Contains handy methods for function analysis.
            </remarks>
        </member>
        <member name="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.WidthFunction.#ctor(System.Double,System.Double)">
            <summary>Create new instance</summary>
            <param name="angle">rotation angle in radians</param>
            <param name="area">the constant area</param>
        </member>
        <member name="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.WidthFunction.GetRotatedWidth(System.Double)">
            <summary>Function used for width calculations of rotated element.</summary>
            <remarks>Function used for width calculations of rotated element. This function is continuous on interval: (0, Infinity)
                </remarks>
            <param name="x">width value of NOT rotated element</param>
            <returns>width of rotated element</returns>
        </member>
        <member name="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.WidthFunction.GetRotatedHeight(System.Double)">
            <summary>Function used for height calculations of rotated element.</summary>
            <remarks>Function used for height calculations of rotated element. This function is continuous on interval: (0, Infinity)
                </remarks>
            <param name="x">width value of NOT rotated element</param>
            <returns>width of rotated element</returns>
        </member>
        <member name="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.WidthFunction.GetValidOriginalWidths(System.Double)">
            <summary>Get's possible values of NOT rotated width of all element that have therer rotated width less that availableWidth
                </summary>
            <param name="availableWidth">the highest possible width of rotated element.</param>
            <returns>interval that specify biggest and smallest possible values of NOT rotated width of such elements.
                </returns>
        </member>
        <member name="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.WidthFunction.GetWidthDerivativeZeroPoint">
            <summary>
            Gets the argument of
            <see cref="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.WidthFunction.GetRotatedWidth(System.Double)"/>
            that results in zero derivative.
            In case we have
            <see cref="F:iText.Layout.Minmaxwidth.RotationMinMaxWidth.WidthFunction.sin"/>
            <c>== 0</c>
            or
            <see cref="F:iText.Layout.Minmaxwidth.RotationMinMaxWidth.WidthFunction.sin"/>
            <c>== 0</c>
            the function doesn't have
            zero derivative on defined interval, but value returned by this method fits well in the calculations above.
            </summary>
            <returns>
            the argument of
            <see cref="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.WidthFunction.GetRotatedWidth(System.Double)"/>
            that results in zero derivative
            </returns>
        </member>
        <member name="T:iText.Layout.Properties.Background">
            <summary>
            A specialized class holding configurable properties related to an
            <see cref="T:iText.Layout.Element.IElement"/>
            's background. This class is meant to be used as the value for the
            <see cref="F:iText.Layout.Properties.Property.BACKGROUND"/>
            key in an
            <see cref="T:iText.Layout.IPropertyContainer"/>
            . Allows
            to define a background color, and positive or negative changes to the
            location of the edges of the background coloring.
            </summary>
        </member>
        <member name="M:iText.Layout.Properties.Background.#ctor(iText.Kernel.Colors.Color)">
            <summary>Creates a background with a specified color.</summary>
            <param name="color">the background color</param>
        </member>
        <member name="M:iText.Layout.Properties.Background.#ctor(iText.Kernel.Colors.Color,System.Single)">
            <summary>Creates a background with a specified color and opacity.</summary>
            <param name="color">the background color</param>
            <param name="opacity">the opacity of the background color; a float between 0 and 1, where 1 stands for fully opaque color and 0 - for fully transparent
                </param>
        </member>
        <member name="M:iText.Layout.Properties.Background.#ctor(iText.Kernel.Colors.Color,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a background with a specified color, and extra space that
            must be counted as part of the background and therefore colored.
            </summary>
            <remarks>
            Creates a background with a specified color, and extra space that
            must be counted as part of the background and therefore colored.
            These values are allowed to be negative.
            </remarks>
            <param name="color">the background color</param>
            <param name="extraLeft">extra coloring to the left side</param>
            <param name="extraTop">extra coloring at the top</param>
            <param name="extraRight">extra coloring to the right side</param>
            <param name="extraBottom">extra coloring at the bottom</param>
        </member>
        <member name="M:iText.Layout.Properties.Background.#ctor(iText.Kernel.Colors.Color,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a background with a specified color, and extra space that
            must be counted as part of the background and therefore colored.
            </summary>
            <remarks>
            Creates a background with a specified color, and extra space that
            must be counted as part of the background and therefore colored.
            These values are allowed to be negative.
            </remarks>
            <param name="color">the background color</param>
            <param name="opacity">the opacity of the background color; a float between 0 and 1, where 1 stands for fully opaque color and 0 - for fully transparent
                </param>
            <param name="extraLeft">extra coloring to the left side</param>
            <param name="extraTop">extra coloring at the top</param>
            <param name="extraRight">extra coloring to the right side</param>
            <param name="extraBottom">extra coloring at the bottom</param>
        </member>
        <member name="M:iText.Layout.Properties.Background.GetColor">
            <summary>Gets the background's color.</summary>
            <returns>
            a
            <see cref="T:iText.Kernel.Colors.Color"/>
            of any supported kind
            </returns>
        </member>
        <member name="M:iText.Layout.Properties.Background.GetOpacity">
            <summary>Gets the opacity of the background.</summary>
            <returns>a float between 0 and 1, where 1 stands for fully opaque color and 0 - for fully transparent</returns>
        </member>
        <member name="M:iText.Layout.Properties.Background.GetExtraLeft">
            <summary>Gets the extra space that must be filled to the left of the Element.</summary>
            <returns>a float value</returns>
        </member>
        <member name="M:iText.Layout.Properties.Background.GetExtraRight">
            <summary>Gets the extra space that must be filled to the right of the Element.</summary>
            <returns>a float value</returns>
        </member>
        <member name="M:iText.Layout.Properties.Background.GetExtraTop">
            <summary>Gets the extra space that must be filled at the top of the Element.</summary>
            <returns>a float value</returns>
        </member>
        <member name="M:iText.Layout.Properties.Background.GetExtraBottom">
            <summary>Gets the extra space that must be filled at the bottom of the Element.</summary>
            <returns>a float value</returns>
        </member>
        <member name="T:iText.Layout.Properties.IListSymbolFactory">
            <summary>Interface for implementing custom symbols for lists</summary>
        </member>
        <member name="M:iText.Layout.Properties.IListSymbolFactory.CreateSymbol(System.Int32,iText.Layout.IPropertyContainer,iText.Layout.IPropertyContainer)">
            <summary>Creates symbol.</summary>
            <param name="index">- the positive (greater then zero) index of list item in list.</param>
            <param name="list">
            - the
            <see cref="T:iText.Layout.IPropertyContainer"/>
            with all properties of corresponding list.
            </param>
            <param name="listItem">
            - the
            <see cref="T:iText.Layout.IPropertyContainer"/>
            with all properties of corresponding list item.
            </param>
            <returns>
            the
            <see cref="T:iText.Layout.Element.IElement"/>
            representing symbol.
            </returns>
        </member>
        <member name="T:iText.Layout.Properties.Leading">
            <summary>
            A specialized class that specifies the leading, "the vertical distance between
            the baselines of adjacent lines of text" (ISO-32000-1, section 9.3.5).
            </summary>
            <remarks>
            A specialized class that specifies the leading, "the vertical distance between
            the baselines of adjacent lines of text" (ISO-32000-1, section 9.3.5).
            Allows to use either an absolute (constant) leading value, or one
            determined by font size. Pronounce as 'ledding' (cfr. Led Zeppelin).
            This class is meant to be used as the value for the
            <see cref="F:iText.Layout.Properties.Property.LEADING"/>
            key in an
            <see cref="T:iText.Layout.IPropertyContainer"/>
            .
            </remarks>
        </member>
        <member name="F:iText.Layout.Properties.Leading.FIXED">
            <summary>A leading type independent of font size.</summary>
        </member>
        <member name="F:iText.Layout.Properties.Leading.MULTIPLIED">
            <summary>A leading type related to the font size and the resulting bounding box.</summary>
        </member>
        <member name="M:iText.Layout.Properties.Leading.#ctor(System.Int32,System.Single)">
            <summary>Creates a Leading object.</summary>
            <param name="type">
            a constant type that defines the calculation of actual
            leading distance. Either
            <see cref="F:iText.Layout.Properties.Leading.FIXED"/>
            or
            <see cref="F:iText.Layout.Properties.Leading.MULTIPLIED"/>
            </param>
            <param name="value">to be used as a basis for the leading calculation.</param>
        </member>
        <member name="M:iText.Layout.Properties.Leading.GetLeadingType">
            <summary>Gets the calculation type of the Leading object.</summary>
            <returns>
            the calculation type. Either
            <see cref="F:iText.Layout.Properties.Leading.FIXED"/>
            or
            <see cref="F:iText.Layout.Properties.Leading.MULTIPLIED"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Properties.Leading.GetValue">
            <summary>Gets the value to be used as the basis for the leading calculation.</summary>
            <returns>a calculation value</returns>
        </member>
        <member name="T:iText.Layout.Properties.ListSymbolAlignment">
            <summary>A specialized enum containing alignment properties for list symbols.</summary>
        </member>
        <member name="T:iText.Layout.Properties.Property">
            <summary>
            An enum of property names that are used for graphical properties of layout
            elements.
            </summary>
            <remarks>
            An enum of property names that are used for graphical properties of layout
            elements. The
            <see cref="T:iText.Layout.IPropertyContainer"/>
            performs the same function as an
            <see cref="!:System.Collections.IDictionary&lt;K, V&gt;"/>
            , with the values of
            <see cref="T:iText.Layout.Properties.Property"/>
            as its potential keys.
            </remarks>
        </member>
        <member name="F:iText.Layout.Properties.Property.FONT_STYLE">
            <summary>String value.</summary>
            <remarks>
            String value. 'normal'|'italic'|'oblique'
            Note, this property will be applied only if
            <see cref="F:iText.Layout.Properties.Property.FONT"/>
            has String value.
            </remarks>
        </member>
        <member name="F:iText.Layout.Properties.Property.FONT_WEIGHT">
            <summary>String value.</summary>
            <remarks>
            String value. 'normal'|'bold'|number
            Note, this property will be applied only if
            <see cref="F:iText.Layout.Properties.Property.FONT"/>
            has String value.
            </remarks>
        </member>
        <member name="F:iText.Layout.Properties.Property.FONT_PROVIDER">
            <summary>
            Shall be instance of
            <see cref="T:iText.Layout.Font.FontProvider"/>
            </summary>
        </member>
        <member name="F:iText.Layout.Properties.Property.FONT_SET">
            <summary>
            Shall be instance of
            <see cref="T:iText.Layout.Font.FontSet"/>
            .
            </summary>
        </member>
        <member name="F:iText.Layout.Properties.Property.HORIZONTAL_SCALING">
            <summary>Value of 1 is equivalent to no scaling</summary>
        </member>
        <member name="F:iText.Layout.Properties.Property.TEXT_RENDERING_MODE">
            <summary>
            Use values from
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvasConstants.TextRenderingMode"/>
            .
            </summary>
        </member>
        <member name="F:iText.Layout.Properties.Property.VERTICAL_SCALING">
            <summary>Value of 1 is equivalent to no scaling</summary>
        </member>
        <member name="F:iText.Layout.Properties.Property.INHERITED_PROPERTIES">
            <summary>
            Some properties must be passed to
            <see cref="T:iText.Layout.IPropertyContainer"/>
            objects that
            are lower in the document's hierarchy. Most inherited properties are
            related to textual operations. Indicates whether or not this type of property is inheritable.
            </summary>
        </member>
        <member name="M:iText.Layout.Properties.Property.IsPropertyInherited(System.Int32)">
            <summary>
            This method checks whether a Property, in order to be picked up by the
            rendering engine, must be defined on the current element or renderer
            (<code>return false</code>), or may be defined in one of its parent
            elements or renderers (<code>return true</code>).
            </summary>
            <param name="property">the ID, defined in this class, of the property to check</param>
            <returns>whether the property type is inheritable</returns>
        </member>
        <member name="T:iText.Layout.Properties.Underline">
            <summary>A POJO that describes the underline of a layout element.</summary>
            <remarks>
            A POJO that describes the underline of a layout element.
            This class is to be used as a property for an element or renderer,
            as the value for
            <see cref="F:iText.Layout.Properties.Property.UNDERLINE"/>
            </remarks>
        </member>
        <member name="M:iText.Layout.Properties.Underline.#ctor(iText.Kernel.Colors.Color,System.Single,System.Single,System.Single,System.Single,System.Int32)">
            <summary>Creates an Underline.</summary>
            <remarks>
            Creates an Underline. Both the thickness and vertical positioning under
            the text element's base line can be set to a fixed value, or a variable
            one depending on the element's font size.
            If you want a fixed-width thickness, set <code>thicknessMul</code> to 0;
            if you want a thickness solely dependent on the font size, set
            <code>thickness</code> to 0.
            Mutatis mutandis for the y-position.
            </remarks>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.Color"/>
            of the underline
            </param>
            <param name="thickness">a float defining the minimum thickness in points of the underline</param>
            <param name="thicknessMul">a float defining the font size dependent component of the thickness of the underline
                </param>
            <param name="yPosition">a float defining the default absolute vertical distance in points from the text's base line
                </param>
            <param name="yPositionMul">a float defining the font size dependent component of the vertical positioning of the underline
                </param>
            <param name="lineCapStyle">
            the way the underline finishes at its edges.
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvasConstants.LineCapStyle"/>
            </param>
        </member>
        <member name="M:iText.Layout.Properties.Underline.#ctor(iText.Kernel.Colors.Color,System.Single,System.Single,System.Single,System.Single,System.Single,System.Int32)">
            <summary>Creates an Underline.</summary>
            <remarks>
            Creates an Underline. Both the thickness and vertical positioning under
            the text element's base line can be set to a fixed value, or a variable
            one depending on the element's font size.
            If you want a fixed-width thickness, set <code>thicknessMul</code> to 0;
            if you want a thickness solely dependent on the font size, set
            <code>thickness</code> to 0.
            Mutatis mutandis for the y-position.
            </remarks>
            <param name="color">
            the
            <see cref="T:iText.Kernel.Colors.Color"/>
            of the underline
            </param>
            <param name="opacity">a float defining the opacity of the underline; a float between 0 and 1, where 1 stands for fully opaque color and 0 - for fully transparent
                </param>
            <param name="thickness">a float defining the minimum thickness in points of the underline</param>
            <param name="thicknessMul">a float defining the font size dependent component of the thickness of the underline
                </param>
            <param name="yPosition">a float defining the default absolute vertical distance in points from the text's base line
                </param>
            <param name="yPositionMul">a float defining the font size dependent component of the vertical positioning of the underline
                </param>
            <param name="lineCapStyle">
            the way the underline finishes at its edges.
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvasConstants.LineCapStyle"/>
            </param>
        </member>
        <member name="M:iText.Layout.Properties.Underline.GetColor">
            <summary>Gets the color of the underline.</summary>
            <returns>
            a
            <see cref="T:iText.Kernel.Colors.Color"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Properties.Underline.GetOpacity">
            <summary>Gets the opacity of the underline color.</summary>
            <returns>a float between 0 and 1, where 1 stands for fully opaque color and 0 - for fully transparent</returns>
        </member>
        <member name="M:iText.Layout.Properties.Underline.GetThickness(System.Single)">
            <summary>Gets the total thickness of the underline (fixed + variable part).</summary>
            <param name="fontSize">the font size for which to calculate the variable thickness</param>
            <returns>the total thickness, as a <code>float</code>, in points</returns>
        </member>
        <member name="M:iText.Layout.Properties.Underline.GetYPosition(System.Single)">
            <summary>Gets the vertical position of the underline (fixed + variable part).</summary>
            <param name="fontSize">the font size for which to calculate the variable position</param>
            <returns>the y-position, as a <code>float</code>, in points</returns>
        </member>
        <member name="M:iText.Layout.Properties.Underline.GetYPositionMul">
            <summary>Gets the multiplier for the vertical positioning of the text underline.</summary>
            <returns>the Y-position multiplier, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Properties.Underline.GetLineCapStyle">
            <summary>
            Gets the
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvasConstants.LineCapStyle"/>
            of the text underline.
            </summary>
            <returns>
            the line cap style, as an <code>int</code> referring to
            the values of
            <see cref="T:iText.Kernel.Pdf.Canvas.PdfCanvasConstants.LineCapStyle"/>
            </returns>
        </member>
        <member name="T:iText.Layout.Properties.UnitValue">
            <summary>A specialized class that holds a value and the unit it is measured in.</summary>
        </member>
        <member name="M:iText.Layout.Properties.UnitValue.#ctor(System.Int32,System.Single)">
            <summary>Creates a UnitValue object with a specified type and value.</summary>
            <param name="unitType">
            either
            <see cref="F:iText.Layout.Properties.UnitValue.POINT"/>
            or a
            <see cref="F:iText.Layout.Properties.UnitValue.PERCENT"/>
            </param>
            <param name="value">the value to be stored.</param>
        </member>
        <member name="M:iText.Layout.Properties.UnitValue.#ctor(iText.Layout.Properties.UnitValue)">
            <summary>Creates a copy of UnitValue object.</summary>
        </member>
        <member name="M:iText.Layout.Properties.UnitValue.CreatePointValue(System.Single)">
            <summary>Creates a UnitValue POINT object with a specified value.</summary>
            <param name="value">the value to be stored.</param>
            <returns>
            a new
            <see cref="F:iText.Layout.Properties.UnitValue.POINT"/>
            
            <see cref="T:iText.Layout.Properties.UnitValue"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Properties.UnitValue.CreatePercentValue(System.Single)">
            <summary>Creates a UnitValue PERCENT object with a specified value.</summary>
            <param name="value">the value to be stored.</param>
            <returns>
            a new
            <see cref="F:iText.Layout.Properties.UnitValue.PERCENT"/>
            
            <see cref="T:iText.Layout.Properties.UnitValue"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Properties.UnitValue.CreatePercentArray(System.Single[])">
            <summary>Creates an array of UnitValue PERCENT objects with specified values.</summary>
            <param name="values">the values to be stored.</param>
            <returns>
            a new normalized (Σ=100%) array of
            <see cref="F:iText.Layout.Properties.UnitValue.PERCENT"/>
            
            <see cref="T:iText.Layout.Properties.UnitValue"/>
            .
            </returns>
        </member>
        <member name="M:iText.Layout.Properties.UnitValue.CreatePointArray(System.Single[])">
            <summary>Creates an array of UnitValue POINT objects with specified values.</summary>
            <param name="values">the values to be stored.</param>
            <returns>
            a new array of
            <see cref="F:iText.Layout.Properties.UnitValue.POINT"/>
            
            <see cref="T:iText.Layout.Properties.UnitValue"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Properties.UnitValue.GetUnitType">
            <summary>Returns the unit this value is stored in, either points (pt) or percent(%)</summary>
            <returns>
            either 1 for
            <see cref="F:iText.Layout.Properties.UnitValue.POINT"/>
            or 2 for
            <see cref="F:iText.Layout.Properties.UnitValue.PERCENT"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Properties.UnitValue.SetUnitType(System.Int32)">
            <summary>Sets the unit this value is stored in, either points (pt) or percent(%)</summary>
            <param name="unitType">
            either
            <see cref="F:iText.Layout.Properties.UnitValue.POINT"/>
            or
            <see cref="F:iText.Layout.Properties.UnitValue.PERCENT"/>
            </param>
        </member>
        <member name="M:iText.Layout.Properties.UnitValue.GetValue">
            <summary>Gets the measured value stored in this object</summary>
            <returns>the value, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Properties.UnitValue.SetValue(System.Single)">
            <summary>Sets the measured value stored in this object</summary>
            <param name="value">a <code>float</code></param>
        </member>
        <member name="M:iText.Layout.Properties.UnitValue.IsPointValue">
            <summary>Returns whether or not the value is stored in points (pt)</summary>
            <returns><code>true</code> if stored in points</returns>
        </member>
        <member name="M:iText.Layout.Properties.UnitValue.IsPercentValue">
            <summary>Returns whether or not the value is stored in percent (%)</summary>
            <returns><code>true</code> if stored in percent</returns>
        </member>
        <member name="T:iText.Layout.Renderer.AbstractRendererExtensions">
            <summary>
            Helper class for easier acces for float and int properties.
            </summary>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRendererExtensions.GetPropertyAsFloat(iText.Layout.Renderer.AbstractRenderer,System.Int32)">
            <summary>Returns a property with a certain key, as a floating point value.</summary>
            <param name="property">
            an
            <see cref="T:iText.Layout.Properties.Property">enum value</see>
            </param>
            <returns>
            a
            <see cref="!:float?"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRendererExtensions.GetPropertyAsFloat(iText.Layout.Renderer.AbstractRenderer,System.Int32,System.Nullable{System.Single})">
            <summary>Returns a property with a certain key, as a floating point value.</summary>
            <param name="property">
            an
            <see cref="T:iText.Layout.Properties.Property">enum value</see>
            </param>
            <param name="defaultValue">default value to be returned if property is not found</param>
            <returns>
            a
            <see cref="!:float?"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.AbstractRendererExtensions.GetPropertyAsInteger(iText.Layout.Renderer.AbstractRenderer,System.Int32)">
            <summary>Returns a property with a certain key, as an integer value.</summary>
            <param name="property">
            an
            <see cref="T:iText.Layout.Properties.Property">enum value</see>
            </param>
            <returns>
            a
            <see cref="!:int?"/>
            </returns>
        </member>
        <member name="T:iText.Layout.Renderer.AccessibleAttributesApplier">
            <summary>
            Writes standard structure attributes to the IAccessibleElement based on the layout element properties
            and renderer layout result.
            </summary>
        </member>
        <member name="M:iText.Layout.Renderer.AccessibleAttributesApplier.RemoveSameAttributesTypeIfPresent(iText.Kernel.Pdf.Tagutils.AccessibilityProperties,iText.Kernel.Pdf.PdfName)">
            <summary>The same layout element instance can be added several times to the document.</summary>
            <remarks>
            The same layout element instance can be added several times to the document.
            In that case it will already have attributes which belong to the previous positioning on the page, and because of
            that we want to remove those old irrelevant attributes.
            </remarks>
        </member>
        <member name="T:iText.Layout.Renderer.AreaBreakRenderer">
            <summary>
            Renderer object for the
            <see cref="T:iText.Layout.Element.AreaBreak"/>
            layout element. Will terminate the
            current content area and initialize a new one.
            </summary>
        </member>
        <member name="M:iText.Layout.Renderer.AreaBreakRenderer.#ctor(iText.Layout.Element.AreaBreak)">
            <summary>Creates an AreaBreakRenderer.</summary>
            <param name="areaBreak">
            the
            <see cref="T:iText.Layout.Element.AreaBreak"/>
            that will be rendered by this object
            </param>
        </member>
        <member name="M:iText.Layout.Renderer.BlockRenderer.CreateRotationTransformInsideOccupiedArea">
            <summary>
            This method creates
            <see cref="T:iText.Kernel.Geom.AffineTransform"/>
            instance that could be used
            to rotate content inside the occupied area. Be aware that it should be used only after
            layout rendering is finished and correct occupied area for the rotated element is calculated.
            </summary>
            <returns>
            
            <see cref="T:iText.Kernel.Geom.AffineTransform"/>
            that rotates the content and places it inside occupied area.
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.CanvasRenderer.#ctor(iText.Layout.Canvas)">
            <summary>Creates a CanvasRenderer from its corresponding layout object.</summary>
            <remarks>
            Creates a CanvasRenderer from its corresponding layout object.
            Sets
            <see cref="F:iText.Layout.Renderer.RootRenderer.immediateFlush"/>
            to true.
            </remarks>
            <param name="canvas">
            the
            <see cref="T:iText.Layout.Canvas"/>
            which this object should manage
            </param>
        </member>
        <member name="M:iText.Layout.Renderer.CanvasRenderer.#ctor(iText.Layout.Canvas,System.Boolean)">
            <summary>Creates a CanvasRenderer from its corresponding layout object.</summary>
            <remarks>
            Creates a CanvasRenderer from its corresponding layout object.
            Defines whether the content should be flushed immediately after addition
            <see cref="M:iText.Layout.Renderer.CanvasRenderer.AddChild(iText.Layout.Renderer.IRenderer)"/>
            or not
            </remarks>
            <param name="canvas">
            the
            <see cref="T:iText.Layout.Canvas"/>
            which this object should manage
            </param>
            <param name="immediateFlush">the value which stands for immediate flushing</param>
        </member>
        <member name="M:iText.Layout.Renderer.CanvasRenderer.FlushSingleRenderer(iText.Layout.Renderer.IRenderer)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.CanvasRenderer.UpdateCurrentArea(iText.Layout.Layout.LayoutResult)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.CanvasRenderer.GetNextRenderer">
            <summary>
            For
            <see cref="T:iText.Layout.Renderer.CanvasRenderer"/>
            , this has a meaning of the renderer that will be used for relayout.
            </summary>
            <returns>relayout renderer.</returns>
        </member>
        <member name="M:iText.Layout.Renderer.CellRenderer.#ctor(iText.Layout.Element.Cell)">
            <summary>Creates a CellRenderer from its corresponding layout object.</summary>
            <param name="modelElement">
            the
            <see cref="T:iText.Layout.Element.Cell"/>
            which this object should manage
            </param>
        </member>
        <member name="M:iText.Layout.Renderer.CellRenderer.GetModelElement">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.CellRenderer.CreateSplitRenderer(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.CellRenderer.CreateOverflowRenderer(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.CellRenderer.DrawBorder(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.CellRenderer.GetNextRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.CollapsedTableBorders.GetCollapsedBorder(iText.Layout.Borders.Border,iText.Layout.Borders.Border)">
            <summary>Returns the collapsed border.</summary>
            <remarks>
            Returns the collapsed border. We process collapse
            if the table border width is strictly greater than cell border width.
            </remarks>
            <param name="cellBorder">cell border</param>
            <param name="tableBorder">table border</param>
            <returns>the collapsed border</returns>
        </member>
        <member name="M:iText.Layout.Renderer.DivRenderer.#ctor(iText.Layout.Element.Div)">
            <summary>Creates a DivRenderer from its corresponding layout object.</summary>
            <param name="modelElement">
            the
            <see cref="T:iText.Layout.Element.Div"/>
            which this object should manage
            </param>
        </member>
        <member name="M:iText.Layout.Renderer.DivRenderer.GetNextRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.ILeafElementRenderer.GetAscent">
            <summary>
            Gets the maximum offset above the base line that this
            <see cref="T:iText.Layout.Renderer.ILeafElementRenderer"/>
            extends to.
            </summary>
            <returns>
            the upwards vertical offset of this
            <see cref="T:iText.Layout.Renderer.ILeafElementRenderer"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.ILeafElementRenderer.GetDescent">
            <summary>
            Gets the maximum offset below the base line that this
            <see cref="T:iText.Layout.Renderer.ILeafElementRenderer"/>
            extends to.
            </summary>
            <returns>
            the downwards vertical offset of this
            <see cref="T:iText.Layout.Renderer.ILeafElementRenderer"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.ImageRenderer.#ctor(iText.Layout.Element.Image)">
            <summary>Creates an ImageRenderer from its corresponding layout object.</summary>
            <param name="image">
            the
            <see cref="T:iText.Layout.Element.Image"/>
            which this object should manage
            </param>
        </member>
        <member name="M:iText.Layout.Renderer.LineRenderer.Length">
            <summary>Gets the total lengths of characters in this line.</summary>
            <remarks>
            Gets the total lengths of characters in this line. Other elements (images, tables) are not taken
            into account.
            </remarks>
        </member>
        <member name="M:iText.Layout.Renderer.LineRenderer.BaseCharactersCount">
            <summary>Returns the number of base characters, i.e.</summary>
            <remarks>Returns the number of base characters, i.e. non-mark characters</remarks>
        </member>
        <member name="M:iText.Layout.Renderer.LineRenderer.CalculateTab(iText.Layout.Renderer.IRenderer,System.Single,System.Single)">
            <summary>Calculates and sets encountered tab size.</summary>
            <remarks>
            Calculates and sets encountered tab size.
            Returns null, if processing is finished and layout can be performed for the tab renderer;
            otherwise, in case when the tab should be processed after the next element in the line, this method returns corresponding tab stop.
            </remarks>
        </member>
        <member name="M:iText.Layout.Renderer.LineRenderer.CalculateTab(iText.Kernel.Geom.Rectangle,System.Single,iText.Layout.Element.TabStop,System.Collections.Generic.IList{iText.Layout.Renderer.IRenderer},iText.Layout.Renderer.IRenderer)">
            <summary>Calculates and sets tab size with the account of the element that is next in the line after the tab.
                </summary>
            <remarks>
            Calculates and sets tab size with the account of the element that is next in the line after the tab.
            Returns resulting width of the tab.
            </remarks>
        </member>
        <member name="M:iText.Layout.Renderer.LineRenderer.TrimFirst">
            <summary>Trim first child text renderers.</summary>
            <returns>total number of trimmed glyphs.</returns>
        </member>
        <member name="M:iText.Layout.Renderer.LineRenderer.ApplyOtf">
            <summary>Apply OTF features and return the last(!) base direction of child renderer</summary>
            <returns>the last(!) base direction of child renderer.</returns>
        </member>
        <member name="M:iText.Layout.Renderer.LineRenderer.ResolveChildrenFonts">
            <summary>While resolving TextRenderer may split into several ones with different fonts.</summary>
        </member>
        <member name="M:iText.Layout.Renderer.LineSeparatorRenderer.#ctor(iText.Layout.Element.LineSeparator)">
            <summary>Creates a LineSeparatorRenderer from its corresponding layout object.</summary>
            <param name="lineSeparator">
            the
            <see cref="T:iText.Layout.Element.LineSeparator"/>
            which this object should manage
            </param>
        </member>
        <member name="M:iText.Layout.Renderer.LineSeparatorRenderer.Layout(iText.Layout.Layout.LayoutContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.LineSeparatorRenderer.GetNextRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.LineSeparatorRenderer.DrawChildren(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="T:iText.Layout.Renderer.TextRenderer">
            <summary>
            This class represents the
            <see cref="T:iText.Layout.Renderer.IRenderer">renderer</see>
            object for a
            <see cref="T:iText.Layout.Element.Text"/>
            object. It will draw the glyphs of the textual content on the
            <see cref="T:iText.Layout.Renderer.DrawContext"/>
            .
            </summary>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.#ctor(iText.Layout.Element.Text)">
            <summary>Creates a TextRenderer from its corresponding layout object.</summary>
            <param name="textElement">
            the
            <see cref="T:iText.Layout.Element.Text"/>
            which this object should manage
            </param>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.#ctor(iText.Layout.Element.Text,System.String)">
            <summary>
            Creates a TextRenderer from its corresponding layout object, with a custom
            text to replace the contents of the
            <see cref="T:iText.Layout.Element.Text"/>
            .
            </summary>
            <param name="textElement">
            the
            <see cref="T:iText.Layout.Element.Text"/>
            which this object should manage
            </param>
            <param name="text">the replacement text</param>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.TrimFirst">
            <summary>
            Trims any whitespace characters from the start of the
            <see cref="T:iText.IO.Font.Otf.GlyphLine"/>
            to be rendered.
            </summary>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.TrimLast">
            <summary>
            Trims any whitespace characters from the end of the rendered
            <see cref="T:iText.IO.Font.Otf.GlyphLine"/>
            .
            </summary>
            <returns>the amount of space in points which the text was trimmed by</returns>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.GetAscent">
            <summary>Gets the maximum offset above the base line that this Text extends to.</summary>
            <returns>
            the upwards vertical offset of this
            <see cref="T:iText.Layout.Element.Text"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.GetDescent">
            <summary>Gets the maximum offset below the base line that this Text extends to.</summary>
            <returns>
            the downwards vertical offset of this
            <see cref="T:iText.Layout.Element.Text"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.GetYLine">
            <summary>
            Gets the position on the canvas of the imaginary horizontal line upon which
            the
            <see cref="T:iText.Layout.Element.Text"/>
            's contents will be written.
            </summary>
            <returns>
            the y position of this text on the
            <see cref="T:iText.Layout.Renderer.DrawContext"/>
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.MoveYLineTo(System.Single)">
            <summary>Moves the vertical position to the parameter's value.</summary>
            <param name="y">the new vertical position of the Text</param>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.SetText(System.String)">
            <summary>
            Manually sets the contents of the Text's representation on the canvas,
            regardless of the Text's own contents.
            </summary>
            <param name="text">the replacement text</param>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.SetText(iText.IO.Font.Otf.GlyphLine,System.Int32,System.Int32)">
            <summary>
            Manually sets a GlyphLine to be rendered with a specific start and end
            point.
            </summary>
            <param name="text">
            a
            <see cref="T:iText.IO.Font.Otf.GlyphLine"/>
            </param>
            <param name="leftPos">the leftmost end of the GlyphLine</param>
            <param name="rightPos">the rightmost end of the GlyphLine</param>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.Length">
            <summary>The length of the whole text assigned to this renderer.</summary>
            <returns>the text length</returns>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.CharAt(System.Int32)">
            <summary>Gets char code at given position for the text belonging to this renderer.</summary>
            <param name="pos">the position in range [0; length())</param>
            <returns>Unicode char code</returns>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.LineLength">
            <summary>
            Returns the length of the
            <see cref="F:iText.Layout.Renderer.TextRenderer.line">line</see>
            which is the result of the layout call.
            </summary>
            <returns>the length of the line</returns>
        </member>
        <member name="M:iText.Layout.Renderer.TextRenderer.ResolveFonts(System.Collections.Generic.IList{iText.Layout.Renderer.IRenderer})">
            <summary>
            Resolve
            <see cref="F:iText.Layout.Properties.Property.FONT"/>
            string value.
            </summary>
            <param name="addTo">add all processed renderers to.</param>
            <returns>
            true, if new
            <see cref="T:iText.Layout.Renderer.TextRenderer"/>
            has been created.
            </returns>
        </member>
        <member name="M:iText.Layout.Renderer.LinkRenderer.#ctor(iText.Layout.Element.Link)">
            <summary>Creates a LinkRenderer from its corresponding layout object.</summary>
            <param name="link">
            the
            <see cref="T:iText.Layout.Element.Link"/>
            which this object should manage
            </param>
        </member>
        <member name="M:iText.Layout.Renderer.LinkRenderer.#ctor(iText.Layout.Element.Link,System.String)">
            <summary>
            Creates a LinkRenderer from its corresponding layout object, with a custom
            text to replace the contents of the
            <see cref="T:iText.Layout.Element.Link"/>
            .
            </summary>
            <param name="linkElement">
            the
            <see cref="T:iText.Layout.Element.Link"/>
            which this object should manage
            </param>
            <param name="text">the replacement text</param>
        </member>
        <member name="M:iText.Layout.Renderer.ListItemRenderer.#ctor(iText.Layout.Element.ListItem)">
            <summary>Creates a ListItemRenderer from its corresponding layout object.</summary>
            <param name="modelElement">
            the
            <see cref="T:iText.Layout.Element.ListItem"/>
            which this object should manage
            </param>
        </member>
        <member name="M:iText.Layout.Renderer.ListRenderer.#ctor(iText.Layout.Element.List)">
            <summary>Creates a ListRenderer from its corresponding layout object.</summary>
            <param name="modelElement">
            the
            <see cref="T:iText.Layout.Element.List"/>
            which this object should manage
            </param>
        </member>
        <member name="M:iText.Layout.Renderer.ListRenderer.CorrectListSplitting(iText.Layout.Renderer.IRenderer,iText.Layout.Renderer.IRenderer,iText.Layout.Renderer.IRenderer,iText.Layout.Layout.LayoutArea)">
            <summary>
            <p>
            Corrects split and overflow renderers when
            <see cref="F:iText.Layout.Properties.Property.FORCED_PLACEMENT"/>
            is applied.
            We assume that
            <see cref="F:iText.Layout.Properties.Property.FORCED_PLACEMENT"/>
            is applied when the first
            <see cref="T:iText.Layout.Renderer.ListItemRenderer"/>
            cannot be fully layouted.
            This means that the problem has occurred in one of first list item renderer's child.
            We consider the right solution to force placement of all first item renderer's childs before the one,
            which was the cause of
            <see cref="F:iText.Layout.Layout.LayoutResult.NOTHING"/>
            , including this child.
            </p>
            <p>
            Notice that we do not expect
            <see cref="F:iText.Layout.Properties.Property.FORCED_PLACEMENT"/>
            to be applied
            if we can render the first item renderer and strongly recommend not to set
            <see cref="F:iText.Layout.Properties.Property.FORCED_PLACEMENT"/>
            manually.
            </p>
            </summary>
            <param name="splitRenderer">
            the
            <see cref="T:iText.Layout.Renderer.IRenderer">split renderer</see>
            before correction
            </param>
            <param name="overflowRenderer">
            the
            <see cref="T:iText.Layout.Renderer.IRenderer">overflow renderer</see>
            before correction
            </param>
            <param name="causeOfNothing">
            the
            <see cref="!:com.itextpdf.layout.layout.LayoutResult#causeOfNothing">cause of nothing renderer</see>
            </param>
            <param name="occupiedArea">the area occupied by layouting before correction</param>
            <returns>
            corrected
            <see cref="T:iText.Layout.Layout.LayoutResult">layout result</see>
            </returns>
        </member>
        <member name="T:iText.Layout.Renderer.ParagraphRenderer">
            <summary>
            This class represents the
            <see cref="T:iText.Layout.Renderer.IRenderer">renderer</see>
            object for a
            <see cref="T:iText.Layout.Element.Paragraph"/>
            object. It will draw the glyphs of the textual content on the
            <see cref="T:iText.Layout.Renderer.DrawContext"/>
            .
            </summary>
        </member>
        <member name="M:iText.Layout.Renderer.ParagraphRenderer.#ctor(iText.Layout.Element.Paragraph)">
            <summary>Creates a ParagraphRenderer from its corresponding layout object.</summary>
            <param name="modelElement">
            the
            <see cref="T:iText.Layout.Element.Paragraph"/>
            which this object should manage
            </param>
        </member>
        <member name="M:iText.Layout.Renderer.ParagraphRenderer.Layout(iText.Layout.Layout.LayoutContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.ParagraphRenderer.GetNextRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.ParagraphRenderer.GetDefaultProperty``1(System.Int32)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.ParagraphRenderer.ToString">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.ParagraphRenderer.DrawChildren(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.ParagraphRenderer.Move(System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.ParagraphRenderer.GetLines">
            <summary>
            Gets the lines which are the result of the
            <see cref="M:iText.Layout.Renderer.ParagraphRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            .
            </summary>
            <returns>paragraph lines, or <code>null</code> if layout hasn't been called yet</returns>
        </member>
        <member name="M:iText.Layout.Renderer.RotationUtils.CountRotationMinMaxWidth(iText.Layout.Minmaxwidth.MinMaxWidth,iText.Layout.Renderer.AbstractRenderer)">
            <summary>
            This method tries to calculate min-max-width of rotated element using heuristics
            of
            <see cref="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.Calculate(System.Double,System.Double,iText.Layout.Minmaxwidth.MinMaxWidth)"/>
            .
            This method may call
            <see cref="M:iText.Layout.Renderer.IRenderer.Layout(iText.Layout.Layout.LayoutContext)"/>
            once in best case
            (if the width is set on element, or if we are really lucky) and three times in worst case.
            </summary>
            <param name="minMaxWidth">the minMaxWidth of NOT rotated renderer</param>
            <param name="renderer">the actual renderer</param>
            <returns>minMaxWidth of rotated renderer or original value in case rotated value can not be calculated, or renderer isn't rotated.
                </returns>
        </member>
        <member name="M:iText.Layout.Renderer.RotationUtils.RetrieveRotatedLayoutWidth(System.Single,iText.Layout.Renderer.AbstractRenderer)">
            <summary>This method tries to calculate width of not rotated renderer, so after rotation it fits availableWidth.
                </summary>
            <remarks>
            This method tries to calculate width of not rotated renderer, so after rotation it fits availableWidth.
            This method uses heuristics of
            <see cref="M:iText.Layout.Minmaxwidth.RotationMinMaxWidth.Calculate(System.Double,System.Double,iText.Layout.Minmaxwidth.MinMaxWidth,System.Double)"/>
            .
            It doesn't take into account any of height properties of renderer or height of layoutArea.
            The minMaxWidth calculations and initial layout may take long time, but they won't be called if the renderer have width property.
            </remarks>
            <param name="availableWidth">the width of layoutArea</param>
            <param name="renderer">the actual renderer</param>
            <returns>
            the width that should be set as width of layout area to properly layout element, or fallback to
            <see cref="M:iText.Layout.Renderer.AbstractRenderer.RetrieveWidth(System.Single)"/>
            in case it can not be calculated, or renderer isn't rotated.
            </returns>
        </member>
        <member name="T:iText.Layout.Renderer.TableRenderer">
            <summary>
            This class represents the
            <see cref="T:iText.Layout.Renderer.IRenderer">renderer</see>
            object for a
            <see cref="T:iText.Layout.Element.Table"/>
            object. It will delegate its drawing operations on to the
            <see cref="T:iText.Layout.Renderer.CellRenderer"/>
            instances associated with the
            <see cref="T:iText.Layout.Element.Cell">table cells</see>
            .
            </summary>
        </member>
        <member name="F:iText.Layout.Renderer.TableRenderer.isOriginalNonSplitRenderer">
            <summary>True for newly created renderer.</summary>
            <remarks>True for newly created renderer. For split renderers this is set to false. Used for tricky layout.
                </remarks>
        </member>
        <member name="M:iText.Layout.Renderer.TableRenderer.#ctor(iText.Layout.Element.Table,iText.Layout.Element.Table.RowRange)">
            <summary>
            Creates a TableRenderer from a
            <see cref="T:iText.Layout.Element.Table"/>
            which will partially render
            the table.
            </summary>
            <param name="modelElement">the table to be rendered by this renderer</param>
            <param name="rowRange">the table rows to be rendered</param>
        </member>
        <member name="M:iText.Layout.Renderer.TableRenderer.#ctor(iText.Layout.Element.Table)">
            <summary>
            Creates a TableRenderer from a
            <see cref="T:iText.Layout.Element.Table"/>
            .
            </summary>
            <param name="modelElement">the table to be rendered by this renderer</param>
        </member>
        <member name="M:iText.Layout.Renderer.TableRenderer.AddChild(iText.Layout.Renderer.IRenderer)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.TableRenderer.Layout(iText.Layout.Layout.LayoutContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.TableRenderer.Draw(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.TableRenderer.DrawChildren(iText.Layout.Renderer.DrawContext)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.TableRenderer.GetNextRenderer">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.TableRenderer.Move(System.Single,System.Single)">
            <summary><inheritDoc/></summary>
        </member>
        <member name="M:iText.Layout.Renderer.TableRenderer.AdjustFooterAndFixOccupiedArea(iText.Kernel.Geom.Rectangle)">
            <summary>If there is some space left, we move footer up, because initially footer will be at the very bottom of the area.
                </summary>
            <remarks>
            If there is some space left, we move footer up, because initially footer will be at the very bottom of the area.
            We also adjust occupied area by footer size if it is present.
            </remarks>
            <param name="layoutBox">the layout box which represents the area which is left free.</param>
        </member>
        <member name="M:iText.Layout.Renderer.TableRenderer.SetRowRange(iText.Layout.Element.Table.RowRange)">
            <summary>This method is used to set row range for table renderer during creating a new renderer.</summary>
            <remarks>
            This method is used to set row range for table renderer during creating a new renderer.
            The purpose to use this method is to remove input argument RowRange from createOverflowRenderer
            and createSplitRenderer methods.
            </remarks>
        </member>
        <member name="M:iText.Layout.Renderer.TableRenderer.CalculateColumnWidths(System.Single)">
            <summary>Returns minWidth</summary>
        </member>
        <member name="T:iText.Layout.Renderer.TableRenderer.CellRendererInfo">
            <summary>This are a structs used for convenience in layout.</summary>
        </member>
        <member name="T:iText.Layout.Renderer.TableRenderer.OverflowRowsWrapper">
            <summary>Utility class that copies overflow renderer rows on cell replacement so it won't affect original renderer
                </summary>
        </member>
        <member name="M:iText.Layout.Renderer.TabRenderer.#ctor(iText.Layout.Element.Tab)">
            <summary>Creates a TabRenderer from its corresponding layout object</summary>
            <param name="tab">
            the
            <see cref="T:iText.Layout.Element.Tab"/>
            which this object should manage
            </param>
        </member>
        <member name="M:iText.Layout.Renderer.TypographyUtils.FindMethod(System.String,System.String,System.Type[])">
            <exception cref="T:System.MissingMethodException"/>
            <exception cref="T:System.TypeLoadException"/>
        </member>
        <member name="M:iText.Layout.Renderer.TypographyUtils.FindConstructor(System.String,System.Type[])">
            <exception cref="T:System.MissingMethodException"/>
            <exception cref="T:System.TypeLoadException"/>
        </member>
        <member name="M:iText.Layout.Renderer.TypographyUtils.FindClass(System.String)">
            <exception cref="T:System.TypeLoadException"/>
        </member>
        <member name="T:iText.Layout.Splitting.DefaultSplitCharacters">
            <summary>
            The default implementation of
            <see cref="T:iText.Layout.Splitting.ISplitCharacters"/>
            interface.
            </summary>
        </member>
        <member name="T:iText.Layout.Splitting.ISplitCharacters">
            <summary>Interface for customizing the split character.</summary>
        </member>
        <member name="M:iText.Layout.Splitting.ISplitCharacters.IsSplitCharacter(iText.IO.Font.Otf.GlyphLine,System.Int32)">
            <summary>Returns <CODE>true</CODE> if the character can split a line.</summary>
            <remarks>
            Returns <CODE>true</CODE> if the character can split a line. The splitting implementation
            is free to look ahead or look behind characters to make a decision.
            </remarks>
            <param name="glyphPos">
            the position of
            <see cref="T:iText.IO.Font.Otf.Glyph"/>
            in the
            <see cref="T:iText.IO.Font.Otf.GlyphLine"/>
            </param>
            <param name="text">an array of unicode char codes which represent current text</param>
        </member>
        <member name="T:iText.Layout.Style">
            <summary>Container object for style properties of an element.</summary>
            <remarks>
            Container object for style properties of an element. A style can be used as
            an effective way to define multiple equal properties to several elements.
            Used in
            <see cref="T:iText.Layout.Element.AbstractElement`1"/>
            </remarks>
        </member>
        <member name="M:iText.Layout.Style.GetMarginLeft">
            <summary>Gets the current left margin width of the element.</summary>
            <returns>the left margin width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Style.SetMarginLeft(System.Single)">
            <summary>Sets the left margin width of the element.</summary>
            <param name="value">the new left margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.GetMarginRight">
            <summary>Gets the current right margin width of the element.</summary>
            <returns>the right margin width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Style.SetMarginRight(System.Single)">
            <summary>Sets the right margin width of the element.</summary>
            <param name="value">the new right margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.GetMarginTop">
            <summary>Gets the current top margin width of the element.</summary>
            <returns>the top margin width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Style.SetMarginTop(System.Single)">
            <summary>Sets the top margin width of the element.</summary>
            <param name="value">the new top margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.GetMarginBottom">
            <summary>Gets the current bottom margin width of the element.</summary>
            <returns>the bottom margin width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Style.SetMarginBottom(System.Single)">
            <summary>Sets the bottom margin width of the element.</summary>
            <param name="value">the new bottom margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.SetMargin(System.Single)">
            <summary>Sets all margins around the element to the same width.</summary>
            <param name="commonMargin">the new margin width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.SetMargins(System.Single,System.Single,System.Single,System.Single)">
            <summary>Sets the margins around the element to a series of new widths.</summary>
            <param name="marginTop">the new margin top width</param>
            <param name="marginRight">the new margin right width</param>
            <param name="marginBottom">the new margin bottom width</param>
            <param name="marginLeft">the new margin left width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.GetPaddingLeft">
            <summary>Gets the current left padding width of the element.</summary>
            <returns>the left padding width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Style.SetPaddingLeft(System.Single)">
            <summary>Sets the left padding width of the element.</summary>
            <param name="value">the new left padding width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.GetPaddingRight">
            <summary>Gets the current right padding width of the element.</summary>
            <returns>the right padding width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Style.SetPaddingRight(System.Single)">
            <summary>Sets the right padding width of the element.</summary>
            <param name="value">the new right padding width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.GetPaddingTop">
            <summary>Gets the current top padding width of the element.</summary>
            <returns>the top padding width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Style.SetPaddingTop(System.Single)">
            <summary>Sets the top padding width of the element.</summary>
            <param name="value">the new top padding width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.GetPaddingBottom">
            <summary>Gets the current bottom padding width of the element.</summary>
            <returns>the bottom padding width, as a <code>float</code></returns>
        </member>
        <member name="M:iText.Layout.Style.SetPaddingBottom(System.Single)">
            <summary>Sets the bottom padding width of the element.</summary>
            <param name="value">the new bottom padding width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.SetPadding(System.Single)">
            <summary>Sets all paddings around the element to the same width.</summary>
            <param name="commonPadding">the new padding width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.SetPaddings(System.Single,System.Single,System.Single,System.Single)">
            <summary>Sets the paddings around the element to a series of new widths.</summary>
            <param name="paddingTop">the new padding top width</param>
            <param name="paddingRight">the new padding right width</param>
            <param name="paddingBottom">the new padding bottom width</param>
            <param name="paddingLeft">the new padding left width</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.SetVerticalAlignment(System.Nullable{iText.Layout.Properties.VerticalAlignment})">
            <summary>Sets the vertical alignment of the element.</summary>
            <param name="verticalAlignment">the vertical alignment setting</param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.SetSpacingRatio(System.Single)">
            <summary>
            Sets a ratio which determines in which proportion will word spacing and character spacing
            be applied when horizontal alignment is justified.
            </summary>
            <param name="ratio">
            the ratio coefficient. It must be between 0 and 1, inclusive.
            It means that <b>ratio</b> part of the free space will
            be compensated by word spacing, and <b>1-ratio</b> part of the free space will
            be compensated by character spacing.
            If <b>ratio</b> is 1, additional character spacing will not be applied.
            If <b>ratio</b> is 0, additional word spacing will not be applied.
            </param>
        </member>
        <member name="M:iText.Layout.Style.IsKeepTogether">
            <summary>
            Returns whether the
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            should be kept together as much
            as possible.
            </summary>
            <returns>
            the current value of the
            <see cref="F:iText.Layout.Properties.Property.KEEP_TOGETHER"/>
            property
            </returns>
        </member>
        <member name="M:iText.Layout.Style.SetKeepTogether(System.Boolean)">
            <summary>
            Sets whether the
            <see cref="T:iText.Layout.Element.BlockElement`1"/>
            should be kept together as much
            as possible.
            </summary>
            <param name="keepTogether">
            the new value of the
            <see cref="F:iText.Layout.Properties.Property.KEEP_TOGETHER"/>
            property
            </param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.SetRotationAngle(System.Single)">
            <summary>Sets the rotation radAngle.</summary>
            <param name="radAngle">the new rotation radAngle, as a <code>float</code></param>
            <returns>this element</returns>
        </member>
        <member name="M:iText.Layout.Style.SetRotationAngle(System.Double)">
            <summary>Sets the rotation angle.</summary>
            <param name="angle">the new rotation angle, as a <code>double</code></param>
            <returns>this element</returns>
        </member>
    </members>
</doc>
