<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Nito.AsyncEx</name>
    </assembly>
    <members>
        <member name="T:Nito.AsyncEx.AsyncAutoResetEvent">
            <summary>
            An async-compatible auto-reset event.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncAutoResetEvent._queue">
            <summary>
            The queue of TCSs that other tasks are awaiting.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncAutoResetEvent._set">
            <summary>
            The current state of the event.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncAutoResetEvent._id">
            <summary>
            The semi-unique identifier for this instance. This is 0 if the id has not yet been created.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncAutoResetEvent._mutex">
            <summary>
            The object used for mutual exclusion.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncAutoResetEvent.#ctor(System.Bo<PERSON>an,Nito.AsyncEx.IAsyncWaitQueue{System.Object})">
            <summary>
            Creates an async-compatible auto-reset event.
            </summary>
            <param name="set">Whether the auto-reset event is initially set or unset.</param>
            <param name="queue">The wait queue used to manage waiters.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncAutoResetEvent.#ctor(System.Boolean)">
            <summary>
            Creates an async-compatible auto-reset event.
            </summary>
            <param name="set">Whether the auto-reset event is initially set or unset.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncAutoResetEvent.#ctor">
            <summary>
            Creates an async-compatible auto-reset event that is initially unset.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncAutoResetEvent.WaitAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously waits for this event to be set. If the event is set, this method will auto-reset it and return immediately, even if the cancellation token is already signalled. If the wait is canceled, then it will not auto-reset this event.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel this wait.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncAutoResetEvent.Wait(System.Threading.CancellationToken)">
            <summary>
            Synchronously waits for this event to be set. If the event is set, this method will auto-reset it and return immediately, even if the cancellation token is already signalled. If the wait is canceled, then it will not auto-reset this event. This method may block the calling thread.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel this wait.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncAutoResetEvent.WaitAsync">
            <summary>
            Asynchronously waits for this event to be set. If the event is set, this method will auto-reset it and return immediately.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncAutoResetEvent.Wait">
            <summary>
            Synchronously waits for this event to be set. If the event is set, this method will auto-reset it and return immediately. This method may block the calling thread.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncAutoResetEvent.Set">
            <summary>
            Sets the event, atomically completing a task returned by <see cref="o:WaitAsync"/>. If the event is already set, this method does nothing.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncAutoResetEvent.Id">
            <summary>
            Gets a semi-unique identifier for this asynchronous auto-reset event.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncBarrier">
            <summary>
            An async-compatible barrier.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncBarrier._sync">
            <summary>
            Mutex used to control access to other fields.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncBarrier._tcs">
            <summary>
            The TCS used to signal the current phase.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncBarrier._phase">
            <summary>
            The current phase.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncBarrier._count">
            <summary>
            The remaining count on this event.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncBarrier._participants">
            <summary>
            The total number of participants.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncBarrier._id">
            <summary>
            The semi-unique identifier for this instance. This is 0 if the id has not yet been created.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncBarrier._asyncPostPhaseAction">
            <summary>
            The asynchronous post-phase action, if any. Either this member or <see cref="F:Nito.AsyncEx.AsyncBarrier._syncPostPhaseAction"/> may be non-<c>null</c>, but not both.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncBarrier._syncPostPhaseAction">
            <summary>
            The synchonous post-phase action, if any. Either this member or <see cref="F:Nito.AsyncEx.AsyncBarrier._asyncPostPhaseAction"/> may be non-<c>null</c>, but not both.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.#ctor(System.Int32)">
            <summary>
            Creates an async-compatible barrier.
            </summary>
            <param name="participants">The number of participants.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.#ctor(System.Int32,System.Action{Nito.AsyncEx.AsyncBarrier})">
            <summary>
            Creates an async-compatible barrier.
            </summary>
            <param name="participants">The number of participants.</param>
            <param name="postPhaseAction">The post-phase action to execute at the end of every phase.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.#ctor(System.Int32,System.Func{Nito.AsyncEx.AsyncBarrier,System.Threading.Tasks.Task})">
            <summary>
            Creates an async-compatible barrier.
            </summary>
            <param name="participants">The number of participants.</param>
            <param name="postPhaseAction">The post-phase action to execute at the end of every phase.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.RunPostPhaseActionAsync">
            <summary>
            Starts executing the post-phase action and returns a <see cref="T:System.Threading.Tasks.Task"/> representing the action.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.SignalAsync(System.Int32,System.Boolean)">
            <summary>
            Signals completions to this barrier. Returns the task for the current phase, which may already be completed. Returns <c>null</c> if the signal count is greater than the remaining participant count.
            </summary>
            <param name="signalCount">The number of completions to signal.</param>
            <param name="removeParticipants">Whether the participants should be removed permanently.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.SignalAndWaitAsync(System.Int32)">
            <summary>
            Signals the specified number of completions to this barrier and asynchronously waits for the phase to complete. This method may not be called during the post-phase action.
            </summary>
            <param name="count">The number of completion signals to send to this barrier.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.SignalAndWait(System.Int32)">
            <summary>
            Signals the specified number of completions to this barrier and synchronously waits for the phase to complete. This method may not be called during the post-phase action. This method may block the calling thread.
            </summary>
            <param name="count">The number of completion signals to send to this barrier.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.SignalAndWait(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Signals the specified number of completions to this barrier and synchronously waits for the phase to complete. This method may not be called during the post-phase action. This method may block the calling thread.
            </summary>
            <param name="count">The number of completion signals to send to this barrier.</param>
            <param name="cancellationToken">The cancellation token used to cancel the wait. If this signal completes the phase and there is no post-phase action, then this token is ignored.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.SignalAndWaitAsync">
            <summary>
            Signals a completion to this barrier and asynchronously waits for the phase to complete. This method may not be called during the post-phase action.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.SignalAndWait">
            <summary>
            Signals a completion to this barrier and asynchronously waits for the phase to complete. This method may not be called during the post-phase action. This method may block the calling thread.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.SignalAndWait(System.Threading.CancellationToken)">
            <summary>
            Signals a completion to this barrier and asynchronously waits for the phase to complete. This method may not be called during the post-phase action. This method may block the calling thread.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.AddParticipants(System.Int32)">
            <summary>
            Adds the specified number of participants to the barrier. Returns the current phase. This method may not be called during the post-phase action.
            </summary>
            <param name="count">The number of participants to add.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.AddParticipants">
            <summary>
            Adds a participant to the barrier. Returns the current phase. This method may not be called during the post-phase action.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.RemoveParticipantsAsync(System.Int32)">
            <summary>
            Removes the specified number of participants from the barrier. These participants must not have signalled the barrier for this phase yet. This method may not be called during the post-phase action.
            </summary>
            <param name="count">The number of participants to remove.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.RemoveParticipantsAndWaitAsync(System.Int32)">
            <summary>
            Removes the specified number of participants from the barrier and asynchronously waits for the phase to complete. These participants must not have signalled the barrier for this phase yet. This method may not be called during the post-phase action.
            </summary>
            <param name="count">The number of participants to remove.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.RemoveParticipantsAndWait(System.Int32)">
            <summary>
            Removes the specified number of participants from the barrier and synchronously waits for the phase to complete. These participants must not have signalled the barrier for this phase yet. This method may not be called during the post-phase action. This method may block the calling thread.
            </summary>
            <param name="count">The number of participants to remove.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.RemoveParticipantsAndWait(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Removes the specified number of participants from the barrier and synchronously waits for the phase to complete. These participants must not have signalled the barrier for this phase yet. This method may not be called during the post-phase action. This method may block the calling thread.
            </summary>
            <param name="count">The number of participants to remove.</param>
            <param name="cancellationToken">The cancellation token used to cancel the wait. If this removal completes the phase and there is no post-phase action, then this token is ignored.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.RemoveParticipants(System.Int32)">
            <summary>
            Removes the specified number of participants from the barrier. These participants must not have signalled the barrier for this phase yet. This method may not be called during the post-phase action.
            </summary>
            <param name="count">The number of participants to remove.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.RemoveParticipantsAsync">
            <summary>
            Removes one participant from the barrier. This participant must not have signalled the barrier for this phase yet. This method may not be called during the post-phase action.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.RemoveParticipantsAndWaitAsync">
            <summary>
            Removes one participant from the barrier and asynchronously waits for the phase to complete. This participant must not have signalled the barrier for this phase yet. This method may not be called during the post-phase action.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.RemoveParticipantsAndWait">
            <summary>
            Removes one participant from the barrier and synchronously waits for the phase to complete. This participant must not have signalled the barrier for this phase yet. This method may not be called during the post-phase action. This method may block the calling thread.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.RemoveParticipantsAndWait(System.Threading.CancellationToken)">
            <summary>
            Removes one participant from the barrier and synchronously waits for the phase to complete. This participant must not have signalled the barrier for this phase yet. This method may not be called during the post-phase action. This method may block the calling thread.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the wait. If this removal completes the phase and there is no post-phase action, then this token is ignored.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncBarrier.RemoveParticipants">
            <summary>
            Removes one participant from the barrier. This participant must not have signalled the barrier for this phase yet. This method may not be called during the post-phase action.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncBarrier.Id">
            <summary>
            Gets a semi-unique identifier for this asynchronous barrier.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncBarrier.CurrentPhaseNumber">
            <summary>
            Gets the current phase of the barrier.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncBarrier.ParticipantCount">
            <summary>
            Gets the number of participants in this barrier.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncBarrier.ParticipantsRemaining">
            <summary>
            Gets the number of participants for this phase that have not yet signalled.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncConditionVariable">
            <summary>
            An async-compatible condition variable. This type uses Mesa-style semantics (the notifying tasks do not yield).
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncConditionVariable._asyncLock">
            <summary>
            The lock associated with this condition variable.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncConditionVariable._queue">
            <summary>
            The queue of waiting tasks.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncConditionVariable._id">
            <summary>
            The semi-unique identifier for this instance. This is 0 if the id has not yet been created.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncConditionVariable._mutex">
            <summary>
            The object used for mutual exclusion.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncConditionVariable.#ctor(Nito.AsyncEx.AsyncLock,Nito.AsyncEx.IAsyncWaitQueue{System.Object})">
            <summary>
            Creates an async-compatible condition variable associated with an async-compatible lock.
            </summary>
            <param name="asyncLock">The lock associated with this condition variable.</param>
            <param name="queue">The wait queue used to manage waiters.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncConditionVariable.#ctor(Nito.AsyncEx.AsyncLock)">
            <summary>
            Creates an async-compatible condition variable associated with an async-compatible lock.
            </summary>
            <param name="asyncLock">The lock associated with this condition variable.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncConditionVariable.Notify">
            <summary>
            Sends a signal to a single task waiting on this condition variable. The associated lock MUST be held when calling this method, and it will still be held when this method returns.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncConditionVariable.NotifyAll">
            <summary>
            Sends a signal to all tasks waiting on this condition variable. The associated lock MUST be held when calling this method, and it will still be held when this method returns.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncConditionVariable.WaitAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously waits for a signal on this condition variable. The associated lock MUST be held when calling this method, and it will still be held when this method returns, even if the method is cancelled.
            </summary>
            <param name="cancellationToken">The cancellation signal used to cancel this wait.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncConditionVariable.Wait(System.Threading.CancellationToken)">
            <summary>
            Synchronously waits for a signal on this condition variable. This method may block the calling thread. The associated lock MUST be held when calling this method, and it will still be held when this method returns, even if the method is cancelled.
            </summary>
            <param name="cancellationToken">The cancellation signal used to cancel this wait.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncConditionVariable.WaitAsync">
            <summary>
            Asynchronously waits for a signal on this condition variable. The associated lock MUST be held when calling this method, and it will still be held when this method returns.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncConditionVariable.Wait">
            <summary>
            Synchronously waits for a signal on this condition variable. This method may block the calling thread. The associated lock MUST be held when calling this method, and it will still be held when this method returns.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncConditionVariable.Id">
            <summary>
            Gets a semi-unique identifier for this asynchronous condition variable.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncContext">
            <summary>
            Provides a context for asynchronous operations. This class is threadsafe.
            </summary>
            <remarks>
            <para><see cref="M:Nito.AsyncEx.AsyncContext.Execute"/> may only be called once. After <see cref="M:Nito.AsyncEx.AsyncContext.Execute"/> returns, the async context should be disposed.</para>
            </remarks>
        </member>
        <member name="F:Nito.AsyncEx.AsyncContext._queue">
            <summary>
            The queue holding the actions to run.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncContext._synchronizationContext">
            <summary>
            The <see cref="P:Nito.AsyncEx.AsyncContext.SynchronizationContext"/> for this <see cref="T:Nito.AsyncEx.AsyncContext"/>.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncContext._taskScheduler">
            <summary>
            The <see cref="T:System.Threading.Tasks.TaskScheduler"/> for this <see cref="T:Nito.AsyncEx.AsyncContext"/>.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncContext._taskFactory">
            <summary>
            The <see cref="T:System.Threading.Tasks.TaskFactory"/> for this <see cref="T:Nito.AsyncEx.AsyncContext"/>.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncContext._outstandingOperations">
            <summary>
            The number of outstanding operations, including actions in the queue.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.AsyncContext"/> class. This is an advanced operation; most people should use one of the static <c>Run</c> methods instead.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.OperationStarted">
            <summary>
            Increments the outstanding asynchronous operation count.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.OperationCompleted">
            <summary>
            Decrements the outstanding asynchronous operation count.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.Enqueue(System.Threading.Tasks.Task,System.Boolean)">
            <summary>
            Queues a task for execution by <see cref="M:Nito.AsyncEx.AsyncContext.Execute"/>. If all tasks have been completed and the outstanding asynchronous operation count is zero, then this method has undefined behavior.
            </summary>
            <param name="task">The task to queue. May not be <c>null</c>.</param>
            <param name="propagateExceptions">A value indicating whether exceptions on this task should be propagated out of the main loop.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.Dispose">
            <summary>
            Disposes all resources used by this class. This method should NOT be called while <see cref="M:Nito.AsyncEx.AsyncContext.Execute"/> is executing.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.Execute">
            <summary>
            Executes all queued actions. This method returns when all tasks have been completed and the outstanding asynchronous operation count is zero. This method will unwrap and propagate errors from tasks that are supposed to propagate errors.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.Run(System.Action)">
            <summary>
            Queues a task for execution, and begins executing all tasks in the queue. This method returns when all tasks have been completed and the outstanding asynchronous operation count is zero. This method will unwrap and propagate errors from the task.
            </summary>
            <param name="action">The action to execute. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.Run``1(System.Func{``0})">
            <summary>
            Queues a task for execution, and begins executing all tasks in the queue. This method returns when all tasks have been completed and the outstanding asynchronous operation count is zero. Returns the result of the task. This method will unwrap and propagate errors from the task.
            </summary>
            <typeparam name="TResult">The result type of the task.</typeparam>
            <param name="action">The action to execute. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.Run(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Queues a task for execution, and begins executing all tasks in the queue. This method returns when all tasks have been completed and the outstanding asynchronous operation count is zero. This method will unwrap and propagate errors from the task proxy.
            </summary>
            <param name="action">The action to execute. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.Run``1(System.Func{System.Threading.Tasks.Task{``0}})">
            <summary>
            Queues a task for execution, and begins executing all tasks in the queue. This method returns when all tasks have been completed and the outstanding asynchronous operation count is zero. Returns the result of the task proxy. This method will unwrap and propagate errors from the task proxy.
            </summary>
            <typeparam name="TResult">The result type of the task.</typeparam>
            <param name="action">The action to execute. May not be <c>null</c>.</param>
        </member>
        <member name="P:Nito.AsyncEx.AsyncContext.Id">
            <summary>
            Gets a semi-unique identifier for this asynchronous context. This is the same identifier as the context's <see cref="T:System.Threading.Tasks.TaskScheduler"/>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncContext.Current">
            <summary>
            Gets the current <see cref="T:Nito.AsyncEx.AsyncContext"/> for this thread, or <c>null</c> if this thread is not currently running in an <see cref="T:Nito.AsyncEx.AsyncContext"/>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncContext.SynchronizationContext">
            <summary>
            Gets the <see cref="P:Nito.AsyncEx.AsyncContext.SynchronizationContext"/> for this <see cref="T:Nito.AsyncEx.AsyncContext"/>. From inside <see cref="M:Nito.AsyncEx.AsyncContext.Execute"/>, this value is always equal to <see cref="P:System.Threading.SynchronizationContext.Current"/>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncContext.Scheduler">
            <summary>
            Gets the <see cref="T:System.Threading.Tasks.TaskScheduler"/> for this <see cref="T:Nito.AsyncEx.AsyncContext"/>. From inside <see cref="M:Nito.AsyncEx.AsyncContext.Execute"/>, this value is always equal to <see cref="P:System.Threading.Tasks.TaskScheduler.Current"/>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncContext.Factory">
            <summary>
            Gets the <see cref="T:System.Threading.Tasks.TaskFactory"/> for this <see cref="T:Nito.AsyncEx.AsyncContext"/>. Be careful with async delegates; you may need to call <see cref="M:System.Threading.SynchronizationContext.OperationStarted"/> and <see cref="M:System.Threading.SynchronizationContext.OperationCompleted"/> to prevent early termination of this <see cref="T:Nito.AsyncEx.AsyncContext"/>.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncContext.AsyncContextSynchronizationContext">
            <summary>
            The <see cref="P:Nito.AsyncEx.AsyncContext.SynchronizationContext"/> implementation used by <see cref="T:Nito.AsyncEx.AsyncContext"/>.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncContext.AsyncContextSynchronizationContext._context">
            <summary>
            The async context.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.AsyncContextSynchronizationContext.#ctor(Nito.AsyncEx.AsyncContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.AsyncContext.AsyncContextSynchronizationContext"/> class.
            </summary>
            <param name="context">The async context.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.AsyncContextSynchronizationContext.Post(System.Threading.SendOrPostCallback,System.Object)">
            <summary>
            Dispatches an asynchronous message to the async context. If all tasks have been completed and the outstanding asynchronous operation count is zero, then this method has undefined behavior.
            </summary>
            <param name="d">The <see cref="T:System.Threading.SendOrPostCallback"/> delegate to call. May not be <c>null</c>.</param>
            <param name="state">The object passed to the delegate.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.AsyncContextSynchronizationContext.Send(System.Threading.SendOrPostCallback,System.Object)">
            <summary>
            Dispatches an asynchronous message to the async context, and waits for it to complete.
            </summary>
            <param name="d">The <see cref="T:System.Threading.SendOrPostCallback"/> delegate to call. May not be <c>null</c>.</param>
            <param name="state">The object passed to the delegate.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.AsyncContextSynchronizationContext.OperationStarted">
            <summary>
            Responds to the notification that an operation has started by incrementing the outstanding asynchronous operation count.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.AsyncContextSynchronizationContext.OperationCompleted">
            <summary>
            Responds to the notification that an operation has completed by decrementing the outstanding asynchronous operation count.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.AsyncContextSynchronizationContext.CreateCopy">
            <summary>
            Creates a copy of the synchronization context.
            </summary>
            <returns>A new <see cref="T:System.Threading.SynchronizationContext"/> object.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.AsyncContextSynchronizationContext.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.AsyncContextSynchronizationContext.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance. It is considered equal if it refers to the same underlying async context as this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns><c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.</returns>
        </member>
        <member name="P:Nito.AsyncEx.AsyncContext.AsyncContextSynchronizationContext.Context">
            <summary>
            Gets the async context.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncContext.TaskQueue">
            <summary>
            A blocking queue.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncContext.TaskQueue._queue">
            <summary>
            The underlying blocking collection.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.TaskQueue.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.AsyncContext.TaskQueue"/> class.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.TaskQueue.GetConsumingEnumerable">
            <summary>
            Gets a blocking enumerable that removes items from the queue. This enumerable only completes after <see cref="M:Nito.AsyncEx.AsyncContext.TaskQueue.CompleteAdding"/> has been called.
            </summary>
            <returns>A blocking enumerable that removes items from the queue.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.TaskQueue.GetScheduledTasks">
            <summary>
            Generates an enumerable of <see cref="T:System.Threading.Tasks.Task"/> instances currently queued to the scheduler waiting to be executed.
            </summary>
            <returns>An enumerable that allows traversal of tasks currently queued to this scheduler.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.TaskQueue.TryAdd(System.Threading.Tasks.Task,System.Boolean)">
            <summary>
            Attempts to add the item to the queue. If the queue has been marked as complete for adding, this method returns <c>false</c>.
            </summary>
            <param name="item">The item to enqueue.</param>
            <param name="propagateExceptions">A value indicating whether exceptions on this task should be propagated out of the main loop.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.TaskQueue.CompleteAdding">
            <summary>
            Marks the queue as complete for adding, allowing the enumerator returned from <see cref="M:Nito.AsyncEx.AsyncContext.TaskQueue.GetConsumingEnumerable"/> to eventually complete. This method may be called several times.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.TaskQueue.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncContext.AsyncContextTaskScheduler">
            <summary>
            A task scheduler which schedules tasks to an async context.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncContext.AsyncContextTaskScheduler._context">
            <summary>
            The async context for this task scheduler.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.AsyncContextTaskScheduler.#ctor(Nito.AsyncEx.AsyncContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.AsyncContext.AsyncContextTaskScheduler"/> class.
            </summary>
            <param name="context">The async context for this task scheduler. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.AsyncContextTaskScheduler.GetScheduledTasks">
            <summary>
            Generates an enumerable of <see cref="T:System.Threading.Tasks.Task"/> instances currently queued to the scheduler waiting to be executed.
            </summary>
            <returns>An enumerable that allows traversal of tasks currently queued to this scheduler.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.AsyncContextTaskScheduler.QueueTask(System.Threading.Tasks.Task)">
            <summary>
            Queues a <see cref="T:System.Threading.Tasks.Task"/> to the scheduler. If all tasks have been completed and the outstanding asynchronous operation count is zero, then this method has undefined behavior.
            </summary>
            <param name="task">The <see cref="T:System.Threading.Tasks.Task"/> to be queued.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.AsyncContextTaskScheduler.TryExecuteTaskInline(System.Threading.Tasks.Task,System.Boolean)">
            <summary>
            Determines whether the provided <see cref="T:System.Threading.Tasks.Task"/> can be executed synchronously in this call, and if it can, executes it.
            </summary>
            <param name="task">The <see cref="T:System.Threading.Tasks.Task"/> to be executed.</param>
            <param name="taskWasPreviouslyQueued">A Boolean denoting whether or not task has previously been queued. If this parameter is True, then the task may have been previously queued (scheduled); if False, then the task is known not to have been queued, and this call is being made in order to execute the task inline without queuing it.</param>
            <returns>A Boolean value indicating whether the task was executed inline.</returns>
            <exception cref="T:System.InvalidOperationException">The <paramref name="task"/> was already executed.</exception>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContext.AsyncContextTaskScheduler.DoTryExecuteTask(System.Threading.Tasks.Task)">
            <summary>
            Exposes the base <see cref="M:System.Threading.Tasks.TaskScheduler.TryExecuteTask(System.Threading.Tasks.Task)"/> method.
            </summary>
            <param name="task">The task to attempt to execute.</param>
        </member>
        <member name="P:Nito.AsyncEx.AsyncContext.AsyncContextTaskScheduler.MaximumConcurrencyLevel">
            <summary>
            Indicates the maximum concurrency level this <see cref="T:System.Threading.Tasks.TaskScheduler"/> is able to support.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncContextThread">
            <summary>
            A thread that executes actions within an <see cref="T:Nito.AsyncEx.AsyncContext"/>.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncContextThread._thread">
            <summary>
            The child thread.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncContextThread._context">
            <summary>
            The asynchronous context executed by the child thread.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncContextThread._stoppingFlag">
            <summary>
            A flag used to ensure we only call <see cref="M:Nito.AsyncEx.AsyncContext.OperationCompleted"/> once during complex join/dispose operations.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContextThread.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.AsyncContextThread"/> class, creating a child thread waiting for commands.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContextThread.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.AsyncContextThread"/> class, creating a child thread waiting for commands. If <paramref name="sta"/> is <c>true</c>, then the child thread is an STA thread (throwing <see cref="T:System.NotSupportedException"/> if the platform does not support STA threads).
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContextThread.AllowThreadToExit">
            <summary>
            Permits the thread to exit, if we have not already done so.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContextThread.JoinAsync">
            <summary>
            Requests the thread to exit and returns a task representing the exit of the thread. The thread will exit when all outstanding asynchronous operations complete.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncContextThread.Dispose">
            <summary>
            Requests the thread to exit.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncContextThread.Context">
            <summary>
            Gets the <see cref="T:Nito.AsyncEx.AsyncContext"/> executed by this thread.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncContextThread.Factory">
            <summary>
            Gets the <see cref="T:System.Threading.Tasks.TaskFactory"/> for this thread, which can be used to schedule work to this thread.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncCountdownEvent">
            <summary>
            An async-compatible countdown event.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncCountdownEvent._tcs">
            <summary>
            The TCS used to signal this event.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncCountdownEvent._count">
            <summary>
            The remaining count on this event.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncCountdownEvent.#ctor(System.Int32)">
            <summary>
            Creates an async-compatible countdown event.
            </summary>
            <param name="count">The number of signals this event will need before it becomes set. Must be greater than zero.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncCountdownEvent.WaitAsync">
            <summary>
            Asynchronously waits for this event to be set.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncCountdownEvent.Wait">
            <summary>
            Synchronously waits for this event to be set. This method may block the calling thread.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncCountdownEvent.Wait(System.Threading.CancellationToken)">
            <summary>
            Synchronously waits for this event to be set. This method may block the calling thread.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the wait. If this token is already canceled, this method will first check whether the event is set.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncCountdownEvent.ModifyCount(System.Int32)">
            <summary>
            Attempts to modify the current count by the specified amount. This method returns <c>false</c> if the new current count value would be invalid, or if the count has already reached zero.
            </summary>
            <param name="signalCount">The amount to change the current count. This may be positive or negative, but not zero.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncCountdownEvent.TryAddCount(System.Int32)">
            <summary>
            Attempts to add the specified value to the current count. This method returns <c>false</c> if the count is already at zero or if the new count would be greater than <see cref="F:System.Int32.MaxValue"/>.
            </summary>
            <param name="signalCount">The amount to change the current count. This must be greater than zero.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncCountdownEvent.TryAddCount">
            <summary>
            Attempts to add one to the current count. This method returns <c>false</c> if the count is already at zero or if the new count would be greater than <see cref="F:System.Int32.MaxValue"/>.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncCountdownEvent.TrySignal(System.Int32)">
            <summary>
            Attempts to subtract the specified value from the current count. This method returns <c>false</c> if the count is already at zero or if the new count would be less than zero.
            </summary>
            <param name="signalCount">The amount to change the current count. This must be greater than zero.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncCountdownEvent.TrySignal">
            <summary>
            Attempts to subtract one from the current count. This method returns <c>false</c> if the count is already at zero or if the new count would be less than zero.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncCountdownEvent.AddCount(System.Int32)">
            <summary>
            Attempts to add the specified value to the current count. This method throws <see cref="T:System.InvalidOperationException"/> if the count is already at zero or if the new count would be greater than <see cref="F:System.Int32.MaxValue"/>.
            </summary>
            <param name="signalCount">The amount to change the current count. This must be greater than zero.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncCountdownEvent.AddCount">
            <summary>
            Attempts to add one to the current count. This method throws <see cref="T:System.InvalidOperationException"/> if the count is already at zero or if the new count would be greater than <see cref="F:System.Int32.MaxValue"/>.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncCountdownEvent.Signal(System.Int32)">
            <summary>
            Attempts to subtract the specified value from the current count. This method throws <see cref="T:System.InvalidOperationException"/> if the count is already at zero or if the new count would be less than zero.
            </summary>
            <param name="signalCount">The amount to change the current count. This must be greater than zero.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncCountdownEvent.Signal">
            <summary>
            Attempts to subtract one from the current count. This method throws <see cref="T:System.InvalidOperationException"/> if the count is already at zero or if the new count would be less than zero.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncCountdownEvent.Id">
            <summary>
            Gets a semi-unique identifier for this asynchronous countdown event.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncCountdownEvent.CurrentCount">
            <summary>
            Gets the current number of remaining signals before this event becomes set.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncFactory`1">
            <summary>
            Provides asynchronous wrappers.
            </summary>
            <typeparam name="TResult">The type of the result of the asychronous operation.</typeparam>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0})">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <param name="beginMethod">The begin method. May not be <c>null</c>.</param>
            <param name="endMethod">The end method. May not be <c>null</c>.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.ToBegin(System.Threading.Tasks.Task{`0},System.AsyncCallback,System.Object)">
            <summary>
            Wraps a <see cref="T:System.Threading.Tasks.Task`1"/> into the Begin method of an APM pattern.
            </summary>
            <param name="task">The task to wrap. May not be <c>null</c>.</param>
            <param name="callback">The callback method passed into the Begin method of the APM pattern.</param>
            <param name="state">The state passed into the Begin method of the APM pattern.</param>
            <returns>The asynchronous operation, to be returned by the Begin method of the APM pattern.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.ToEnd(System.IAsyncResult)">
            <summary>
            Wraps a <see cref="T:System.Threading.Tasks.Task`1"/> into the End method of an APM pattern.
            </summary>
            <param name="asyncResult">The asynchronous operation returned by the matching Begin method of this APM pattern.</param>
            <returns>The result of the asynchronous operation, to be returned by the End method of the APM pattern.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromEvent(System.Object)">
            <summary>
            Gets a task that will complete the next time an event is raised. The event type must follow the standard <c>void EventHandlerType(object, TResult)</c> pattern. Be mindful of race conditions (i.e., if the event is raised immediately before this method is called, your task may never complete).
            </summary>
            <param name="target">The object that publishes the event.</param>
            <returns>The event args.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromEvent(System.Object,System.String)">
            <summary>
            Gets a task that will complete the next time an event is raised. The event type must follow the standard <c>void EventHandlerType(object, TResult)</c> pattern. Be mindful of race conditions (i.e., if the event is raised immediately before this method is called, your task may never complete).
            </summary>
            <param name="target">The object that publishes the event. May not be <c>null</c>.</param>
            <param name="eventName">The name of the event. May not be <c>null</c>.</param>
            <returns>The event args.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``4(System.Func{``0,``1,``2,``3,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,``3)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``5(System.Func{``0,``1,``2,``3,``4,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,``3,``4)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``6(System.Func{``0,``1,``2,``3,``4,``5,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,``3,``4,``5)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``7(System.Func{``0,``1,``2,``3,``4,``5,``6,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,``3,``4,``5,``6)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``8(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``9(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <typeparam name="TArg8">The type of argument 8.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <param name="arg8">Argument 8.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``10(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <typeparam name="TArg8">The type of argument 8.</typeparam>
            <typeparam name="TArg9">The type of argument 9.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <param name="arg8">Argument 8.</param>
            <param name="arg9">Argument 9.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``11(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <typeparam name="TArg8">The type of argument 8.</typeparam>
            <typeparam name="TArg9">The type of argument 9.</typeparam>
            <typeparam name="TArg10">The type of argument 10.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <param name="arg8">Argument 8.</param>
            <param name="arg9">Argument 9.</param>
            <param name="arg10">Argument 10.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``12(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <typeparam name="TArg8">The type of argument 8.</typeparam>
            <typeparam name="TArg9">The type of argument 9.</typeparam>
            <typeparam name="TArg10">The type of argument 10.</typeparam>
            <typeparam name="TArg11">The type of argument 11.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <param name="arg8">Argument 8.</param>
            <param name="arg9">Argument 9.</param>
            <param name="arg10">Argument 10.</param>
            <param name="arg11">Argument 11.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``13(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <typeparam name="TArg8">The type of argument 8.</typeparam>
            <typeparam name="TArg9">The type of argument 9.</typeparam>
            <typeparam name="TArg10">The type of argument 10.</typeparam>
            <typeparam name="TArg11">The type of argument 11.</typeparam>
            <typeparam name="TArg12">The type of argument 12.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <param name="arg8">Argument 8.</param>
            <param name="arg9">Argument 9.</param>
            <param name="arg10">Argument 10.</param>
            <param name="arg11">Argument 11.</param>
            <param name="arg12">Argument 12.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.FromApm``14(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,System.AsyncCallback,System.Object,System.IAsyncResult},System.Func{System.IAsyncResult,`0},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <typeparam name="TArg8">The type of argument 8.</typeparam>
            <typeparam name="TArg9">The type of argument 9.</typeparam>
            <typeparam name="TArg10">The type of argument 10.</typeparam>
            <typeparam name="TArg11">The type of argument 11.</typeparam>
            <typeparam name="TArg12">The type of argument 12.</typeparam>
            <typeparam name="TArg13">The type of argument 13.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <param name="arg8">Argument 8.</param>
            <param name="arg9">Argument 9.</param>
            <param name="arg10">Argument 10.</param>
            <param name="arg11">Argument 11.</param>
            <param name="arg12">Argument 12.</param>
            <param name="arg13">Argument 13.</param>
            <returns>The result of the asynchronous operation.</returns>
        </member>
        <member name="T:Nito.AsyncEx.AsyncFactory`1.EventArgsTask`1">
            <summary>
            Manages the subscription to an event on a target object, triggering a task (and unsubscribing) when the event is raised.
            </summary>
            <typeparam name="TEventArgs">The type of event arguments passed to the event.</typeparam>
        </member>
        <member name="F:Nito.AsyncEx.AsyncFactory`1.EventArgsTask`1._tcs">
            <summary>
            The source for our task, which is returned to the user.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncFactory`1.EventArgsTask`1._subscription">
            <summary>
            The subscription to the event.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncFactory`1.EventArgsTask`1._target">
            <summary>
            The object that publishes the event.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncFactory`1.EventArgsTask`1._eventInfo">
            <summary>
            The event to which we subscribe.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.EventArgsTask`1.#ctor(System.Object,System.Reflection.EventInfo)">
            <summary>
            Subscribes to the specified event.
            </summary>
            <param name="target">The object that publishes the event.</param>
            <param name="eventInfo">The event to which we subscribe.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory`1.EventArgsTask`1.EventCompleted(System.Object,`1)">
            <summary>
            Private method that handles event completion. Do not call this method; it is public to avoid security problems when reflecting.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncFactory`1.EventArgsTask`1.Task">
            <summary>
            Gets the task that is completed when the event is raised.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncFactory">
            <summary>
            Provides asynchronous wrappers.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm(System.Func{System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult})">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.ToBegin(System.Threading.Tasks.Task,System.AsyncCallback,System.Object)">
            <summary>
            Wraps a <see cref="T:System.Threading.Tasks.Task"/> into the Begin method of an APM pattern.
            </summary>
            <param name="task">The task to wrap.</param>
            <param name="callback">The callback method passed into the Begin method of the APM pattern.</param>
            <param name="state">The state passed into the Begin method of the APM pattern.</param>
            <returns>The asynchronous operation, to be returned by the Begin method of the APM pattern.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.ToEnd(System.IAsyncResult)">
            <summary>
            Wraps a <see cref="T:System.Threading.Tasks.Task"/> into the End method of an APM pattern.
            </summary>
            <param name="asyncResult">The asynchronous operation returned by the matching Begin method of this APM pattern.</param>
            <returns>The result of the asynchronous operation, to be returned by the End method of the APM pattern.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromWaitHandle(System.Threading.WaitHandle)">
            <summary>
            Wraps a <see cref="T:System.Threading.WaitHandle"/> with a <see cref="T:System.Threading.Tasks.Task"/>. When the <see cref="T:System.Threading.WaitHandle"/> is signalled, the returned <see cref="T:System.Threading.Tasks.Task"/> is completed. If the handle is already signalled, this method acts synchronously.
            </summary>
            <param name="handle">The <see cref="T:System.Threading.WaitHandle"/> to observe.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromWaitHandle(System.Threading.WaitHandle,System.TimeSpan)">
            <summary>
            Wraps a <see cref="T:System.Threading.WaitHandle"/> with a <see cref="T:System.Threading.Tasks.Task`1"/>. If the <see cref="T:System.Threading.WaitHandle"/> is signalled, the returned task is completed with a <c>true</c> result. If the observation times out, the returned task is completed with a <c>false</c> result. If the handle is already signalled or the timeout is zero, this method acts synchronously.
            </summary>
            <param name="handle">The <see cref="T:System.Threading.WaitHandle"/> to observe.</param>
            <param name="timeout">The timeout after which the <see cref="T:System.Threading.WaitHandle"/> is no longer observed.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromWaitHandle(System.Threading.WaitHandle,System.Threading.CancellationToken)">
            <summary>
            Wraps a <see cref="T:System.Threading.WaitHandle"/> with a <see cref="T:System.Threading.Tasks.Task`1"/>. If the <see cref="T:System.Threading.WaitHandle"/> is signalled, the returned task is (successfully) completed. If the observation is cancelled, the returned task is cancelled. If the handle is already signalled or the cancellation token is already cancelled, this method acts synchronously.
            </summary>
            <param name="handle">The <see cref="T:System.Threading.WaitHandle"/> to observe.</param>
            <param name="token">The cancellation token that cancels observing the <see cref="T:System.Threading.WaitHandle"/>.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromWaitHandle(System.Threading.WaitHandle,System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            Wraps a <see cref="T:System.Threading.WaitHandle"/> with a <see cref="T:System.Threading.Tasks.Task`1"/>. If the <see cref="T:System.Threading.WaitHandle"/> is signalled, the returned task is completed with a <c>true</c> result. If the observation times out, the returned task is completed with a <c>false</c> result. If the observation is cancelled, the returned task is cancelled. If the handle is already signalled, the timeout is zero, or the cancellation token is already cancelled, then this method acts synchronously.
            </summary>
            <param name="handle">The <see cref="T:System.Threading.WaitHandle"/> to observe.</param>
            <param name="timeout">The timeout after which the <see cref="T:System.Threading.WaitHandle"/> is no longer observed.</param>
            <param name="token">The cancellation token that cancels observing the <see cref="T:System.Threading.WaitHandle"/>.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``1(System.Func{``0,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``2(System.Func{``0,``1,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``3(System.Func{``0,``1,``2,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``4(System.Func{``0,``1,``2,``3,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,``3)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``5(System.Func{``0,``1,``2,``3,``4,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,``3,``4)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``6(System.Func{``0,``1,``2,``3,``4,``5,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,``3,``4,``5)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``7(System.Func{``0,``1,``2,``3,``4,``5,``6,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,``3,``4,``5,``6)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``8(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``9(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <typeparam name="TArg8">The type of argument 8.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <param name="arg8">Argument 8.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``10(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <typeparam name="TArg8">The type of argument 8.</typeparam>
            <typeparam name="TArg9">The type of argument 9.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <param name="arg8">Argument 8.</param>
            <param name="arg9">Argument 9.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``11(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <typeparam name="TArg8">The type of argument 8.</typeparam>
            <typeparam name="TArg9">The type of argument 9.</typeparam>
            <typeparam name="TArg10">The type of argument 10.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <param name="arg8">Argument 8.</param>
            <param name="arg9">Argument 9.</param>
            <param name="arg10">Argument 10.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``12(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <typeparam name="TArg8">The type of argument 8.</typeparam>
            <typeparam name="TArg9">The type of argument 9.</typeparam>
            <typeparam name="TArg10">The type of argument 10.</typeparam>
            <typeparam name="TArg11">The type of argument 11.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <param name="arg8">Argument 8.</param>
            <param name="arg9">Argument 9.</param>
            <param name="arg10">Argument 10.</param>
            <param name="arg11">Argument 11.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``13(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <typeparam name="TArg8">The type of argument 8.</typeparam>
            <typeparam name="TArg9">The type of argument 9.</typeparam>
            <typeparam name="TArg10">The type of argument 10.</typeparam>
            <typeparam name="TArg11">The type of argument 11.</typeparam>
            <typeparam name="TArg12">The type of argument 12.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <param name="arg8">Argument 8.</param>
            <param name="arg9">Argument 9.</param>
            <param name="arg10">Argument 10.</param>
            <param name="arg11">Argument 11.</param>
            <param name="arg12">Argument 12.</param>
            <returns></returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncFactory.FromApm``14(System.Func{``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13,System.AsyncCallback,System.Object,System.IAsyncResult},System.Action{System.IAsyncResult},``0,``1,``2,``3,``4,``5,``6,``7,``8,``9,``10,``11,``12,``13)">
            <summary>
            Wraps a begin/end asynchronous method.
            </summary>
            <typeparam name="TArg0">The type of argument 0.</typeparam>
            <typeparam name="TArg1">The type of argument 1.</typeparam>
            <typeparam name="TArg2">The type of argument 2.</typeparam>
            <typeparam name="TArg3">The type of argument 3.</typeparam>
            <typeparam name="TArg4">The type of argument 4.</typeparam>
            <typeparam name="TArg5">The type of argument 5.</typeparam>
            <typeparam name="TArg6">The type of argument 6.</typeparam>
            <typeparam name="TArg7">The type of argument 7.</typeparam>
            <typeparam name="TArg8">The type of argument 8.</typeparam>
            <typeparam name="TArg9">The type of argument 9.</typeparam>
            <typeparam name="TArg10">The type of argument 10.</typeparam>
            <typeparam name="TArg11">The type of argument 11.</typeparam>
            <typeparam name="TArg12">The type of argument 12.</typeparam>
            <typeparam name="TArg13">The type of argument 13.</typeparam>
            <param name="beginMethod">The begin method.</param>
            <param name="endMethod">The end method.</param>
            <param name="arg0">Argument 0.</param>
            <param name="arg1">Argument 1.</param>
            <param name="arg2">Argument 2.</param>
            <param name="arg3">Argument 3.</param>
            <param name="arg4">Argument 4.</param>
            <param name="arg5">Argument 5.</param>
            <param name="arg6">Argument 6.</param>
            <param name="arg7">Argument 7.</param>
            <param name="arg8">Argument 8.</param>
            <param name="arg9">Argument 9.</param>
            <param name="arg10">Argument 10.</param>
            <param name="arg11">Argument 11.</param>
            <param name="arg12">Argument 12.</param>
            <param name="arg13">Argument 13.</param>
            <returns></returns>
        </member>
        <member name="T:Nito.AsyncEx.AsyncLazy`1">
            <summary>
            Provides support for asynchronous lazy initialization. This type is fully threadsafe.
            </summary>
            <typeparam name="T">The type of object that is being asynchronously initialized.</typeparam>
        </member>
        <member name="F:Nito.AsyncEx.AsyncLazy`1._instance">
            <summary>
            The underlying lazy task.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncLazy`1._id">
            <summary>
            The semi-unique identifier for this instance. This is 0 if the id has not yet been created.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncLazy`1.#ctor(System.Func{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.AsyncLazy`1"/> class.
            </summary>
            <param name="factory">The delegate that is invoked on a background thread to produce the value when it is needed. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncLazy`1.#ctor(System.Func{System.Threading.Tasks.Task{`0}})">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.AsyncLazy`1"/> class.
            </summary>
            <param name="factory">The asynchronous delegate that is invoked on a background thread to produce the value when it is needed. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncLazy`1.GetAwaiter">
            <summary>
            Asynchronous infrastructure support. This method permits instances of <see cref="T:Nito.AsyncEx.AsyncLazy`1"/> to be await'ed.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncLazy`1.Start">
            <summary>
            Starts the asynchronous initialization, if it has not already started.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncLazy`1.Id">
            <summary>
            Gets a semi-unique identifier for this asynchronous lazy instance.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncLazy`1.IsStarted">
            <summary>
            Whether the asynchronous factory method has started. This is initially <c>false</c> and becomes <c>true</c> when this instance is awaited or after <see cref="M:Nito.AsyncEx.AsyncLazy`1.Start"/> is called.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncLock">
            <summary>
            A mutual exclusion lock that is compatible with async. Note that this lock is <b>not</b> recursive!
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncLock._taken">
            <summary>
            Whether the lock is taken by a task.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncLock._queue">
            <summary>
            The queue of TCSs that other tasks are awaiting to acquire the lock.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncLock._cachedKeyTask">
            <summary>
            A task that is completed with the key object for this lock.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncLock._id">
            <summary>
            The semi-unique identifier for this instance. This is 0 if the id has not yet been created.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncLock._mutex">
            <summary>
            The object used for mutual exclusion.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncLock.#ctor">
            <summary>
            Creates a new async-compatible mutual exclusion lock.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncLock.#ctor(Nito.AsyncEx.IAsyncWaitQueue{System.IDisposable})">
            <summary>
            Creates a new async-compatible mutual exclusion lock using the specified wait queue.
            </summary>
            <param name="queue">The wait queue used to manage waiters.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncLock.LockAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously acquires the lock. Returns a disposable that releases the lock when disposed.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the lock. If this is already set, then this method will attempt to take the lock immediately (succeeding if the lock is currently available).</param>
            <returns>A disposable that releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncLock.Lock(System.Threading.CancellationToken)">
            <summary>
            Synchronously acquires the lock. Returns a disposable that releases the lock when disposed. This method may block the calling thread.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the lock. If this is already set, then this method will attempt to take the lock immediately (succeeding if the lock is currently available).</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncLock.LockAsync">
            <summary>
            Asynchronously acquires the lock. Returns a disposable that releases the lock when disposed.
            </summary>
            <returns>A disposable that releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncLock.Lock">
            <summary>
            Synchronously acquires the lock. Returns a disposable that releases the lock when disposed. This method may block the calling thread.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncLock.ReleaseLock">
            <summary>
            Releases the lock.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncLock.Id">
            <summary>
            Gets a semi-unique identifier for this asynchronous lock.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncLock.Key">
            <summary>
            The disposable which releases the lock.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncLock.Key._asyncLock">
            <summary>
            The lock to release.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncLock.Key.#ctor(Nito.AsyncEx.AsyncLock)">
            <summary>
            Creates the key for a lock.
            </summary>
            <param name="asyncLock">The lock to release. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncLock.Key.Dispose">
            <summary>
            Release the lock.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncManualResetEvent">
            <summary>
            An async-compatible manual-reset event.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncManualResetEvent._sync">
            <summary>
            The object used for synchronization.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncManualResetEvent._tcs">
            <summary>
            The current state of the event.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncManualResetEvent._id">
            <summary>
            The semi-unique identifier for this instance. This is 0 if the id has not yet been created.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncManualResetEvent.#ctor(System.Boolean)">
            <summary>
            Creates an async-compatible manual-reset event.
            </summary>
            <param name="set">Whether the manual-reset event is initially set or unset.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncManualResetEvent.#ctor">
            <summary>
            Creates an async-compatible manual-reset event that is initially unset.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncManualResetEvent.WaitAsync">
            <summary>
            Asynchronously waits for this event to be set.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncManualResetEvent.Wait">
            <summary>
            Synchronously waits for this event to be set. This method may block the calling thread.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncManualResetEvent.Wait(System.Threading.CancellationToken)">
            <summary>
            Synchronously waits for this event to be set. This method may block the calling thread.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the wait. If this token is already canceled, this method will first check whether the event is set.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncManualResetEvent.Set">
            <summary>
            Sets the event, atomically completing every task returned by <see cref="M:Nito.AsyncEx.AsyncManualResetEvent.WaitAsync"/>. If the event is already set, this method does nothing.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncManualResetEvent.Reset">
            <summary>
            Resets the event. If the event is already reset, this method does nothing.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncManualResetEvent.Id">
            <summary>
            Gets a semi-unique identifier for this asynchronous manual-reset event.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncMonitor">
            <summary>
            An async-compatible monitor.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncMonitor._asyncLock">
            <summary>
            The lock.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncMonitor._conditionVariable">
            <summary>
            The condition variable.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncMonitor.#ctor(Nito.AsyncEx.IAsyncWaitQueue{System.IDisposable},Nito.AsyncEx.IAsyncWaitQueue{System.Object})">
            <summary>
            Constructs a new monitor.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncMonitor.#ctor">
            <summary>
            Constructs a new monitor.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncMonitor.EnterAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously enters the monitor. Returns a disposable that leaves the monitor when disposed.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the enter. If this is already set, then this method will attempt to enter the monitor immediately (succeeding if the monitor is currently available).</param>
            <returns>A disposable that leaves the monitor when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncMonitor.Enter(System.Threading.CancellationToken)">
            <summary>
            Synchronously enters the monitor. Returns a disposable that leaves the monitor when disposed. This method may block the calling thread.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the enter. If this is already set, then this method will attempt to enter the monitor immediately (succeeding if the monitor is currently available).</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncMonitor.EnterAsync">
            <summary>
            Asynchronously enters the monitor. Returns a disposable that leaves the monitor when disposed.
            </summary>
            <returns>A disposable that leaves the monitor when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncMonitor.Enter">
            <summary>
            Asynchronously enters the monitor. Returns a disposable that leaves the monitor when disposed. This method may block the calling thread.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncMonitor.WaitAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously waits for a pulse signal on this monitor. The monitor MUST already be entered when calling this method, and it will still be entered when this method returns, even if the method is cancelled. This method internally will leave the monitor while waiting for a notification.
            </summary>
            <param name="cancellationToken">The cancellation signal used to cancel this wait.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncMonitor.Wait(System.Threading.CancellationToken)">
            <summary>
            Asynchronously waits for a pulse signal on this monitor. This method may block the calling thread. The monitor MUST already be entered when calling this method, and it will still be entered when this method returns, even if the method is cancelled. This method internally will leave the monitor while waiting for a notification.
            </summary>
            <param name="cancellationToken">The cancellation signal used to cancel this wait.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncMonitor.WaitAsync">
            <summary>
            Asynchronously waits for a pulse signal on this monitor. The monitor MUST already be entered when calling this method, and it will still be entered when this method returns. This method internally will leave the monitor while waiting for a notification.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncMonitor.Wait">
            <summary>
            Asynchronously waits for a pulse signal on this monitor. This method may block the calling thread. The monitor MUST already be entered when calling this method, and it will still be entered when this method returns. This method internally will leave the monitor while waiting for a notification.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncMonitor.Pulse">
            <summary>
            Sends a signal to a single task waiting on this monitor. The monitor MUST already be entered when calling this method, and it will still be entered when this method returns.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncMonitor.PulseAll">
            <summary>
            Sends a signal to all tasks waiting on this monitor. The monitor MUST already be entered when calling this method, and it will still be entered when this method returns.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncMonitor.Id">
            <summary>
            Gets a semi-unique identifier for this monitor.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncProducerConsumerQueue`1">
            <summary>
            An async-compatible producer/consumer queue.
            </summary>
            <typeparam name="T">The type of elements contained in the queue.</typeparam>
        </member>
        <member name="F:Nito.AsyncEx.AsyncProducerConsumerQueue`1._queue">
            <summary>
            The underlying queue.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncProducerConsumerQueue`1._maxCount">
            <summary>
            The maximum number of elements allowed in the queue.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncProducerConsumerQueue`1._mutex">
            <summary>
            The mutual-exclusion lock protecting the queue.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncProducerConsumerQueue`1._notFull">
            <summary>
            A condition variable that is signalled when the queue is not full.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncProducerConsumerQueue`1._completedOrNotEmpty">
            <summary>
            A condition variable that is signalled when the queue is completed or not empty.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncProducerConsumerQueue`1._completed">
            <summary>
            A cancellation token source that is canceled when the queue is marked completed for adding.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncProducerConsumerQueue`1.FalseResult">
            <summary>
            A cached result that is common when calling <see cref="o:AsyncProducerConsumerQueueExtensions.TryDequeueFromAnyAsync"/>.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Int32)">
            <summary>
            Creates a new async-compatible producer/consumer queue with the specified initial elements and a maximum element count.
            </summary>
            <param name="collection">The initial elements to place in the queue.</param>
            <param name="maxCount">The maximum element count. This must be greater than zero.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Creates a new async-compatible producer/consumer queue with the specified initial elements.
            </summary>
            <param name="collection">The initial elements to place in the queue.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.#ctor(System.Int32)">
            <summary>
            Creates a new async-compatible producer/consumer queue with a maximum element count.
            </summary>
            <param name="maxCount">The maximum element count. This must be greater than zero.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.#ctor">
            <summary>
            Creates a new async-compatible producer/consumer queue.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.Dispose">
            <summary>
            Releases resources held by this instance. After disposal, any use of this instance is undefined.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.CompleteAddingAsync">
            <summary>
            Asynchronously marks the producer/consumer queue as complete for adding.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.CompleteAdding">
            <summary>
            Synchronously marks the producer/consumer queue as complete for adding.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.TryEnqueueAsync(`0,System.Threading.CancellationToken,Nito.AsyncEx.TaskCompletionSource)">
            <summary>
            Attempts to enqueue an item.
            </summary>
            <param name="item">The item to enqueue.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the enqueue operation. If <paramref name="abort"/> is not <c>null</c>, then this token must include signals from the <paramref name="abort"/> object.</param>
            <param name="abort">A synchronization object used to cancel related enqueue operations. May be <c>null</c> if this is the only enqueue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.DoTryEnqueue(`0,System.Threading.CancellationToken)">
            <summary>
            Attempts to enqueue an item. This method may block the calling thread.
            </summary>
            <param name="item">The item to enqueue.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the enqueue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.TryEnqueueAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Attempts to enqueue an item to the producer/consumer queue. Returns <c>false</c> if the producer/consumer queue has completed adding.
            </summary>
            <param name="item">The item to enqueue.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the enqueue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.TryEnqueue(`0,System.Threading.CancellationToken)">
            <summary>
            Attempts to enqueue an item to the producer/consumer queue. Returns <c>false</c> if the producer/consumer queue has completed adding. This method may block the calling thread.
            </summary>
            <param name="item">The item to enqueue.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the enqueue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.TryEnqueueAsync(`0)">
            <summary>
            Attempts to enqueue an item to the producer/consumer queue. Returns <c>false</c> if the producer/consumer queue has completed adding.
            </summary>
            <param name="item">The item to enqueue.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.TryEnqueue(`0)">
            <summary>
            Attempts to enqueue an item to the producer/consumer queue. Returns <c>false</c> if the producer/consumer queue has completed adding. This method may block the calling thread.
            </summary>
            <param name="item">The item to enqueue.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.EnqueueAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Enqueues an item to the producer/consumer queue. Throws <see cref="T:System.InvalidOperationException"/> if the producer/consumer queue has completed adding.
            </summary>
            <param name="item">The item to enqueue.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the enqueue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.Enqueue(`0,System.Threading.CancellationToken)">
            <summary>
            Enqueues an item to the producer/consumer queue. Throws <see cref="T:System.InvalidOperationException"/> if the producer/consumer queue has completed adding. This method may block the calling thread.
            </summary>
            <param name="item">The item to enqueue.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the enqueue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.EnqueueAsync(`0)">
            <summary>
            Enqueues an item to the producer/consumer queue. Throws <see cref="T:System.InvalidOperationException"/> if the producer/consumer queue has completed adding.
            </summary>
            <param name="item">The item to enqueue.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.Enqueue(`0)">
            <summary>
            Enqueues an item to the producer/consumer queue. This method may block the calling thread. Throws <see cref="T:System.InvalidOperationException"/> if the producer/consumer queue has completed adding.
            </summary>
            <param name="item">The item to enqueue.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.OutputAvailableAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously waits until an item is available to dequeue. Returns <c>false</c> if the producer/consumer queue has completed adding and there are no more items.
            </summary>
            <param name="cancellationToken">A cancellation token that can be used to abort the asynchronous wait.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.OutputAvailableAsync">
            <summary>
            Asynchronously waits until an item is available to dequeue. Returns <c>false</c> if the producer/consumer queue has completed adding and there are no more items.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.GetConsumingEnumerable(System.Threading.CancellationToken)">
            <summary>
            Provides a (synchronous) consuming enumerable for items in the producer/consumer queue.
            </summary>
            <param name="cancellationToken">A cancellation token that can be used to abort the synchronous enumeration.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.GetConsumingEnumerable">
            <summary>
            Provides a (synchronous) consuming enumerable for items in the producer/consumer queue.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.TryDequeueAsync(System.Threading.CancellationToken,Nito.AsyncEx.TaskCompletionSource)">
            <summary>
            Attempts to dequeue an item.
            </summary>
            <param name="cancellationToken">A cancellation token that can be used to abort the dequeue operation. If <paramref name="abort"/> is not <c>null</c>, then this token must include signals from the <paramref name="abort"/> object.</param>
            <param name="abort">A synchronization object used to cancel related dequeue operations. May be <c>null</c> if this is the only dequeue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.DoTryDequeue(System.Threading.CancellationToken)">
            <summary>
            Attempts to dequeue an item. This method may block the calling thread.
            </summary>
            <param name="cancellationToken">A cancellation token that can be used to abort the dequeue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.TryDequeueAsync(System.Threading.CancellationToken)">
            <summary>
            Attempts to dequeue an item from the producer/consumer queue.
            </summary>
            <param name="cancellationToken">A cancellation token that can be used to abort the dequeue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.TryDequeue(System.Threading.CancellationToken)">
            <summary>
            Attempts to dequeue an item from the producer/consumer queue. This method may block the calling thread.
            </summary>
            <param name="cancellationToken">A cancellation token that can be used to abort the dequeue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.TryDequeueAsync">
            <summary>
            Attempts to dequeue an item from the producer/consumer queue.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.TryDequeue">
            <summary>
            Attempts to dequeue an item from the producer/consumer queue. This method may block the calling thread.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.DequeueAsync(System.Threading.CancellationToken)">
            <summary>
            Dequeues an item from the producer/consumer queue. Returns the dequeued item. Throws <see cref="T:System.InvalidOperationException"/> if the producer/consumer queue has completed adding and is empty.
            </summary>
            <param name="cancellationToken">A cancellation token that can be used to abort the dequeue operation.</param>
            <returns>The dequeued item.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.Dequeue(System.Threading.CancellationToken)">
            <summary>
            Dequeues an item from the producer/consumer queue. Returns the dequeued item. This method may block the calling thread. Throws <see cref="T:System.InvalidOperationException"/> if the producer/consumer queue has completed adding and is empty.
            </summary>
            <param name="cancellationToken">A cancellation token that can be used to abort the dequeue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.DequeueAsync">
            <summary>
            Dequeues an item from the producer/consumer queue. Returns the dequeued item. Throws <see cref="T:System.InvalidOperationException"/> if the producer/consumer queue has completed adding and is empty.
            </summary>
            <returns>The dequeued item.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueue`1.Dequeue">
            <summary>
            Dequeues an item from the producer/consumer queue. Returns the dequeued item. This method may block the calling thread. Throws <see cref="T:System.InvalidOperationException"/> if the producer/consumer queue has completed adding and is empty.
            </summary>
            <returns>The dequeued item.</returns>
        </member>
        <member name="P:Nito.AsyncEx.AsyncProducerConsumerQueue`1.Empty">
            <summary>
            Whether the queue is empty.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncProducerConsumerQueue`1.Full">
            <summary>
            Whether the queue is full.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncProducerConsumerQueue`1.DequeueResult">
            <summary>
            The result of a <c>TryDequeue</c>, <c>DequeueFromAny</c>, or <c>TryDequeueFromAny</c> operation.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncProducerConsumerQueue`1.DequeueResult.Queue">
            <summary>
            The queue from which the item was dequeued, or <c>null</c> if the operation failed.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncProducerConsumerQueue`1.DequeueResult.Success">
            <summary>
            Whether the operation was successful. This is <c>true</c> if and only if <see cref="P:Nito.AsyncEx.AsyncProducerConsumerQueue`1.DequeueResult.Queue"/> is not <c>null</c>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncProducerConsumerQueue`1.DequeueResult.Item">
            <summary>
            The dequeued item. This is only valid if <see cref="P:Nito.AsyncEx.AsyncProducerConsumerQueue`1.DequeueResult.Queue"/> is not <c>null</c>.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions">
            <summary>
            Provides methods for working on multiple <see cref="T:Nito.AsyncEx.AsyncProducerConsumerQueue`1"/> instances.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.TryEnqueueToAnyAsync``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}},``0,System.Threading.CancellationToken)">
            <summary>
            Attempts to enqueue an item to any of a number of producer/consumer queues. Returns the producer/consumer queue that received the item. Returns <c>null</c> if all producer/consumer queues have completed adding.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
            <param name="item">The item to enqueue.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the enqueue operation.</param>
            <returns>The producer/consumer queue that received the item.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.TryEnqueueToAny``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}},``0,System.Threading.CancellationToken)">
            <summary>
            Attempts to enqueue an item to any of a number of producer/consumer queues. Returns the producer/consumer queue that received the item. Returns <c>null</c> if all producer/consumer queues have completed adding. This method may block the calling thread.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
            <param name="item">The item to enqueue.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the enqueue operation.</param>
            <returns>The producer/consumer queue that received the item.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.TryEnqueueToAnyAsync``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}},``0)">
            <summary>
            Attempts to enqueue an item to any of a number of producer/consumer queues. Returns the producer/consumer queue that received the item. Returns <c>null</c> if all producer/consumer queues have completed adding.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
            <param name="item">The item to enqueue.</param>
            <returns>The producer/consumer queue that received the item.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.TryEnqueueToAny``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}},``0)">
            <summary>
            Attempts to enqueue an item to any of a number of producer/consumer queues. Returns the producer/consumer queue that received the item. Returns <c>null</c> if all producer/consumer queues have completed adding. This method may block the calling thread.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
            <param name="item">The item to enqueue.</param>
            <returns>The producer/consumer queue that received the item.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.EnqueueToAnyAsync``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}},``0,System.Threading.CancellationToken)">
            <summary>
            Enqueues an item to any of a number of producer/consumer queues. Returns the producer/consumer queue that received the item. Throws <see cref="T:System.InvalidOperationException"/> if all producer/consumer queues have completed adding.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
            <param name="item">The item to enqueue.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the enqueue operation.</param>
            <returns>The producer/consumer queue that received the item.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.EnqueueToAny``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}},``0,System.Threading.CancellationToken)">
            <summary>
            Enqueues an item to any of a number of producer/consumer queues. Returns the producer/consumer queue that received the item. Throws <see cref="T:System.InvalidOperationException"/> if all producer/consumer queues have completed adding. This method may block the calling thread.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
            <param name="item">The item to enqueue.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the enqueue operation.</param>
            <returns>The producer/consumer queue that received the item.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.EnqueueToAnyAsync``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}},``0)">
            <summary>
            Enqueues an item to any of a number of producer/consumer queues. Returns the producer/consumer queue that received the item. Throws <see cref="T:System.InvalidOperationException"/> if all producer/consumer queues have completed adding.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
            <param name="item">The item to enqueue.</param>
            <returns>The producer/consumer queue that received the item.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.EnqueueToAny``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}},``0)">
            <summary>
            Enqueues an item to any of a number of producer/consumer queues. Returns the producer/consumer queue that received the item. Throws <see cref="T:System.InvalidOperationException"/> if all producer/consumer queues have completed adding. This method may block the calling thread.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
            <param name="item">The item to enqueue.</param>
            <returns>The producer/consumer queue that received the item.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.TryDequeueFromAnyAsync``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}},System.Threading.CancellationToken)">
            <summary>
            Attempts to dequeue an item from any of a number of producer/consumer queues. The operation "fails" if all the producer/consumer queues have completed adding and are empty.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the dequeue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.TryDequeueFromAny``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}},System.Threading.CancellationToken)">
            <summary>
            Attempts to dequeue an item from any of a number of producer/consumer queues. The operation "fails" if all the producer/consumer queues have completed adding and are empty. This method may block the calling thread.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the dequeue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.TryDequeueFromAnyAsync``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}})">
            <summary>
            Attempts to dequeue an item from any of a number of producer/consumer queues. The operation "fails" if all the producer/consumer queues have completed adding and are empty.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.TryDequeueFromAny``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}})">
            <summary>
            Attempts to dequeue an item from any of a number of producer/consumer queues. The operation "fails" if all the producer/consumer queues have completed adding and are empty. This method may block the calling thread.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.DequeueFromAnyAsync``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}},System.Threading.CancellationToken)">
            <summary>
            Dequeues an item from any of a number of producer/consumer queues. Throws <see cref="T:System.InvalidOperationException"/> if all the producer/consumer queues have completed adding and are empty.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the dequeue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.DequeueFromAny``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}},System.Threading.CancellationToken)">
            <summary>
            Dequeues an item from any of a number of producer/consumer queues. Throws <see cref="T:System.InvalidOperationException"/> if all the producer/consumer queues have completed adding and are empty. This method may block the calling thread.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
            <param name="cancellationToken">A cancellation token that can be used to abort the dequeue operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.DequeueFromAnyAsync``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}})">
            <summary>
            Dequeues an item from any of a number of producer/consumer queues. Throws <see cref="T:System.InvalidOperationException"/> if all the producer/consumer queues have completed adding and are empty.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncProducerConsumerQueueExtensions.DequeueFromAny``1(System.Collections.Generic.IEnumerable{Nito.AsyncEx.AsyncProducerConsumerQueue{``0}})">
            <summary>
            Dequeues an item from any of a number of producer/consumer queues. Throws <see cref="T:System.InvalidOperationException"/> if all the producer/consumer queues have completed adding and are empty. This method may block the calling thread.
            </summary>
            <param name="queues">The producer/consumer queues.</param>
        </member>
        <member name="T:Nito.AsyncEx.AsyncReaderWriterLock">
            <summary>
            A reader/writer lock that is compatible with async. Note that this lock is <b>not</b> recursive!
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock._writerQueue">
            <summary>
            The queue of TCSs that other tasks are awaiting to acquire the lock as writers.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock._readerQueue">
            <summary>
            The queue of TCSs that other tasks are awaiting to acquire the lock as readers.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock._upgradeableReaderQueue">
            <summary>
            The queue of TCSs that other tasks are awaiting to acquire the lock as upgradeable readers.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock._upgradeReaderQueue">
            <summary>
            The queue of TCSs that other tasks are awaiting to upgrade a reader lock to a writer lock.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock._id">
            <summary>
            The semi-unique identifier for this instance. This is 0 if the id has not yet been created.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock._upgradeableReaderKey">
            <summary>
            The current upgradeable reader lock key, if any. If this is not <c>null</c>, then there is an upgradeable reader lock held.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock._locksHeld">
            <summary>
            Number of reader locks held (including an upgradeable reader lock, if applicable); -1 if a writer lock is held; 0 if no locks are held.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock._mutex">
            <summary>
            The object used for mutual exclusion.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock._cachedReaderKeyTask">
            <summary>
            A task that is completed with the reader key object for this lock.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock._cachedWriterKeyTask">
            <summary>
            A task that is completed with the writer key object for this lock.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.#ctor(Nito.AsyncEx.IAsyncWaitQueue{System.IDisposable},Nito.AsyncEx.IAsyncWaitQueue{System.IDisposable},Nito.AsyncEx.IAsyncWaitQueue{Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey},Nito.AsyncEx.IAsyncWaitQueue{System.IDisposable})">
            <summary>
            Creates a new async-compatible reader/writer lock.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.#ctor">
            <summary>
            Creates a new async-compatible reader/writer lock.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.ReleaseWaitersWhenCanceled(System.Threading.Tasks.Task)">
            <summary>
            Applies a continuation to the task that will call <see cref="M:Nito.AsyncEx.AsyncReaderWriterLock.ReleaseWaiters"/> if the task is canceled. This method may not be called while holding the sync lock.
            </summary>
            <param name="task">The task to observe for cancellation.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.ReaderLockAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously acquires the lock as a reader. Returns a disposable that releases the lock when disposed.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the lock. If this is already set, then this method will attempt to take the lock immediately (succeeding if the lock is currently available).</param>
            <returns>A disposable that releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.ReaderLock(System.Threading.CancellationToken)">
            <summary>
            Synchronously acquires the lock as a reader. Returns a disposable that releases the lock when disposed. This method may block the calling thread.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the lock. If this is already set, then this method will attempt to take the lock immediately (succeeding if the lock is currently available).</param>
            <returns>A disposable that releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.ReaderLockAsync">
            <summary>
            Asynchronously acquires the lock as a reader. Returns a disposable that releases the lock when disposed.
            </summary>
            <returns>A disposable that releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.ReaderLock">
            <summary>
            Synchronously acquires the lock as a reader. Returns a disposable that releases the lock when disposed. This method may block the calling thread.
            </summary>
            <returns>A disposable that releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.WriterLockAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously acquires the lock as a writer. Returns a disposable that releases the lock when disposed.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the lock. If this is already set, then this method will attempt to take the lock immediately (succeeding if the lock is currently available).</param>
            <returns>A disposable that releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.WriterLock(System.Threading.CancellationToken)">
            <summary>
            Synchronously acquires the lock as a writer. Returns a disposable that releases the lock when disposed. This method may block the calling thread.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the lock. If this is already set, then this method will attempt to take the lock immediately (succeeding if the lock is currently available).</param>
            <returns>A disposable that releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.WriterLockAsync">
            <summary>
            Asynchronously acquires the lock as a writer. Returns a disposable that releases the lock when disposed.
            </summary>
            <returns>A disposable that releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.WriterLock">
            <summary>
            Asynchronously acquires the lock as a writer. Returns a disposable that releases the lock when disposed. This method may block the calling thread.
            </summary>
            <returns>A disposable that releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderLockAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously acquires the lock as a reader with the option to upgrade. Returns a key that can be used to upgrade and downgrade the lock, and releases the lock when disposed.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the lock. If this is already set, then this method will attempt to take the lock immediately (succeeding if the lock is currently available).</param>
            <returns>A key that can be used to upgrade and downgrade this lock, and releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderLock(System.Threading.CancellationToken)">
            <summary>
            Synchronously acquires the lock as a reader with the option to upgrade. Returns a key that can be used to upgrade and downgrade the lock, and releases the lock when disposed. This method may block the calling thread.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the lock. If this is already set, then this method will attempt to take the lock immediately (succeeding if the lock is currently available).</param>
            <returns>A key that can be used to upgrade and downgrade this lock, and releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderLockAsync">
            <summary>
            Asynchronously acquires the lock as a reader with the option to upgrade. Returns a key that can be used to upgrade and downgrade the lock, and releases the lock when disposed.
            </summary>
            <returns>A key that can be used to upgrade and downgrade this lock, and releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderLock">
            <summary>
            Synchronously acquires the lock as a reader with the option to upgrade. Returns a key that can be used to upgrade and downgrade the lock, and releases the lock when disposed. This method may block the calling thread.
            </summary>
            <returns>A key that can be used to upgrade and downgrade this lock, and releases the lock when disposed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously upgrades a reader lock to a writer lock. This method assumes the sync lock is already held.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.Downgrade">
            <summary>
            Downgrades a writer lock to a reader lock. This method assumes the sync lock is already held.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.ReleaseWaiters">
            <summary>
            Grants lock(s) to waiting tasks. This method assumes the sync lock is already held.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.ReleaseReaderLock">
            <summary>
            Releases the lock as a reader.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.ReleaseWriterLock">
            <summary>
            Releases the lock as a writer.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.ReleaseUpgradeableReaderLock(System.Threading.Tasks.Task)">
            <summary>
            Releases the lock as an upgradeable reader.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncReaderWriterLock.Id">
            <summary>
            Gets a semi-unique identifier for this asynchronous lock.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncReaderWriterLock.ReaderKey">
            <summary>
            The disposable which releases the reader lock.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock.ReaderKey._asyncReaderWriterLock">
            <summary>
            The lock to release.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.ReaderKey.#ctor(Nito.AsyncEx.AsyncReaderWriterLock)">
            <summary>
            Creates the key for a lock.
            </summary>
            <param name="asyncReaderWriterLock">The lock to release. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.ReaderKey.Dispose">
            <summary>
            Release the lock.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncReaderWriterLock.WriterKey">
            <summary>
            The disposable which releases the writer lock.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock.WriterKey._asyncReaderWriterLock">
            <summary>
            The lock to release.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.WriterKey.#ctor(Nito.AsyncEx.AsyncReaderWriterLock)">
            <summary>
            Creates the key for a lock.
            </summary>
            <param name="asyncReaderWriterLock">The lock to release. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.WriterKey.Dispose">
            <summary>
            Release the lock.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey">
            <summary>
            The disposable which manages the upgradeable reader lock.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey._asyncReaderWriterLock">
            <summary>
            The lock to release.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey._upgrade">
            <summary>
            The task doing the upgrade.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey._cachedUpgradeKeyTask">
            <summary>
            A task that is completed with the upgrade key object for this key.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey.#ctor(Nito.AsyncEx.AsyncReaderWriterLock)">
            <summary>
            Creates the key for a lock.
            </summary>
            <param name="asyncReaderWriterLock">The lock to release. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey.UpgradeAsync(System.Threading.CancellationToken)">
            <summary>
            Upgrades the reader lock to a writer lock. Returns a disposable that downgrades the writer lock to a reader lock when disposed.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the upgrade. If this is already set, then this method will attempt to upgrade immediately (succeeding if the lock is currently available).</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey.Upgrade(System.Threading.CancellationToken)">
            <summary>
            Synchronously upgrades the reader lock to a writer lock. Returns a disposable that downgrades the writer lock to a reader lock when disposed. This method may block the calling thread.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the upgrade. If this is already set, then this method will attempt to upgrade immediately (succeeding if the lock is currently available).</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey.UpgradeAsync">
            <summary>
            Upgrades the reader lock to a writer lock. Returns a disposable that downgrades the writer lock to a reader lock when disposed.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey.Upgrade">
            <summary>
            Synchronously upgrades the reader lock to a writer lock. Returns a disposable that downgrades the writer lock to a reader lock when disposed. This method may block the calling thread.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey.Downgrade">
            <summary>
            Downgrades the writer lock to a reader lock.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey.Dispose">
            <summary>
            Release the lock.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey.Upgraded">
            <summary>
            Gets a value indicating whether this lock has been upgraded to a write lock.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey.UpgradeKey">
            <summary>
            The disposable which downgrades an upgradeable reader key.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey.UpgradeKey._key">
            <summary>
            The upgradeable reader key to downgrade.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey.UpgradeKey.#ctor(Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey)">
            <summary>
            Creates the upgrade key for an upgradeable reader key.
            </summary>
            <param name="key">The upgradeable reader key to downgrade. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncReaderWriterLock.UpgradeableReaderKey.UpgradeKey.Dispose">
            <summary>
            Downgrade the upgradeable reader key.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncSemaphore">
            <summary>
            An async-compatible semaphore. Alternatively, you could use <c>SemaphoreSlim</c> on .NET 4.5 / Windows Store.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncSemaphore._queue">
            <summary>
            The queue of TCSs that other tasks are awaiting to acquire the semaphore.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncSemaphore._count">
            <summary>
            The number of waits that will be immediately granted.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncSemaphore._id">
            <summary>
            The semi-unique identifier for this instance. This is 0 if the id has not yet been created.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.AsyncSemaphore._mutex">
            <summary>
            The object used for mutual exclusion.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncSemaphore.#ctor(System.Int32,Nito.AsyncEx.IAsyncWaitQueue{System.Object})">
            <summary>
            Creates a new async-compatible semaphore with the specified initial count.
            </summary>
            <param name="initialCount">The initial count for this semaphore. This must be greater than or equal to zero.</param>
            <param name="queue">The wait queue used to manage waiters.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncSemaphore.#ctor(System.Int32)">
            <summary>
            Creates a new async-compatible semaphore with the specified initial count.
            </summary>
            <param name="initialCount">The initial count for this semaphore. This must be greater than or equal to zero.</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncSemaphore.WaitAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously waits for a slot in the semaphore to be available.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the wait. If this is already set, then this method will attempt to take the slot immediately (succeeding if a slot is currently available).</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncSemaphore.Wait(System.Threading.CancellationToken)">
            <summary>
            Synchronously waits for a slot in the semaphore to be available. This method may block the calling thread.
            </summary>
            <param name="cancellationToken">The cancellation token used to cancel the wait. If this is already set, then this method will attempt to take the slot immediately (succeeding if a slot is currently available).</param>
        </member>
        <member name="M:Nito.AsyncEx.AsyncSemaphore.WaitAsync">
            <summary>
            Asynchronously waits for a slot in the semaphore to be available.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncSemaphore.Wait">
            <summary>
            Synchronously waits for a slot in the semaphore to be available. This method may block the calling thread.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncSemaphore.Release(System.Int32)">
            <summary>
            Releases the semaphore.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncSemaphore.Release">
            <summary>
            Releases the semaphore.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncSemaphore.Id">
            <summary>
            Gets a semi-unique identifier for this asynchronous semaphore.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.AsyncSemaphore.CurrentCount">
            <summary>
            Gets the number of slots currently available on this semaphore.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.IAsyncWaitQueue`1">
            <summary>
            A collection of cancelable <see cref="T:System.Threading.Tasks.TaskCompletionSource`1"/> instances. Implementations must be threadsafe <b>and</b> must work correctly if the caller is holding a lock.
            </summary>
            <typeparam name="T">The type of the results. If this isn't needed, use <see cref="T:System.Object"/>.</typeparam>
        </member>
        <member name="M:Nito.AsyncEx.IAsyncWaitQueue`1.Enqueue">
            <summary>
            Creates a new entry and queues it to this wait queue. The returned task must support both synchronous and asynchronous waits.
            </summary>
            <returns>The queued task.</returns>
        </member>
        <member name="M:Nito.AsyncEx.IAsyncWaitQueue`1.Dequeue(`0)">
            <summary>
            Removes a single entry in the wait queue. Returns a disposable that completes that entry.
            </summary>
            <param name="result">The result used to complete the wait queue entry. If this isn't needed, use <c>default(T)</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.IAsyncWaitQueue`1.DequeueAll(`0)">
            <summary>
            Removes all entries in the wait queue. Returns a disposable that completes all entries.
            </summary>
            <param name="result">The result used to complete the wait queue entries. If this isn't needed, use <c>default(T)</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.IAsyncWaitQueue`1.TryCancel(System.Threading.Tasks.Task)">
            <summary>
            Attempts to remove an entry from the wait queue. Returns a disposable that cancels the entry.
            </summary>
            <param name="task">The task to cancel.</param>
            <returns>A value indicating whether the entry was found and canceled.</returns>
        </member>
        <member name="M:Nito.AsyncEx.IAsyncWaitQueue`1.CancelAll">
            <summary>
            Removes all entries from the wait queue. Returns a disposable that cancels all entries.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.IAsyncWaitQueue`1.IsEmpty">
            <summary>
            Gets whether the queue is empty.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.AsyncWaitQueueExtensions">
            <summary>
            Provides extension methods for wait queues.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AsyncWaitQueueExtensions.Enqueue``1(Nito.AsyncEx.IAsyncWaitQueue{``0},System.Threading.CancellationToken)">
            <summary>
            Creates a new entry and queues it to this wait queue. If the cancellation token is already canceled, this method immediately returns a canceled task without modifying the wait queue.
            </summary>
            <param name="this">The wait queue.</param>
            <param name="token">The token used to cancel the wait.</param>
            <returns>The queued task.</returns>
        </member>
        <member name="M:Nito.AsyncEx.AsyncWaitQueueExtensions.Enqueue``1(Nito.AsyncEx.IAsyncWaitQueue{``0},System.Object,System.Threading.CancellationToken)">
            <summary>
            Creates a new entry and queues it to this wait queue. If the cancellation token is already canceled, this method immediately returns a canceled task without modifying the wait queue.
            </summary>
            <param name="this">The wait queue.</param>
            <param name="syncObject">A synchronization object taken while cancelling the entry.</param>
            <param name="token">The token used to cancel the wait.</param>
            <returns>The queued task.</returns>
        </member>
        <member name="T:Nito.AsyncEx.DefaultAsyncWaitQueue`1">
            <summary>
            The default wait queue implementation, which uses a double-ended queue.
            </summary>
            <typeparam name="T">The type of the results. If this isn't needed, use <see cref="T:System.Object"/>.</typeparam>
        </member>
        <member name="T:Nito.AsyncEx.AwaitableDisposable`1">
            <summary>
            An awaitable wrapper around a task whose result is disposable. The wrapper is not disposable, so this prevents usage errors like "using (MyAsync())" when the appropriate usage should be "using (await MyAsync())".
            </summary>
            <typeparam name="T">The type of the result of the underlying task.</typeparam>
        </member>
        <member name="F:Nito.AsyncEx.AwaitableDisposable`1._task">
            <summary>
            The underlying task.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AwaitableDisposable`1.#ctor(System.Threading.Tasks.Task{`0})">
            <summary>
            Initializes a new awaitable wrapper around the specified task.
            </summary>
            <param name="task">The underlying task to wrap.</param>
        </member>
        <member name="M:Nito.AsyncEx.AwaitableDisposable`1.AsTask">
            <summary>
            Returns the underlying task.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AwaitableDisposable`1.op_Implicit(Nito.AsyncEx.AwaitableDisposable{`0})~System.Threading.Tasks.Task{`0}">
            <summary>
            Implicit conversion to the underlying task.
            </summary>
            <param name="source">The awaitable wrapper.</param>
        </member>
        <member name="M:Nito.AsyncEx.AwaitableDisposable`1.GetAwaiter">
            <summary>
            Infrastructure. Returns the task awaiter for the underlying task.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.AwaitableDisposable`1.ConfigureAwait(System.Boolean)">
            <summary>
            Infrastructure. Returns a configured task awaiter for the underlying task.
            </summary>
            <param name="continueOnCapturedContext">Whether to attempt to marshal the continuation back to the captured context.</param>
        </member>
        <member name="T:Nito.AsyncEx.CancellationTokenExtensions">
            <summary>
            Provides extension methods for <see cref="T:System.Threading.CancellationToken"/>.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.CancellationTokenExtensions.AsTask(System.Threading.CancellationToken)">
            <summary>
            Returns a <see cref="T:System.Threading.Tasks.Task"/> that is canceled when this <see cref="T:System.Threading.CancellationToken"/> is canceled.
            </summary>
            <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken"/> to monitor.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that is canceled when this <see cref="T:System.Threading.CancellationToken"/> is canceled.</returns>
        </member>
        <member name="T:Nito.AsyncEx.CancellationTokenHelpers">
            <summary>
            Helper methods for cancellation tokens.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.CancellationTokenHelpers.#cctor">
            <summary>
            Initializes the static members.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.CancellationTokenHelpers.Timeout(System.TimeSpan)">
            <summary>
            Creates a cancellation token that is canceled after the due time.
            </summary>
            <param name="dueTime">The due time after which to cancel the token.</param>
            <returns>A cancellation token that is canceled after the due time.</returns>
        </member>
        <member name="M:Nito.AsyncEx.CancellationTokenHelpers.Timeout(System.Int32)">
            <summary>
            Creates a cancellation token that is canceled after the due time.
            </summary>
            <param name="dueTime">The due time after which to cancel the token.</param>
            <returns>A cancellation token that is canceled after the due time.</returns>
        </member>
        <member name="M:Nito.AsyncEx.CancellationTokenHelpers.Normalize(System.Threading.CancellationToken[])">
            <summary>
            Reduces a set of cancellation tokens by removing any cancellation tokens that cannot be canceled. If any tokens are already canceled, the returned token will be canceled.
            </summary>
            <param name="cancellationTokens">The cancellation tokens to reduce.</param>
        </member>
        <member name="M:Nito.AsyncEx.CancellationTokenHelpers.Normalize(System.Collections.Generic.IEnumerable{System.Threading.CancellationToken})">
            <summary>
            Reduces a set of cancellation tokens by removing any cancellation tokens that cannot be canceled. If any tokens are already canceled, the returned token will be canceled.
            </summary>
            <param name="cancellationTokens">The cancellation tokens to reduce.</param>
        </member>
        <member name="M:Nito.AsyncEx.CancellationTokenHelpers.FromTask(System.Threading.Tasks.Task,System.Threading.Tasks.TaskContinuationOptions)">
            <summary>
            Creates a cancellation token that is canceled when the provided <see cref="T:System.Threading.Tasks.Task"/> completes.
            </summary>
            <param name="source">The task to observe.</param>
            <param name="continuationOptions">The options to use for the task continuation.</param>
        </member>
        <member name="M:Nito.AsyncEx.CancellationTokenHelpers.FromTask(System.Threading.Tasks.Task)">
            <summary>
            Creates a cancellation token that is canceled when the provided <see cref="T:System.Threading.Tasks.Task"/> completes.
            </summary>
            <param name="source">The task to observe.</param>
        </member>
        <member name="P:Nito.AsyncEx.CancellationTokenHelpers.None">
            <summary>
            Gets <see cref="P:System.Threading.CancellationToken.None"/>, a cancellation token that is never canceled.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.CancellationTokenHelpers.Canceled">
            <summary>
            Gets a cancellation token that is already canceled.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.DeferralManager">
            <summary>
            Manages the deferrals for a "command" event that may have asynchonous handlers.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.DeferralManager._count">
            <summary>
            The countdown event, triggered when all deferrals have completed. This is <c>null</c> if there are no deferrals.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.DeferralManager.GetDeferral">
            <summary>
            Gets a deferral. The deferral is complete when disposed.
            </summary>
            <returns>The deferral.</returns>
        </member>
        <member name="M:Nito.AsyncEx.DeferralManager.SignalAndWaitAsync">
            <summary>
            Notifies the manager that all deferrals have been requested, and returns a task that is completed when all deferrals have completed.
            </summary>
            <returns>A task that is completed when all deferrals have completed.</returns>
        </member>
        <member name="T:Nito.AsyncEx.DeferralManager.Deferral">
            <summary>
            A deferral.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.DeferralManager.Deferral._count">
            <summary>
            The countdown event of the deferral manager.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.DeferralManager.Deferral.#ctor(Nito.AsyncEx.AsyncCountdownEvent)">
            <summary>
            Creates a new deferral referencing the countdown event of the deferral manager.
            </summary>
            <param name="count">The countdown event of the deferral manager.</param>
        </member>
        <member name="M:Nito.AsyncEx.DeferralManager.Deferral.System#IDisposable#Dispose">
            <summary>
            Completes the deferral.
            </summary>
        </member>
        <member name="T:Nito.Deque`1">
            <summary>
            A double-ended queue (deque), which provides O(1) indexed access, O(1) removals from the front and back, amortized O(1) insertions to the front and back, and O(N) insertions and removals anywhere else (with the operations getting slower as the index approaches the middle).
            </summary>
            <typeparam name="T">The type of elements contained in the deque.</typeparam>
        </member>
        <member name="F:Nito.Deque`1.DefaultCapacity">
            <summary>
            The default capacity.
            </summary>
        </member>
        <member name="F:Nito.Deque`1.buffer">
            <summary>
            The circular buffer that holds the view.
            </summary>
        </member>
        <member name="F:Nito.Deque`1.offset">
            <summary>
            The offset into <see cref="F:Nito.Deque`1.buffer"/> where the view begins.
            </summary>
        </member>
        <member name="M:Nito.Deque`1.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.Deque`1"/> class with the specified capacity.
            </summary>
            <param name="capacity">The initial capacity. Must be greater than <c>0</c>.</param>
        </member>
        <member name="M:Nito.Deque`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.Deque`1"/> class with the elements from the specified collection.
            </summary>
            <param name="collection">The collection.</param>
        </member>
        <member name="M:Nito.Deque`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.Deque`1"/> class.
            </summary>
        </member>
        <member name="M:Nito.Deque`1.Insert(System.Int32,`0)">
            <summary>
            Inserts an item to this list at the specified index.
            </summary>
            <param name="index">The zero-based index at which <paramref name="item"/> should be inserted.</param>
            <param name="item">The object to insert into this list.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="index"/> is not a valid index in this list.
            </exception>
            <exception cref="T:System.NotSupportedException">
            This list is read-only.
            </exception>
        </member>
        <member name="M:Nito.Deque`1.RemoveAt(System.Int32)">
            <summary>
            Removes the item at the specified index.
            </summary>
            <param name="index">The zero-based index of the item to remove.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="index"/> is not a valid index in this list.
            </exception>
            <exception cref="T:System.NotSupportedException">
            This list is read-only.
            </exception>
        </member>
        <member name="M:Nito.Deque`1.IndexOf(`0)">
            <summary>
            Determines the index of a specific item in this list.
            </summary>
            <param name="item">The object to locate in this list.</param>
            <returns>The index of <paramref name="item"/> if found in this list; otherwise, -1.</returns>
        </member>
        <member name="M:Nito.Deque`1.System#Collections#Generic#ICollection{T}#Add(`0)">
            <summary>
            Adds an item to the end of this list.
            </summary>
            <param name="item">The object to add to this list.</param>
            <exception cref="T:System.NotSupportedException">
            This list is read-only.
            </exception>
        </member>
        <member name="M:Nito.Deque`1.System#Collections#Generic#ICollection{T}#Contains(`0)">
            <summary>
            Determines whether this list contains a specific value.
            </summary>
            <param name="item">The object to locate in this list.</param>
            <returns>
            true if <paramref name="item"/> is found in this list; otherwise, false.
            </returns>
        </member>
        <member name="M:Nito.Deque`1.System#Collections#Generic#ICollection{T}#CopyTo(`0[],System.Int32)">
            <summary>
            Copies the elements of this list to an <see cref="T:System.Array"/>, starting at a particular <see cref="T:System.Array"/> index.
            </summary>
            <param name="array">The one-dimensional <see cref="T:System.Array"/> that is the destination of the elements copied from this slice. The <see cref="T:System.Array"/> must have zero-based indexing.</param>
            <param name="arrayIndex">The zero-based index in <paramref name="array"/> at which copying begins.</param>
            <exception cref="T:System.ArgumentNullException">
            <paramref name="array"/> is null.
            </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">
            <paramref name="arrayIndex"/> is less than 0.
            </exception>
            <exception cref="T:System.ArgumentException">
            <paramref name="arrayIndex"/> is equal to or greater than the length of <paramref name="array"/>.
            -or-
            The number of elements in the source <see cref="T:System.Collections.Generic.ICollection`1"/> is greater than the available space from <paramref name="arrayIndex"/> to the end of the destination <paramref name="array"/>.
            </exception>
        </member>
        <member name="M:Nito.Deque`1.Remove(`0)">
            <summary>
            Removes the first occurrence of a specific object from this list.
            </summary>
            <param name="item">The object to remove from this list.</param>
            <returns>
            true if <paramref name="item"/> was successfully removed from this list; otherwise, false. This method also returns false if <paramref name="item"/> is not found in this list.
            </returns>
            <exception cref="T:System.NotSupportedException">
            This list is read-only.
            </exception>
        </member>
        <member name="M:Nito.Deque`1.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1"/> that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Nito.Deque`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Nito.Deque`1.CheckNewIndexArgument(System.Int32,System.Int32)">
            <summary>
            Checks the <paramref name="index"/> argument to see if it refers to a valid insertion point in a source of a given length.
            </summary>
            <param name="sourceLength">The length of the source. This parameter is not checked for validity.</param>
            <param name="index">The index into the source.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="index"/> is not a valid index to an insertion point for the source.</exception>
        </member>
        <member name="M:Nito.Deque`1.CheckExistingIndexArgument(System.Int32,System.Int32)">
            <summary>
            Checks the <paramref name="index"/> argument to see if it refers to an existing element in a source of a given length.
            </summary>
            <param name="sourceLength">The length of the source. This parameter is not checked for validity.</param>
            <param name="index">The index into the source.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="index"/> is not a valid index to an existing element for the source.</exception>
        </member>
        <member name="M:Nito.Deque`1.CheckRangeArguments(System.Int32,System.Int32,System.Int32)">
            <summary>
            Checks the <paramref name="offset"/> and <paramref name="count"/> arguments for validity when applied to a source of a given length. Allows 0-element ranges, including a 0-element range at the end of the source.
            </summary>
            <param name="sourceLength">The length of the source. This parameter is not checked for validity.</param>
            <param name="offset">The index into source at which the range begins.</param>
            <param name="count">The number of elements in the range.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Either <paramref name="offset"/> or <paramref name="count"/> is less than 0.</exception>
            <exception cref="T:System.ArgumentException">The range [offset, offset + count) is not within the range [0, sourceLength).</exception>
        </member>
        <member name="M:Nito.Deque`1.DequeIndexToBufferIndex(System.Int32)">
            <summary>
            Applies the offset to <paramref name="index"/>, resulting in a buffer index.
            </summary>
            <param name="index">The deque index.</param>
            <returns>The buffer index.</returns>
        </member>
        <member name="M:Nito.Deque`1.DoGetItem(System.Int32)">
            <summary>
            Gets an element at the specified view index.
            </summary>
            <param name="index">The zero-based view index of the element to get. This index is guaranteed to be valid.</param>
            <returns>The element at the specified index.</returns>
        </member>
        <member name="M:Nito.Deque`1.DoSetItem(System.Int32,`0)">
            <summary>
            Sets an element at the specified view index.
            </summary>
            <param name="index">The zero-based view index of the element to get. This index is guaranteed to be valid.</param>
            <param name="item">The element to store in the list.</param>
        </member>
        <member name="M:Nito.Deque`1.DoInsert(System.Int32,`0)">
            <summary>
            Inserts an element at the specified view index.
            </summary>
            <param name="index">The zero-based view index at which the element should be inserted. This index is guaranteed to be valid.</param>
            <param name="item">The element to store in the list.</param>
        </member>
        <member name="M:Nito.Deque`1.DoRemoveAt(System.Int32)">
            <summary>
            Removes an element at the specified view index.
            </summary>
            <param name="index">The zero-based view index of the element to remove. This index is guaranteed to be valid.</param>
        </member>
        <member name="M:Nito.Deque`1.PostIncrement(System.Int32)">
            <summary>
            Increments <see cref="F:Nito.Deque`1.offset"/> by <paramref name="value"/> using modulo-<see cref="P:Nito.Deque`1.Capacity"/> arithmetic.
            </summary>
            <param name="value">The value by which to increase <see cref="F:Nito.Deque`1.offset"/>. May not be negative.</param>
            <returns>The value of <see cref="F:Nito.Deque`1.offset"/> after it was incremented.</returns>
        </member>
        <member name="M:Nito.Deque`1.PreDecrement(System.Int32)">
            <summary>
            Decrements <see cref="F:Nito.Deque`1.offset"/> by <paramref name="value"/> using modulo-<see cref="P:Nito.Deque`1.Capacity"/> arithmetic.
            </summary>
            <param name="value">The value by which to reduce <see cref="F:Nito.Deque`1.offset"/>. May not be negative or greater than <see cref="P:Nito.Deque`1.Capacity"/>.</param>
            <returns>The value of <see cref="F:Nito.Deque`1.offset"/> before it was decremented.</returns>
        </member>
        <member name="M:Nito.Deque`1.DoAddToBack(`0)">
            <summary>
            Inserts a single element to the back of the view. <see cref="P:Nito.Deque`1.IsFull"/> must be false when this method is called.
            </summary>
            <param name="value">The element to insert.</param>
        </member>
        <member name="M:Nito.Deque`1.DoAddToFront(`0)">
            <summary>
            Inserts a single element to the front of the view. <see cref="P:Nito.Deque`1.IsFull"/> must be false when this method is called.
            </summary>
            <param name="value">The element to insert.</param>
        </member>
        <member name="M:Nito.Deque`1.DoRemoveFromBack">
            <summary>
            Removes and returns the last element in the view. <see cref="P:Nito.Deque`1.IsEmpty"/> must be false when this method is called.
            </summary>
            <returns>The former last element.</returns>
        </member>
        <member name="M:Nito.Deque`1.DoRemoveFromFront">
            <summary>
            Removes and returns the first element in the view. <see cref="P:Nito.Deque`1.IsEmpty"/> must be false when this method is called.
            </summary>
            <returns>The former first element.</returns>
        </member>
        <member name="M:Nito.Deque`1.DoInsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0},System.Int32)">
            <summary>
            Inserts a range of elements into the view.
            </summary>
            <param name="index">The index into the view at which the elements are to be inserted.</param>
            <param name="collection">The elements to insert.</param>
            <param name="collectionCount">The number of elements in <paramref name="collection"/>. Must be greater than zero, and the sum of <paramref name="collectionCount"/> and <see cref="P:Nito.Deque`1.Count"/> must be less than or equal to <see cref="P:Nito.Deque`1.Capacity"/>.</param>
        </member>
        <member name="M:Nito.Deque`1.DoRemoveRange(System.Int32,System.Int32)">
            <summary>
            Removes a range of elements from the view.
            </summary>
            <param name="index">The index into the view at which the range begins.</param>
            <param name="collectionCount">The number of elements in the range. This must be greater than 0 and less than or equal to <see cref="P:Nito.Deque`1.Count"/>.</param>
        </member>
        <member name="M:Nito.Deque`1.EnsureCapacityForOneElement">
            <summary>
            Doubles the capacity if necessary to make room for one more element. When this method returns, <see cref="P:Nito.Deque`1.IsFull"/> is false.
            </summary>
        </member>
        <member name="M:Nito.Deque`1.AddToBack(`0)">
            <summary>
            Inserts a single element at the back of this deque.
            </summary>
            <param name="value">The element to insert.</param>
        </member>
        <member name="M:Nito.Deque`1.AddToFront(`0)">
            <summary>
            Inserts a single element at the front of this deque.
            </summary>
            <param name="value">The element to insert.</param>
        </member>
        <member name="M:Nito.Deque`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Inserts a collection of elements into this deque.
            </summary>
            <param name="index">The index at which the collection is inserted.</param>
            <param name="collection">The collection of elements to insert.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="index"/> is not a valid index to an insertion point for the source.</exception>
        </member>
        <member name="M:Nito.Deque`1.RemoveRange(System.Int32,System.Int32)">
            <summary>
            Removes a range of elements from this deque.
            </summary>
            <param name="offset">The index into the deque at which the range begins.</param>
            <param name="count">The number of elements to remove.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Either <paramref name="offset"/> or <paramref name="count"/> is less than 0.</exception>
            <exception cref="T:System.ArgumentException">The range [<paramref name="offset"/>, <paramref name="offset"/> + <paramref name="count"/>) is not within the range [0, <see cref="P:Nito.Deque`1.Count"/>).</exception>
        </member>
        <member name="M:Nito.Deque`1.RemoveFromBack">
            <summary>
            Removes and returns the last element of this deque.
            </summary>
            <returns>The former last element.</returns>
            <exception cref="T:System.InvalidOperationException">The deque is empty.</exception>
        </member>
        <member name="M:Nito.Deque`1.RemoveFromFront">
            <summary>
            Removes and returns the first element of this deque.
            </summary>
            <returns>The former first element.</returns>
            <exception cref="T:System.InvalidOperationException">The deque is empty.</exception>
        </member>
        <member name="M:Nito.Deque`1.Clear">
            <summary>
            Removes all items from this deque.
            </summary>
        </member>
        <member name="P:Nito.Deque`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
            <summary>
            Gets a value indicating whether this list is read-only. This implementation always returns <c>false</c>.
            </summary>
            <returns>true if this list is read-only; otherwise, false.</returns>
        </member>
        <member name="P:Nito.Deque`1.Item(System.Int32)">
            <summary>
            Gets or sets the item at the specified index.
            </summary>
            <param name="index">The index of the item to get or set.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="index"/> is not a valid index in this list.</exception>
            <exception cref="T:System.NotSupportedException">This property is set and the list is read-only.</exception>
        </member>
        <member name="P:Nito.Deque`1.IsEmpty">
            <summary>
            Gets a value indicating whether this instance is empty.
            </summary>
        </member>
        <member name="P:Nito.Deque`1.IsFull">
            <summary>
            Gets a value indicating whether this instance is at full capacity.
            </summary>
        </member>
        <member name="P:Nito.Deque`1.IsSplit">
            <summary>
            Gets a value indicating whether the buffer is "split" (meaning the beginning of the view is at a later index in <see cref="F:Nito.Deque`1.buffer"/> than the end).
            </summary>
        </member>
        <member name="P:Nito.Deque`1.Capacity">
            <summary>
            Gets or sets the capacity for this deque. This value must always be greater than zero, and this property cannot be set to a value less than <see cref="P:Nito.Deque`1.Count"/>.
            </summary>
            <exception cref="T:System.InvalidOperationException"><c>Capacity</c> cannot be set to a value less than <see cref="P:Nito.Deque`1.Count"/>.</exception>
        </member>
        <member name="P:Nito.Deque`1.Count">
            <summary>
            Gets the number of elements contained in this deque.
            </summary>
            <returns>The number of elements contained in this deque.</returns>
        </member>
        <member name="T:Nito.AsyncEx.ExceptionHelpers">
            <summary>
            Provides helper (non-extension) methods dealing with exceptions.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.ExceptionHelpers.PrepareForRethrow(System.Exception)">
            <summary>
            Attempts to prepare the exception for re-throwing by preserving the stack trace. The returned exception should be immediately thrown.
            </summary>
            <param name="exception">The exception. May not be <c>null</c>.</param>
            <returns>The <see cref="T:System.Exception"/> that was passed into this method.</returns>
        </member>
        <member name="T:Nito.AsyncEx.Internal.IdManager`1">
            <summary>
            Allocates Ids for instances on demand. 0 is an invalid/unassigned Id. Ids may be non-unique in very long-running systems. This is similar to the Id system used by <see cref="T:System.Threading.Tasks.Task"/> and <see cref="T:System.Threading.Tasks.TaskScheduler"/>.
            </summary>
            <typeparam name="TTag">The type for which ids are generated.</typeparam>
        </member>
        <member name="F:Nito.AsyncEx.Internal.IdManager`1._lastId">
            <summary>
            The last id generated for this type. This is 0 if no ids have been generated.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.Internal.IdManager`1.GetId(System.Int32@)">
            <summary>
            Returns the id, allocating it if necessary.
            </summary>
            <param name="id">A reference to the field containing the id.</param>
        </member>
        <member name="T:Nito.AsyncEx.NormalizedCancellationToken">
            <summary>
            A <see cref="P:Nito.AsyncEx.NormalizedCancellationToken.Token"/> that may or may not also reference its own <see cref="T:System.Threading.CancellationTokenSource"/>. Instances of this type should always be disposed.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.NormalizedCancellationToken._cts">
            <summary>
            The <see cref="T:System.Threading.CancellationTokenSource"/>, if any. If this is not <c>null</c>, then <see cref="F:Nito.AsyncEx.NormalizedCancellationToken._token"/> is <c>_cts.Token</c>.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.NormalizedCancellationToken._token">
            <summary>
            The <see cref="P:Nito.AsyncEx.NormalizedCancellationToken.Token"/>. If <see cref="F:Nito.AsyncEx.NormalizedCancellationToken._cts"/> is not <c>null</c>, then this is <c>_cts.Token</c>.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.NormalizedCancellationToken.#ctor">
            <summary>
            Creates a normalized cancellation token that can never be canceled.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.NormalizedCancellationToken.#ctor(System.Threading.CancellationTokenSource)">
            <summary>
            Creates a normalized cancellation token from a <see cref="T:System.Threading.CancellationTokenSource"/>. <see cref="P:Nito.AsyncEx.NormalizedCancellationToken.Token"/> is set to the <see cref="P:System.Threading.CancellationTokenSource.Token"/> property of <paramref name="cts"/>.
            </summary>
            <param name="cts">The source for this token.</param>
        </member>
        <member name="M:Nito.AsyncEx.NormalizedCancellationToken.#ctor(System.Threading.CancellationToken)">
            <summary>
            Creates a normalized cancellation token from a <see cref="T:System.Threading.CancellationToken"/>. <see cref="P:Nito.AsyncEx.NormalizedCancellationToken.Token"/> is set to <paramref name="token"/>.
            </summary>
            <param name="token">The source for this token.</param>
        </member>
        <member name="M:Nito.AsyncEx.NormalizedCancellationToken.Dispose">
            <summary>
            Releases any resources used by this normalized cancellation token.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.NormalizedCancellationToken.Token">
            <summary>
            Gets the <see cref="T:System.Threading.CancellationToken"/> for this normalized cancellation token.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.INotifyTaskCompletion">
            <summary>
            Watches a task and raises property-changed notifications when the task completes.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.INotifyTaskCompletion.Task">
            <summary>
            Gets the task being watched. This property never changes and is never <c>null</c>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.INotifyTaskCompletion.TaskCompleted">
            <summary>
            Gets a task that completes successfully when <see cref="P:Nito.AsyncEx.INotifyTaskCompletion.Task"/> completes (successfully, faulted, or canceled). This property never changes and is never <c>null</c>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.INotifyTaskCompletion.Status">
            <summary>
            Gets the current task status. This property raises a notification when the task completes.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.INotifyTaskCompletion.IsCompleted">
            <summary>
            Gets whether the task has completed. This property raises a notification when the value changes to <c>true</c>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.INotifyTaskCompletion.IsNotCompleted">
            <summary>
            Gets whether the task is busy (not completed). This property raises a notification when the value changes to <c>false</c>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.INotifyTaskCompletion.IsSuccessfullyCompleted">
            <summary>
            Gets whether the task has completed successfully. This property raises a notification when the value changes to <c>true</c>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.INotifyTaskCompletion.IsCanceled">
            <summary>
            Gets whether the task has been canceled. This property raises a notification only if the task is canceled (i.e., if the value changes to <c>true</c>).
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.INotifyTaskCompletion.IsFaulted">
            <summary>
            Gets whether the task has faulted. This property raises a notification only if the task faults (i.e., if the value changes to <c>true</c>).
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.INotifyTaskCompletion.Exception">
            <summary>
            Gets the wrapped faulting exception for the task. Returns <c>null</c> if the task is not faulted. This property raises a notification only if the task faults (i.e., if the value changes to non-<c>null</c>).
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.INotifyTaskCompletion.InnerException">
            <summary>
            Gets the original faulting exception for the task. Returns <c>null</c> if the task is not faulted. This property raises a notification only if the task faults (i.e., if the value changes to non-<c>null</c>).
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.INotifyTaskCompletion.ErrorMessage">
            <summary>
            Gets the error message for the original faulting exception for the task. Returns <c>null</c> if the task is not faulted. This property raises a notification only if the task faults (i.e., if the value changes to non-<c>null</c>).
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.INotifyTaskCompletion`1">
            <summary>
            Watches a task and raises property-changed notifications when the task completes.
            </summary>
            <typeparam name="TResult">The type of the task result.</typeparam>
        </member>
        <member name="P:Nito.AsyncEx.INotifyTaskCompletion`1.Task">
            <summary>
            Gets the task being watched. This property never changes and is never <c>null</c>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.INotifyTaskCompletion`1.Result">
            <summary>
            Gets the result of the task. Returns the default value of <typeparamref name="TResult"/> if the task has not completed successfully. This property raises a notification when the task completes successfully.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.NotifyTaskCompletion">
            <summary>
            Factory for task completion notifiers.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.NotifyTaskCompletion.Create(System.Threading.Tasks.Task)">
            <summary>
            Creates a new task notifier watching the specified task.
            </summary>
            <param name="task">The task to watch.</param>
            <returns>A new task notifier watching the specified task.</returns>
        </member>
        <member name="M:Nito.AsyncEx.NotifyTaskCompletion.Create``1(System.Threading.Tasks.Task{``0})">
            <summary>
            Creates a new task notifier watching the specified task.
            </summary>
            <typeparam name="TResult">The type of the task result.</typeparam>
            <param name="task">The task to watch.</param>
            <returns>A new task notifier watching the specified task.</returns>
        </member>
        <member name="M:Nito.AsyncEx.NotifyTaskCompletion.Create(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Executes the specified asynchronous code and creates a new task notifier watching the returned task.
            </summary>
            <param name="asyncAction">The asynchronous code to execute.</param>
            <returns>A new task notifier watching the returned task.</returns>
        </member>
        <member name="M:Nito.AsyncEx.NotifyTaskCompletion.Create``1(System.Func{System.Threading.Tasks.Task{``0}})">
            <summary>
            Executes the specified asynchronous code and creates a new task notifier watching the returned task.
            </summary>
            <param name="asyncAction">The asynchronous code to execute.</param>
            <returns>A new task notifier watching the returned task.</returns>
        </member>
        <member name="T:Nito.AsyncEx.NotifyTaskCompletion.NotifyTaskCompletionImplementation">
            <summary>
            Watches a task and raises property-changed notifications when the task completes.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.NotifyTaskCompletion.NotifyTaskCompletionImplementation.#ctor(System.Threading.Tasks.Task)">
            <summary>
            Initializes a task notifier watching the specified task.
            </summary>
            <param name="task">The task to watch.</param>
        </member>
        <member name="T:Nito.AsyncEx.NotifyTaskCompletion.NotifyTaskCompletionImplementation`1">
            <summary>
            Watches a task and raises property-changed notifications when the task completes.
            </summary>
            <typeparam name="TResult">The type of the task result.</typeparam>
        </member>
        <member name="M:Nito.AsyncEx.NotifyTaskCompletion.NotifyTaskCompletionImplementation`1.#ctor(System.Threading.Tasks.Task{`0})">
            <summary>
            Initializes a task notifier watching the specified task.
            </summary>
            <param name="task">The task to watch.</param>
        </member>
        <member name="T:Nito.AsyncEx.PropertyProgress`1">
            <summary>
            A progress implementation that stores progress updates in a property. If this instance is created on a UI thread, its <see cref="P:Nito.AsyncEx.PropertyProgress`1.Progress"/> property is suitable for data binding.
            </summary>
            <typeparam name="T">The type of progress value.</typeparam>
        </member>
        <member name="F:Nito.AsyncEx.PropertyProgress`1._context">
            <summary>
            The context of the thread that created this instance.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.PropertyProgress`1._progress">
            <summary>
            The last reported progress value.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.PropertyProgress`1.#ctor(`0)">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.PropertyProgress`1"/> class.
            </summary>
            <param name="initialProgress">The initial progress value.</param>
        </member>
        <member name="M:Nito.AsyncEx.PropertyProgress`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.PropertyProgress`1"/> class.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.PropertyProgress`1.Progress">
            <summary>
            The last reported progress value.
            </summary>
        </member>
        <member name="E:Nito.AsyncEx.PropertyProgress`1.PropertyChanged">
            <summary>
            Occurs when the property value changes.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.SynchronizationContextHelpers">
            <summary>
            Provides helper types for <see cref="T:System.Threading.SynchronizationContext"/>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.SynchronizationContextHelpers.CurrentOrDefault">
            <summary>
            Retrieves the current synchronization context, or the default synchronization context if there is no current synchronization context.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.SynchronizationContextHelpers.SynchronizationContextSwitcher">
            <summary>
            Utility class for temporarily switching <see cref="T:System.Threading.SynchronizationContext"/> implementations.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.SynchronizationContextHelpers.SynchronizationContextSwitcher._oldContext">
            <summary>
            The previous <see cref="T:System.Threading.SynchronizationContext"/>.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.SynchronizationContextHelpers.SynchronizationContextSwitcher._disposed">
            <summary>
            Whether this object has already been disposed.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.SynchronizationContextHelpers.SynchronizationContextSwitcher.#ctor(System.Threading.SynchronizationContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.SynchronizationContextHelpers.SynchronizationContextSwitcher"/> class, installing the new <see cref="T:System.Threading.SynchronizationContext"/>.
            </summary>
            <param name="newContext">The new <see cref="T:System.Threading.SynchronizationContext"/>.</param>
        </member>
        <member name="M:Nito.AsyncEx.SynchronizationContextHelpers.SynchronizationContextSwitcher.System#IDisposable#Dispose">
            <summary>
            Restores the old <see cref="T:System.Threading.SynchronizationContext"/>.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.Synchronous.TaskExtensions">
            <summary>
            Provides synchronous extension methods for tasks.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.Synchronous.TaskExtensions.WaitAndUnwrapException(System.Threading.Tasks.Task)">
            <summary>
            Waits for the task to complete, unwrapping any exceptions.
            </summary>
            <param name="task">The task. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.Synchronous.TaskExtensions.WaitAndUnwrapException(System.Threading.Tasks.Task,System.Threading.CancellationToken)">
            <summary>
            Waits for the task to complete, unwrapping any exceptions.
            </summary>
            <param name="task">The task. May not be <c>null</c>.</param>
            <param name="cancellationToken">A cancellation token to observe while waiting for the task to complete.</param>
            <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken"/> was cancelled before the <paramref name="task"/> completed, or the <paramref name="task"/> raised an <see cref="T:System.OperationCanceledException"/>.</exception>
        </member>
        <member name="M:Nito.AsyncEx.Synchronous.TaskExtensions.WaitAndUnwrapException``1(System.Threading.Tasks.Task{``0})">
            <summary>
            Waits for the task to complete, unwrapping any exceptions.
            </summary>
            <typeparam name="TResult">The type of the result of the task.</typeparam>
            <param name="task">The task. May not be <c>null</c>.</param>
            <returns>The result of the task.</returns>
        </member>
        <member name="M:Nito.AsyncEx.Synchronous.TaskExtensions.WaitAndUnwrapException``1(System.Threading.Tasks.Task{``0},System.Threading.CancellationToken)">
            <summary>
            Waits for the task to complete, unwrapping any exceptions.
            </summary>
            <typeparam name="TResult">The type of the result of the task.</typeparam>
            <param name="task">The task. May not be <c>null</c>.</param>
            <param name="cancellationToken">A cancellation token to observe while waiting for the task to complete.</param>
            <returns>The result of the task.</returns>
            <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken"/> was cancelled before the <paramref name="task"/> completed, or the <paramref name="task"/> raised an <see cref="T:System.OperationCanceledException"/>.</exception>
        </member>
        <member name="M:Nito.AsyncEx.Synchronous.TaskExtensions.WaitWithoutException(System.Threading.Tasks.Task)">
            <summary>
            Waits for the task to complete, but does not raise task exceptions. The task exception (if any) is unobserved.
            </summary>
            <param name="task">The task. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.Synchronous.TaskExtensions.WaitWithoutException(System.Threading.Tasks.Task,System.Threading.CancellationToken)">
            <summary>
            Waits for the task to complete, but does not raise task exceptions. The task exception (if any) is unobserved.
            </summary>
            <param name="task">The task. May not be <c>null</c>.</param>
            <param name="cancellationToken">A cancellation token to observe while waiting for the task to complete.</param>
            <exception cref="T:System.OperationCanceledException">The <paramref name="cancellationToken"/> was cancelled before the <paramref name="task"/> completed.</exception>
        </member>
        <member name="T:Nito.AsyncEx.TaskCompletionSource">
            <summary>
            Represents the producer side of a <see cref="T:System.Threading.Tasks.Task"/> unbound to a delegate, providing access to the consumer side through the <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> property.
            </summary>
        </member>
        <member name="F:Nito.AsyncEx.TaskCompletionSource._tcs">
            <summary>
            The underlying TCS.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSource.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.TaskCompletionSource"/> class.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSource.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.TaskCompletionSource"/> class with the specified state.
            </summary>
            <param name="state">The state to use as the underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/>'s <see cref="P:System.IAsyncResult.AsyncState"/>.</param>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSource.#ctor(System.Threading.Tasks.TaskCreationOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.TaskCompletionSource"/> class with the specified options.
            </summary>
            <param name="creationOptions">The options to use when creating the underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/>.</param>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSource.#ctor(System.Object,System.Threading.Tasks.TaskCreationOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Nito.AsyncEx.TaskCompletionSource"/> class with the specified state and options.
            </summary>
            <param name="state">The state to use as the underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/>'s <see cref="P:System.IAsyncResult.AsyncState"/>.</param>
            <param name="creationOptions">The options to use when creating the underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/>.</param>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSource.SetCanceled">
            <summary>
            Transitions the underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> into the <see cref="F:System.Threading.Tasks.TaskStatus.Canceled"/> state.
            </summary>
            <exception cref="T:System.InvalidOperationException">The underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> has already been completed.</exception>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSource.TrySetCanceled">
            <summary>
            Attempts to transition the underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> into the <see cref="F:System.Threading.Tasks.TaskStatus.Canceled"/> state.
            </summary>
            <returns><c>true</c> if the operation was successful; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSource.SetException(System.Exception)">
            <summary>
            Transitions the underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> into the <see cref="F:System.Threading.Tasks.TaskStatus.Faulted"/> state.
            </summary>
            <param name="exception">The exception to bind to this <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/>. May not be <c>null</c>.</param>
            <exception cref="T:System.InvalidOperationException">The underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> has already been completed.</exception>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSource.SetException(System.Collections.Generic.IEnumerable{System.Exception})">
            <summary>
            Transitions the underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> into the <see cref="F:System.Threading.Tasks.TaskStatus.Faulted"/> state.
            </summary>
            <param name="exceptions">The collection of exceptions to bind to this <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/>. May not be <c>null</c> or contain <c>null</c> elements.</param>
            <exception cref="T:System.InvalidOperationException">The underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> has already been completed.</exception>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSource.TrySetException(System.Exception)">
            <summary>
            Attempts to transition the underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> into the <see cref="F:System.Threading.Tasks.TaskStatus.Faulted"/> state.
            </summary>
            <param name="exception">The exception to bind to this <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/>. May not be <c>null</c>.</param>
            <returns><c>true</c> if the operation was successful; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSource.TrySetException(System.Collections.Generic.IEnumerable{System.Exception})">
            <summary>
            Attempts to transition the underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> into the <see cref="F:System.Threading.Tasks.TaskStatus.Faulted"/> state.
            </summary>
            <param name="exceptions">The collection of exceptions to bind to this <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/>. May not be <c>null</c> or contain <c>null</c> elements.</param>
            <returns><c>true</c> if the operation was successful; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSource.SetResult">
            <summary>
            Transitions the underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> into the <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion"/> state.
            </summary>
            <exception cref="T:System.InvalidOperationException">The underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> has already been completed.</exception>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSource.TrySetResult">
            <summary>
            Attempts to transition the underlying <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> into the <see cref="F:System.Threading.Tasks.TaskStatus.RanToCompletion"/> state.
            </summary>
            <returns><c>true</c> if the operation was successful; otherwise, <c>false</c>.</returns>
        </member>
        <member name="P:Nito.AsyncEx.TaskCompletionSource.Task">
            <summary>
            Gets the <see cref="P:Nito.AsyncEx.TaskCompletionSource.Task"/> created by this <see cref="T:Nito.AsyncEx.TaskCompletionSource"/>.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.TaskCompletionSourceExtensions">
            <summary>
            Provides extension methods for <see cref="T:System.Threading.Tasks.TaskCompletionSource`1"/>.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSourceExtensions.TryCompleteFromCompletedTask``2(System.Threading.Tasks.TaskCompletionSource{``0},System.Threading.Tasks.Task{``1})">
            <summary>
            Attempts to complete a <see cref="T:System.Threading.Tasks.TaskCompletionSource`1"/>, propagating the completion of <paramref name="task"/>.
            </summary>
            <typeparam name="TResult">The type of the result of the target asynchronous operation.</typeparam>
            <typeparam name="TSourceResult">The type of the result of the source asynchronous operation.</typeparam>
            <param name="this">The task completion source. May not be <c>null</c>.</param>
            <param name="task">The task. May not be <c>null</c>.</param>
            <returns><c>true</c> if this method completed the task completion source; <c>false</c> if it was already completed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSourceExtensions.TryCompleteFromEventArgs``1(System.Threading.Tasks.TaskCompletionSource{``0},System.ComponentModel.AsyncCompletedEventArgs,System.Func{``0})">
            <summary>
            Attempts to complete a <see cref="T:System.Threading.Tasks.TaskCompletionSource`1"/>, propagating the completion of <paramref name="eventArgs"/>.
            </summary>
            <typeparam name="TResult">The type of the result of the asynchronous operation.</typeparam>
            <param name="this">The task completion source. May not be <c>null</c>.</param>
            <param name="eventArgs">The event arguments passed to the completion event. May not be <c>null</c>.</param>
            <param name="getResult">The delegate used to retrieve the result. This is only invoked if <paramref name="eventArgs"/> indicates successful completion. May not be <c>null</c>.</param>
            <returns><c>true</c> if this method completed the task completion source; <c>false</c> if it was already completed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSourceExtensions.TryCompleteFromCompletedTask(Nito.AsyncEx.TaskCompletionSource,System.Threading.Tasks.Task)">
            <summary>
            Attempts to complete a <see cref="T:Nito.AsyncEx.TaskCompletionSource"/>, propagating the completion of <paramref name="task"/>.
            </summary>
            <param name="this">The task completion source. May not be <c>null</c>.</param>
            <param name="task">The task. May not be <c>null</c>.</param>
            <returns><c>true</c> if this method completed the task completion source; <c>false</c> if it was already completed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSourceExtensions.TryCompleteFromEventArgs(Nito.AsyncEx.TaskCompletionSource,System.ComponentModel.AsyncCompletedEventArgs)">
            <summary>
            Attempts to complete a <see cref="T:Nito.AsyncEx.TaskCompletionSource"/>, propagating the completion of <paramref name="eventArgs"/>.
            </summary>
            <param name="this">The task completion source. May not be <c>null</c>.</param>
            <param name="eventArgs">The event arguments passed to the completion event. May not be <c>null</c>.</param>
            <returns><c>true</c> if this method completed the task completion source; <c>false</c> if it was already completed.</returns>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSourceExtensions.TrySetResultWithBackgroundContinuations``1(System.Threading.Tasks.TaskCompletionSource{``0},``0)">
            <summary>
            Attempts to complete a <see cref="T:System.Threading.Tasks.TaskCompletionSource`1"/> with the specified value, forcing all continuations onto a threadpool thread even if they specified <c>ExecuteSynchronously</c>.
            </summary>
            <typeparam name="TResult">The type of the result of the asynchronous operation.</typeparam>
            <param name="this">The task completion source. May not be <c>null</c>.</param>
            <param name="result">The result of the asynchronous operation.</param>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSourceExtensions.TrySetResultWithBackgroundContinuations(Nito.AsyncEx.TaskCompletionSource)">
            <summary>
            Attempts to complete a <see cref="T:Nito.AsyncEx.TaskCompletionSource"/>, forcing all continuations onto a threadpool thread even if they specified <c>ExecuteSynchronously</c>.
            </summary>
            <param name="this">The task completion source. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSourceExtensions.TrySetCanceledWithBackgroundContinuations``1(System.Threading.Tasks.TaskCompletionSource{``0})">
            <summary>
            Attempts to complete a <see cref="T:System.Threading.Tasks.TaskCompletionSource`1"/> as canceled, forcing all continuations onto a threadpool thread even if they specified <c>ExecuteSynchronously</c>.
            </summary>
            <typeparam name="TResult">The type of the result of the asynchronous operation.</typeparam>
            <param name="this">The task completion source. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSourceExtensions.TrySetCanceledWithBackgroundContinuations(Nito.AsyncEx.TaskCompletionSource)">
            <summary>
            Attempts to complete a <see cref="T:Nito.AsyncEx.TaskCompletionSource"/> as canceled, forcing all continuations onto a threadpool thread even if they specified <c>ExecuteSynchronously</c>.
            </summary>
            <param name="this">The task completion source. May not be <c>null</c>.</param>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSourceExtensions.TrySetExceptionWithBackgroundContinuations``1(System.Threading.Tasks.TaskCompletionSource{``0},System.Exception)">
            <summary>
            Attempts to complete a <see cref="T:System.Threading.Tasks.TaskCompletionSource`1"/> as faulted, forcing all continuations onto a threadpool thread even if they specified <c>ExecuteSynchronously</c>.
            </summary>
            <typeparam name="TResult">The type of the result of the asynchronous operation.</typeparam>
            <param name="this">The task completion source. May not be <c>null</c>.</param>
            <param name="exception">The exception to bind to the task.</param>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSourceExtensions.TrySetExceptionWithBackgroundContinuations(Nito.AsyncEx.TaskCompletionSource,System.Exception)">
            <summary>
            Attempts to complete a <see cref="T:Nito.AsyncEx.TaskCompletionSource"/> as faulted, forcing all continuations onto a threadpool thread even if they specified <c>ExecuteSynchronously</c>.
            </summary>
            <param name="this">The task completion source. May not be <c>null</c>.</param>
            <param name="exception">The exception to bind to the task.</param>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSourceExtensions.TrySetExceptionWithBackgroundContinuations``1(System.Threading.Tasks.TaskCompletionSource{``0},System.Collections.Generic.IEnumerable{System.Exception})">
            <summary>
            Attempts to complete a <see cref="T:System.Threading.Tasks.TaskCompletionSource`1"/> as faulted, forcing all continuations onto a threadpool thread even if they specified <c>ExecuteSynchronously</c>.
            </summary>
            <typeparam name="TResult">The type of the result of the asynchronous operation.</typeparam>
            <param name="this">The task completion source. May not be <c>null</c>.</param>
            <param name="exceptions">The exceptions to bind to the task.</param>
        </member>
        <member name="M:Nito.AsyncEx.TaskCompletionSourceExtensions.TrySetExceptionWithBackgroundContinuations(Nito.AsyncEx.TaskCompletionSource,System.Collections.Generic.IEnumerable{System.Exception})">
            <summary>
            Attempts to complete a <see cref="T:Nito.AsyncEx.TaskCompletionSource"/> as faulted, forcing all continuations onto a threadpool thread even if they specified <c>ExecuteSynchronously</c>.
            </summary>
            <param name="this">The task completion source. May not be <c>null</c>.</param>
            <param name="exceptions">The exceptions to bind to the task.</param>
        </member>
        <member name="T:Nito.AsyncEx.TaskConstants">
            <summary>
            Provides completed task constants.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.TaskConstants.BooleanTrue">
            <summary>
            A task that has been completed with the value <c>true</c>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.TaskConstants.BooleanFalse">
            <summary>
            A task that has been completed with the value <c>false</c>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.TaskConstants.Int32Zero">
            <summary>
            A task that has been completed with the value <c>0</c>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.TaskConstants.Int32NegativeOne">
            <summary>
            A task that has been completed with the value <c>-1</c>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.TaskConstants.Completed">
            <summary>
            A <see cref="T:System.Threading.Tasks.Task"/> that has been completed.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.TaskConstants.Never">
            <summary>
            A <see cref="T:System.Threading.Tasks.Task"/> that will never complete.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.TaskConstants.Canceled">
            <summary>
            A task that has been canceled.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.TaskConstants`1">
            <summary>
            Provides completed task constants.
            </summary>
            <typeparam name="T">The type of the task result.</typeparam>
        </member>
        <member name="P:Nito.AsyncEx.TaskConstants`1.Default">
            <summary>
            A task that has been completed with the default value of <typeparamref name="T"/>.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.TaskConstants`1.Never">
            <summary>
            A <see cref="T:System.Threading.Tasks.Task"/> that will never complete.
            </summary>
        </member>
        <member name="P:Nito.AsyncEx.TaskConstants`1.Canceled">
            <summary>
            A task that has been canceled.
            </summary>
        </member>
        <member name="T:Nito.AsyncEx.TaskExtensions">
            <summary>
            Provides extension methods for tasks.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.TaskExtensions.OrderByCompletion``1(System.Collections.Generic.IEnumerable{System.Threading.Tasks.Task{``0}})">
            <summary>
            Creates a new array of tasks which complete in order.
            </summary>
            <typeparam name="T">The type of the results of the tasks.</typeparam>
            <param name="tasks">The tasks to order by completion.</param>
        </member>
        <member name="T:Nito.AsyncEx.TaskFactoryExtensions">
            <summary>
            Provides extension methods for task factories.
            </summary>
        </member>
        <member name="M:Nito.AsyncEx.TaskFactoryExtensions.Run(System.Threading.Tasks.TaskFactory,System.Action)">
            <summary>
            Queues work to the task factory and returns a <see cref="T:System.Threading.Tasks.Task"/> representing that work.
            </summary>
            <param name="this">The <see cref="T:System.Threading.Tasks.TaskFactory"/>. May not be <c>null</c>.</param>
            <param name="action">The action delegate to execute. May not be <c>null</c>.</param>
            <returns>The started task.</returns>
        </member>
        <member name="M:Nito.AsyncEx.TaskFactoryExtensions.Run``1(System.Threading.Tasks.TaskFactory,System.Func{``0})">
            <summary>
            Queues work to the task factory and returns a <see cref="T:System.Threading.Tasks.Task`1"/> representing that work.
            </summary>
            <param name="this">The <see cref="T:System.Threading.Tasks.TaskFactory"/>. May not be <c>null</c>.</param>
            <param name="action">The action delegate to execute. May not be <c>null</c>.</param>
            <returns>The started task.</returns>
        </member>
        <member name="M:Nito.AsyncEx.TaskFactoryExtensions.Run(System.Threading.Tasks.TaskFactory,System.Func{System.Threading.Tasks.Task})">
            <summary>
            Queues work to the task factory and returns a proxy <see cref="T:System.Threading.Tasks.Task"/> representing that work.
            </summary>
            <param name="this">The <see cref="T:System.Threading.Tasks.TaskFactory"/>. May not be <c>null</c>.</param>
            <param name="action">The action delegate to execute. May not be <c>null</c>.</param>
            <returns>The started task.</returns>
        </member>
        <member name="M:Nito.AsyncEx.TaskFactoryExtensions.Run``1(System.Threading.Tasks.TaskFactory,System.Func{System.Threading.Tasks.Task{``0}})">
            <summary>
            Queues work to the task factory and returns a proxy <see cref="T:System.Threading.Tasks.Task`1"/> representing that work.
            </summary>
            <param name="this">The <see cref="T:System.Threading.Tasks.TaskFactory"/>. May not be <c>null</c>.</param>
            <param name="action">The action delegate to execute. May not be <c>null</c>.</param>
            <returns>The started task.</returns>
        </member>
    </members>
</doc>
