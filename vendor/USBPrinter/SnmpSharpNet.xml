<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SnmpSharpNet</name>
    </assembly>
    <members>
        <member name="T:SnmpSharpNet.AgentParameters">
            <summary>
            SNMP Agent specific values.
            </summary>
            <remarks>
            This class stores values to access SNMP version 1 and version 2
            agents.
            
            Pass this class with your request data (Pdu) to the request method of the target class to make
            a request.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.AgentParameters._version">
            <summary>
            Agent protocol version
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AgentParameters._community">
            <summary>
            SNMP community name for SNMP v1 and v2 protocol versions
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AgentParameters._disableReplySourceCheck">
            <summary>
            Flag that disables checking of host IP address and port number from which reply is received. If not disabled, only
            replies from the host IP/port to which request was sent will be considered valid and all others will be ignored.
            
            Default value is: false (reply source check is enabled)
            
            Set to true if you wish to disable this check.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.AgentParameters.#ctor">
            <summary>
            Standard constructor
            </summary>
        </member>
        <member name="M:SnmpSharpNet.AgentParameters.#ctor(SnmpSharpNet.AgentParameters)">
            <summary>
            Copy constructor. Initialize the class with the values of the parameter class values.
            </summary>
            <param name="second">Parameter class.</param>
        </member>
        <member name="M:SnmpSharpNet.AgentParameters.#ctor(SnmpSharpNet.SnmpVersion)">
            <summary>
            Constructor
            </summary>
            <param name="version">SNMP protocol version. Acceptable values are SnmpConstants.SNMPV1 and
            SnmpConstants.SNMPV2</param>
        </member>
        <member name="M:SnmpSharpNet.AgentParameters.#ctor(SnmpSharpNet.OctetString)">
            <summary>
            Constructor
            </summary>
            <param name="community">Agent SNMP community name</param>
        </member>
        <member name="M:SnmpSharpNet.AgentParameters.#ctor(SnmpSharpNet.SnmpVersion,SnmpSharpNet.OctetString)">
            <summary>
            Constructor
            </summary>
            <param name="version">SNMP Protocol version</param>
            <param name="community">SNMP community name</param>
        </member>
        <member name="M:SnmpSharpNet.AgentParameters.#ctor(SnmpSharpNet.SnmpVersion,SnmpSharpNet.OctetString,System.Boolean)">
            <summary>
            Constructor
            </summary>
            <param name="version">SNMP Protocol version</param>
            <param name="community">SNMP community name</param>
            <param name="disableReplySourceCheck">Should reply source IP address/port number be checked on reply reception</param>
        </member>
        <member name="P:SnmpSharpNet.AgentParameters.Version">
            <summary>
            Get/Set SNMP protocol version.
            </summary>
            <exception cref="T:SnmpSharpNet.SnmpInvalidVersionException">Thrown when attempting to set protocol version
            other then version 1 or 2c</exception>
        </member>
        <member name="M:SnmpSharpNet.AgentParameters.GetVersion">
            <summary>
            Return SNMP version Integer32 object
            </summary>
            <returns>Integer32 object</returns>
        </member>
        <member name="P:SnmpSharpNet.AgentParameters.Community">
            <summary>
            Get SNMP version 1 or 2 community name object
            </summary>
        </member>
        <member name="P:SnmpSharpNet.AgentParameters.DisableReplySourceCheck">
            <summary>
            Get/Set flag that disables checking of host IP address and port number from which reply is received. If not disabled, only
            replies from the host IP/port to which request was sent will be considered valid and all others will be ignored.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.AgentParameters.Valid">
            <summary>
            Validate object.
            </summary>
            <returns>true if object is valid, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.AgentParameters.InitializePacket(SnmpSharpNet.SnmpPacket)">
            <summary>
            Initialize SNMP packet class with agent parameters. In this class, SNMP community name is
            set in SNMPv1 and SNMPv2 packets.
            </summary>
            <param name="packet">Packet class to initialize</param>
        </member>
        <member name="M:SnmpSharpNet.AgentParameters.Clone">
            <summary>
            Clone current object
            </summary>
            <returns>Duplicate object initialized with values from this class.</returns>
        </member>
        <member name="T:SnmpSharpNet.AsnType">
            <summary>
            Base class for all ASN.1 value classes
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.BOOLEAN">
            <summary>Bool true/false value type</summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.INTEGER">
            <summary>Signed 32-bit integer type</summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.BITSTRING">
            <summary>Bit sequence type</summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.OCTETSTRING">
            <summary>Octet (byte) value type</summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.NULL">
            <summary>Null (no value) type</summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.OBJECTID">
            <summary>Object id type</summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.SEQUENCE">
            <summary>Arbitrary data type</summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.SET">
            <summary> Defined by referencing a fixed, unordered list of types,
            some of which may be declared optional. Each value is an
            unordered list of values, one from each component type.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.UNIVERSAL">
            <summary> Generally useful, application-independent types and
            construction mechanisms.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.APPLICATION">
            <summary> Relevant to a particular application. These are defined
            in standards other than ASN.1.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.CONTEXT">
            <summary> Also relevant to a particular application, but limited by context
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.PRIVATE">
            <summary> These are types not covered by any standard but instead defined by users.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.PRIMITIVE">
            <summary> A primitive data object.</summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.CONSTRUCTOR">
            <summary> A constructed data object such as a set or sequence.</summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.HIGH_BIT">
            <summary> Defines the "high bit" that is the sign extension bit for a 8-bit signed value.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType.EXTENSION_ID">
            <summary> Defines the BER extension "value" that is used to mark an extension type.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsnType._asnType">
            <summary>
            ASN.1 type byte.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.AsnType.Type">
            <summary>
            Get ASN.1 value type stored in this class.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.AsnType.encode(SnmpSharpNet.MutableByte)">
            <summary>
            Encodes the data object in the specified buffer
            </summary>
            <param name="buffer">The buffer to write the encoded information</param>
        </member>
        <member name="M:SnmpSharpNet.AsnType.decode(System.Byte[],System.Int32)">
            <summary>
            Decodes the ASN.1 buffer and sets the values in the AsnType object.
            </summary>
            <param name="buffer">The encoded data buffer</param>
            <param name="offset">The offset of the first valid byte.</param>
            <returns>New offset pointing to the byte after the last decoded position
            </returns>
        </member>
        <member name="M:SnmpSharpNet.AsnType.BuildLength(SnmpSharpNet.MutableByte,System.Int32)">
            <summary>
            Append BER encoded length to the <see cref="T:SnmpSharpNet.MutableByte"/>
            </summary>
            <param name="mb">MutableArray to append BER encoded length to</param>
            <param name="asnLength">Length value to encode.</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when length value to encode is less then 0</exception>
        </member>
        <member name="M:SnmpSharpNet.AsnType.ParseLength(System.Byte[],System.Int32@)">
            <summary>
            MutableByte version of ParseLength. Retrieve BER encoded length from a byte array at supplied offset
            </summary>
            <param name="mb">BER encoded data</param>
            <param name="offset">Offset to start parsing length from</param>
            <returns>Length value</returns>
            <exception cref="T:System.OverflowException">Thrown when buffer is too short</exception>
        </member>
        <member name="M:SnmpSharpNet.AsnType.BuildHeader(SnmpSharpNet.MutableByte,System.Byte,System.Int32)">
            <summary>
            Build ASN.1 header in the MutableByte array.
            </summary>
            <remarks>
            Header is the TL part of the TLV (type, length, value) BER encoded data representation.
            
            Each value is encoded as a Type byte, length of the data field and the actual, encoded
            data. This method will encode the type and length fields.
            </remarks>
            <param name="mb">MurableByte array</param>
            <param name="asnType">ASN.1 header type</param>
            <param name="asnLength">Length of the data contained in the header</param>
        </member>
        <member name="M:SnmpSharpNet.AsnType.ParseHeader(System.Byte[],System.Int32@,System.Int32@)">
            <summary>
            Parse ASN.1 header.
            </summary>
            <param name="mb">BER encoded data</param>
            <param name="offset">Offset in the packet to start parsing the header from</param>
            <param name="length">Length of the data in the section starting with parsed header</param>
            <returns>ASN.1 type of the header</returns>
            <exception cref="T:System.OverflowException">Thrown when buffer is too short</exception>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown when invalid type is encountered in the header</exception>
        </member>
        <member name="M:SnmpSharpNet.AsnType.Clone">
            <summary>
            Abstract Clone() member function
            </summary>
            <returns>Duplicated current object cast as Object</returns>
        </member>
        <member name="T:SnmpSharpNet.AsyncRequestResult">
            <summary>
            Result codes sent by UdpTarget class to the SnmpAsyncCallback delegate.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestResult.NoError">
            <summary>
            No error. Data was received from the socket.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestResult.RequestInProgress">
            <summary>
            Request is in progress. A new request can not be initiated until previous request completes.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestResult.Timeout">
            <summary>
            Request has timed out. Maximum number of retries has been reached without receiving a reply
            from the peer request was sent to
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestResult.SocketSendError">
            <summary>
            An error was encountered when attempting to send data to the peer. Request failed.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestResult.SocketReceiveError">
            <summary>
            An error was encountered when attempting to receive data from the peer. Request failed.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestResult.Terminated">
            <summary>
            Request has been terminated by the user.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestResult.NoDataReceived">
            <summary>
            No data was received from the peer
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestResult.AuthenticationError">
            <summary>
            Authentication error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestResult.PrivacyError">
            <summary>
            Privacy error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestResult.EncodeError">
            <summary>
            Error encoding SNMP packet
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestResult.DecodeError">
            <summary>
            Error decoding SNMP packet
            </summary>
        </member>
        <member name="T:SnmpSharpNet.AsyncRequestState">
            <summary>
            Internal class holding relevant information for async requests.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestState._endPoint">
            <summary>
            Peer end point
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestState._packet">
            <summary>
            Packet
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestState._packetLength">
            <summary>
            Packet length
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestState._maxRetries">
            <summary>
            Maximum number of retries (0 = single request, no retries)
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestState._timeout">
            <summary>
            Request timeout in milliseconds
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestState._timer">
            <summary>
            Timer class
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AsyncRequestState._currentRetry">
            <summary>
            Current retry count. Value represents the number of retries that have been sent
            excluding the original request.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.AsyncRequestState.EndPoint">
            <summary>
            Get/Set end point
            </summary>
        </member>
        <member name="P:SnmpSharpNet.AsyncRequestState.Packet">
            <summary>
            Get/Set packet buffer
            </summary>
        </member>
        <member name="P:SnmpSharpNet.AsyncRequestState.PacketLength">
            <summary>
            Get/Set packet length value
            </summary>
        </member>
        <member name="P:SnmpSharpNet.AsyncRequestState.MaxRetries">
            <summary>
            Get/Set maximum retry value
            </summary>
        </member>
        <member name="P:SnmpSharpNet.AsyncRequestState.Timeout">
            <summary>
            Get/Set timeout value
            </summary>
        </member>
        <member name="P:SnmpSharpNet.AsyncRequestState.Timer">
            <summary>
            Get/Set timer class
            </summary>
        </member>
        <member name="P:SnmpSharpNet.AsyncRequestState.CurrentRetry">
            <summary>
            Get/Set current retry count
            </summary>
        </member>
        <member name="M:SnmpSharpNet.AsyncRequestState.#ctor(System.Net.IPAddress,System.Int32,System.Int32,System.Int32)">
            <summary>
            Constructor.
            </summary>
            <param name="peerIP">Peer IP address</param>
            <param name="peerPort">Peer UDP port number</param>
            <param name="maxretries">Maximum number of retries</param>
            <param name="timeout">Timeout value in milliseconds</param>
        </member>
        <member name="T:SnmpSharpNet.PduErrorStatus">
            <summary>
            Pdu and ScopedPdu error status value enumeration
            </summary>
            <remarks><NAME_EMAIL></remarks>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.noError">
            <summary>
            No error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.tooBig">
            <summary>
            request or reply is too big
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.noSuchName">
            <summary>
            requested name doesn't exist
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.badValue">
            <summary>
            bad value supplied
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.readOnly">
            <summary>
            Oid is read only
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.genErr">
            <summary>
            general error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.noAccess">
            <summary>
            access denied
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.wrongType">
            <summary>
            wrong type
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.wrongLength">
            <summary>
            wrong length
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.wrongEncoding">
            <summary>
            wrong encoding
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.wrongValue">
            <summary>
            wrong value
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.noCreation">
            <summary>
            no creation
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.inconsistentValue">
            <summary>
            inconsistent value
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.resourceUnavailable">
            <summary>
            resource is not available
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.commitFailed">
            <summary>
            commit failed
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.undoFailed">
            <summary>
            undo failed
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.authorizationError">
            <summary>
            authorization error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.notWritable">
            <summary>
            not writable
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduErrorStatus.inconsistentName">
            <summary>
            inconsistent name
            </summary>
        </member>
        <member name="T:SnmpSharpNet.PduType">
            <summary>
            Enumeration of available Protocol Data Unit types
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduType.Get">
            <summary>
            SNMP Get request PDU type
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduType.GetNext">
            <summary>
            SNMP GetNext request PDU type
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduType.Response">
            <summary>
            SNMP Response PDU type
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduType.Set">
            <summary>
            SNMP Set request PDU type
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduType.Trap">
            <summary>
            SNMP Trap notification PDU type
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduType.GetBulk">
            <summary>
            SNMP GetBulk request PDU type
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduType.Inform">
            <summary>
            SNMP Inform notification PDU type
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduType.V2Trap">
            <summary>
            SNMP version 2 Trap notification PDU type
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PduType.Report">
            <summary>
            SNMP version 3 Report notification PDU type
            </summary>
        </member>
        <member name="T:SnmpSharpNet.Privacy3DES">
            <summary>TripleDES privacy protocol implementation class.</summary>
            <remarks>
            TripleDES privacy implementation is based on the Internet Draft proposal to the
            SNMPv3 Working Group titled: Extension to the User-Based Security Model (USM) to Support 
            Triple-DES EDE in "Outside" CBC Mode
            
            High level, TripleDES privacy in SNMPv3 uses DES-EDE. What this means is that a key is generated
            that is 24 bytes long. This key is split into 3 * 8 byte keys suitable for use with DES. Keys
            are then used to perform ecryption, decryption and another encryption using DES. Additionally, each
            block is XORed with the previous block of encrypted data, or if working on the first block, IV value.
            
            For details see draft-reeder-snmpv3-usm-3desede-00.txt.
            
            Important: TripleDES privacy protocol is not based on a standard. This extension to the USM standard has
            been proposed and has expired without approval or move to the standards track. Some vendors have implemented
            this privacy protocol and for the completeness of the library, it has been included in SnmpSharpNet.
            
            Troubleshooting of TripleDES encryption is difficult because of the low availability so if you find problems
            with the SnmpSharpNet implementation, please try to provide me with as much detail, both about your code and
            the type/version/mode of the agent you are working with.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.Privacy3DES._salt">
            <summary>
            Internal salt value
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Privacy3DES.#ctor">
            <summary>
            Standard constructor.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Privacy3DES.NextSalt">
            <summary>
            Returns next salt value.
            </summary>
            <returns>32-bit integer salt value in network byte order (big endian)</returns>
        </member>
        <member name="M:SnmpSharpNet.Privacy3DES.Encrypt(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32,System.Byte[]@,SnmpSharpNet.IAuthenticationDigest)">
            <summary>
            Encrypt ScopedPdu using TripleDES encryption protocol
            </summary>
            <param name="unencryptedData">Unencrypted ScopedPdu byte array</param>
            <param name="offset">Offset to start encryption</param>
            <param name="length">Length of data to encrypt</param>
            <param name="key">Encryption key. Key has to be at least 32 bytes is length</param>
            <param name="engineBoots">Authoritative engine boots value</param>
            <param name="engineTime">Authoritative engine time value.</param>
            <param name="privacyParameters">Privacy parameters out buffer. This field will be filled in with information
            required to decrypt the information. Output length of this field is 8 bytes and space has to be reserved
            in the USM header to store this information</param>
            <param name="authDigest">Authentication digest class reference. Used by TripleDES.</param>
            <returns>Encrypted byte array</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when encryption key is null or length of the encryption key is too short.</exception>
        </member>
        <member name="M:SnmpSharpNet.Privacy3DES.Decrypt(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32,System.Byte[])">
            <summary>
            Decrypt TripleDES encrypted ScopedPdu
            </summary>
            <param name="encryptedData">Source data buffer</param>
            <param name="offset">Offset within the buffer to start decryption process</param>
            <param name="length">Length of data to decrypt</param>
            <param name="key">Decryption key. Key length has to be 32 bytes in length or longer (bytes beyond 32 bytes are ignored).</param>
            <param name="engineBoots">Authoritative engine boots value</param>
            <param name="engineTime">Authoritative engine time value</param>
            <param name="privacyParameters">Privacy parameters extracted from USM header</param>
            <returns>Decrypted byte array</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when encrypted data is null or length == 0</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when encryption key length is less then 32 byte or if privacy parameters
            argument is null or length other then 8 bytes</exception>
        </member>
        <member name="P:SnmpSharpNet.Privacy3DES.MinimumKeyLength">
            <summary>
            Returns minimum encryption/decryption key length. For TripleDES, returned value is 32.
            </summary>
            <remarks>
            TripleDES protocol requires a 24 byte encryption key and additional 8 bytes are used for generating the
            encryption IV.
            </remarks>
        </member>
        <member name="P:SnmpSharpNet.Privacy3DES.MaximumKeyLength">
            <summary>
            Return maximum encryption/decryption key length. For TripleDES, returned value is 32
            </summary>
            <remarks>
            TripleDES protocol requires a 24 byte encryption key and additional 8 bytes are used for generating the
            encryption IV.
            </remarks>
        </member>
        <member name="P:SnmpSharpNet.Privacy3DES.PrivacyParametersLength">
            <summary>
            Returns the length of privacyParameters USM header field. For TripleDES, field length is 8.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Privacy3DES.GetEncryptedLength(System.Int32)">
            <summary>
            Get final encrypted length
            </summary>
            <remarks>
            TripleDES performs encryption on 8 byte blocks so the final encrypted size will be a
            mulitiple of 8 with padding added to the end of the ScopedPdu if required.
            </remarks>
            <param name="scopedPduLength">BER encoded ScopedPdu data length</param>
            <returns>Length of encrypted byte array</returns>
        </member>
        <member name="M:SnmpSharpNet.Privacy3DES.GetSalt(System.Int32)">
            <summary>
            Get TripleDES encryption salt value.
            </summary>
            <remarks>
            Salt value is generated by concatenating engineBoots value with
            the random integer value.
            </remarks>s
            <param name="engineBoots">SNMP engine boots value</param>
            <returns>Salt byte array 8 byte in length</returns>
        </member>
        <member name="M:SnmpSharpNet.Privacy3DES.GetIV(System.Byte[],System.Byte[])">
            <summary>
            Generate IV from the privacy key and salt value returned by GetSalt method.
            </summary>
            <param name="privacyKey">16 byte privacy key</param>
            <param name="salt">Salt value returned by GetSalt method</param>
            <returns>IV value used in the encryption process</returns>
            <exception cref="T:SnmpSharpNet.SnmpPrivacyException">Thrown when privacy key is less then 16 bytes long.</exception>
        </member>
        <member name="P:SnmpSharpNet.Privacy3DES.Name">
            <summary>
            Privacy protocol name
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Privacy3DES.ExtendShortKey(System.Byte[],System.Byte[],System.Byte[],SnmpSharpNet.IAuthenticationDigest)">
            <summary>
            Extends the encryption key if key size returned by PasswordToKey is less then minimum
            required by the encryption protocol.
            </summary>
            <remarks>
            There is no need to call this method in a user application becuase PasswordToKey() method will
            make the call if password it generates is too short.
            </remarks>
            <param name="shortKey">Encryption key</param>
            <param name="password">Privacy password</param>
            <param name="engineID">Authoritative engine id</param>
            <param name="authProtocol">Authentication protocol class instance</param>
            <returns>unaltered shortKey value</returns>
        </member>
        <member name="P:SnmpSharpNet.Privacy3DES.CanExtendShortKey">
            <summary>
            TripleDES implementation supports extending of a short encryption key. Always returns true.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Privacy3DES.PasswordToKey(System.Byte[],System.Byte[],SnmpSharpNet.IAuthenticationDigest)">
            <summary>
            Convert privacy password into encryption key using packet authentication hash.
            </summary>
            <param name="secret">Privacy user secret/password</param>
            <param name="engineId">Authoritative engine id of the SNMP agent</param>
            <param name="authProtocol">Authentication protocol</param>
            <returns>Encryption key</returns>
            <exception cref="T:SnmpSharpNet.SnmpPrivacyException">Thrown when user secret/password is shorter then MinimumKeyLength</exception>
        </member>
        <member name="T:SnmpSharpNet.PrivacyAES192">
            <summary>
            Privacy class for AES 192-bit encryption. This is a helper class. Full functionality is implemented
            in <see cref="T:SnmpSharpNet.PrivacyAES"/> parent class.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.PrivacyAES192.#ctor">
            <summary>
            Standard constructor initializes encryption key size in the parent <see cref="T:SnmpSharpNet.PrivacyAES"/> class to 24 bytes (192 bit).
            </summary>
        </member>
        <member name="P:SnmpSharpNet.PrivacyAES192.Name">
            <summary>
            Returns privacy protocol name "AES192".
            </summary>
        </member>
        <member name="T:SnmpSharpNet.PrivacyAES256">
            <summary>
            Privacy class for AES 256-bit encryption. This is a helper class. Full functionality is implemented
            in <see cref="T:SnmpSharpNet.PrivacyAES"/> parent class.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.PrivacyAES256.#ctor">
            <summary>
            Standard constructor initializes encryption key size in the parent <see cref="T:SnmpSharpNet.PrivacyAES"/> class to 32 bytes (256 bit).
            </summary>
        </member>
        <member name="P:SnmpSharpNet.PrivacyAES256.Name">
            <summary>
            Returns privacy protocol name "AES256".
            </summary>
        </member>
        <member name="T:SnmpSharpNet.SimpleSnmp">
            <summary>
            Utility class to enable simplified access to SNMP version 1 and version 2 requests and replies.
            </summary>
            <remarks>
            Use this class if you are not looking for "deep" SNMP functionality. Design of the class is based
            around providing simplest possible access to SNMP operations without getting stack into details.
            
            If you are using the simplest way, you will leave SuppressExceptions flag to true and get all errors causing methods to return "null" result
            which will not tell you why operation failed. You can change the SuppressExceptions flag to false and catch any and all
            exception throwing errors.
            
            Either way, have fun.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.SimpleSnmp._peerIP">
            <summary>
            SNMP Agents IP address
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SimpleSnmp._peerName">
            <summary>
            SNMP Agents name
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SimpleSnmp._peerPort">
            <summary>
            SNMP Agent UDP port number
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SimpleSnmp._target">
            <summary>
            Target class
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SimpleSnmp._timeout">
            <summary>
            Timeout value in milliseconds
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SimpleSnmp._retry">
            <summary>
            Maximum retry count excluding the first request
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SimpleSnmp._community">
            <summary>
            SNMP community name
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SimpleSnmp._nonRepeaters">
            <summary>
            Non repeaters value used in SNMP GET-BULK requests
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SimpleSnmp._maxRepetitions">
            <summary>
            Maximum repetitions value used in SNMP GET-BULK requests
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SimpleSnmp._suppressExceptions">
            <summary>
            Flag determines if exceptions are suppressed or thrown. When exceptions are suppressed, methods
            return null on errors regardless of what the error is.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.#ctor">
            <summary>Constructor.</summary>
            <remarks>
            Class is initialized to default values. Peer IP address is set to loopback, peer port number
            to 161, timeout to 2000 ms (2 seconds), retry count to 2 and community name to public.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.#ctor(System.String)">
            <summary>
            Constructor.
            </summary>
            <remarks>
            Class is initialized with default values with the agent name set to the supplied DNS name (or 
            IP address). If peer name is a DNS name, DNS resolution will take place in the constructor attempting
            to resolve it an IP address.
            </remarks>
            <param name="peerName">Peer name or IP address</param>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.#ctor(System.String,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="peerName">Peer name or IP address</param>
            <param name="community">SNMP community name</param>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.#ctor(System.String,System.Int32,System.String)">
            <summary>
            Constructor.
            </summary>
            <param name="peerName">Peer name or IP address</param>
            <param name="peerPort">Peer UDP port number</param>
            <param name="community">SNMP community name</param>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.#ctor(System.String,System.Int32,System.String,System.Int32,System.Int32)">
            <summary>
            Constructor.
            </summary>
            <param name="peerName">Peer name or IP address</param>
            <param name="peerPort">Peer UDP port number</param>
            <param name="community">SNMP community name</param>
            <param name="timeout">SNMP request timeout</param>
            <param name="retry">Maximum number of retries excluding the first request (0 = 1 request is sent)</param>
        </member>
        <member name="P:SnmpSharpNet.SimpleSnmp.Valid">
            <summary>Class validity flag</summary>
            <remarks>
            Return class validity status. If class is valid, it is ready to send requests and receive
            replies. If false, requests to send requests will fail.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.Get(SnmpSharpNet.SnmpVersion,SnmpSharpNet.Pdu)">
            <summary>
            SNMP GET request
            </summary>
            <example>SNMP GET request:
            <code>
            String snmpAgent = "**********";
            String snmpCommunity = "public";
            SimpleSnmp snmp = new SimpleSnmp(snmpAgent, snmpCommunity);
            // Create a request Pdu
            Pdu pdu = new Pdu();
            pdu.Type = SnmpConstants.GET; // type GET
            pdu.VbList.Add("1.3.6.1.2.1.1.1.0");
            Dictionary&lt;Oid, AsnType&gt; result = snmp.GetNext(SnmpVersion.Ver1, pdu);
            if( result == null ) {
              Console.WriteLine("Request failed.");
            } else {
              foreach (KeyValuePair&lt;Oid, AsnType&gt; entry in result)
              {
                Console.WriteLine("{0} = {1}: {2}", entry.Key.ToString(), SnmpConstants.GetTypeName(entry.Value.Type),
                  entry.Value.ToString());
              }
            }
            </code>
            Will return:
            <code>
            1.3.6.1.2.1.1.1.0 = OctetString: "Dual core Intel notebook"
            </code>
            </example>
            <param name="version">SNMP protocol version. Acceptable values are SnmpVersion.Ver1 and SnmpVersion.Ver2</param>
            <param name="pdu">Request Protocol Data Unit</param>
            <returns>Result of the SNMP request in a dictionary format with Oid => AsnType values</returns>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.Get(SnmpSharpNet.SnmpVersion,System.String[])">
            <summary>
            SNMP GET request
            </summary>
            <example>SNMP GET request:
            <code>
            String snmpAgent = "**********";
            String snmpCommunity = "public";
            SimpleSnmp snmp = new SimpleSnmp(snmpAgent, snmpCommunity);
            Dictionary&lt;Oid, AsnType&gt; result = snmp.GetNext(SnmpVersion.Ver1, new string[] { "1.3.6.1.2.1.1.1.0" });
            if( result == null ) {
              Console.WriteLine("Request failed.");
            } else {
              foreach (KeyValuePair&lt;Oid, AsnType&gt; entry in result)
              {
                Console.WriteLine("{0} = {1}: {2}", entry.Key.ToString(), SnmpConstants.GetTypeName(entry.Value.Type),
                  entry.Value.ToString());
              }
            }
            </code>
            Will return:
            <code>
            1.3.6.1.2.1.1.1.0 = OctetString: "Dual core Intel notebook"
            </code>
            </example>
            <param name="version">SNMP protocol version number. Acceptable values are SnmpVersion.Ver1 and SnmpVersion.Ver2</param>
            <param name="oidList">List of request OIDs in string dotted decimal format.</param>
            <returns>Result of the SNMP request in a dictionary format with Oid => AsnType values</returns>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.GetNext(SnmpSharpNet.SnmpVersion,SnmpSharpNet.Pdu)">
            <summary>
            SNMP GET-NEXT request
            </summary>
            <example>SNMP GET-NEXT request:
            <code>
            String snmpAgent = "**********";
            String snmpCommunity = "private";
            SimpleSnmp snmp = new SimpleSnmp(snmpAgent, snmpCommunity);
            // Create a request Pdu
            Pdu pdu = new Pdu();
            pdu.Type = SnmpConstants.GETNEXT; // type GETNEXT
            pdu.VbList.Add("1.3.6.1.2.1.1");
            Dictionary&lt;Oid, AsnType&gt; result = snmp.GetNext(pdu);
            if( result == null ) {
              Console.WriteLine("Request failed.");
            } else {
              foreach (KeyValuePair&lt;Oid, AsnType&gt; entry in result)
              {
                Console.WriteLine("{0} = {1}: {2}", entry.Key.ToString(), SnmpConstants.GetTypeName(entry.Value.Type),
                  entry.Value.ToString());
              }
            }
            </code>
            Will return:
            <code>
            1.3.6.1.2.1.1.1.0 = OctetString: "Dual core Intel notebook"
            </code>
            </example>
            <param name="version">SNMP protocol version. Acceptable values are SnmpVersion.Ver1 and SnmpVersion.Ver2</param>
            <param name="pdu">Request Protocol Data Unit</param>
            <returns>Result of the SNMP request in a dictionary format with Oid => AsnType values</returns>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.GetNext(SnmpSharpNet.SnmpVersion,System.String[])">
            <summary>
            SNMP GET-NEXT request
            </summary>
            <example>SNMP GET-NEXT request:
            <code>
            String snmpAgent = "**********";
            String snmpCommunity = "private";
            SimpleSnmp snmp = new SimpleSnmp(snmpAgent, snmpCommunity);
            Dictionary&lt;Oid, AsnType&gt; result = snmp.GetNext(SnmpVersion.Ver1, new string[] { "1.3.6.1.2.1.1" });
            if( result == null ) {
              Console.WriteLine("Request failed.");
            } else {
              foreach (KeyValuePair&lt;Oid, AsnType&gt; entry in result)
              {
                Console.WriteLine("{0} = {1}: {2}", entry.Key.ToString(), SnmpConstants.GetTypeName(entry.Value.Type),
                  entry.Value.ToString());
              }
            }
            </code>
            Will return:
            <code>
            1.3.6.1.2.1.1.1.0 = OctetString: "Dual core Intel notebook"
            </code>
            </example>
            <param name="version">SNMP protocol version number. Acceptable values are SnmpVersion.Ver1 and SnmpVersion.Ver2</param>
            <param name="oidList">List of request OIDs in string dotted decimal format.</param>
            <returns>Result of the SNMP request in a dictionary format with Oid => AsnType values</returns>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.GetBulk(SnmpSharpNet.Pdu)">
            <summary>
            SNMP GET-BULK request
            </summary>
            <remarks>
            GetBulk request type is only available with SNMP v2c agents. SNMP v3 also supports the request itself
            but that version of the protocol is not supported by SimpleSnmp.
            
            GetBulk method will return a dictionary of Oid to value mapped values as returned form a
            single GetBulk request to the agent. You can change how the request itself is made by changing the
            SimpleSnmp.NonRepeaters and SimpleSnmp.MaxRepetitions values. SimpleSnmp properties are only used
            when values in the parameter Pdu are set to 0.
            </remarks>
            <example>SNMP GET-BULK request:
            <code>
            String snmpAgent = "**********";
            String snmpCommunity = "private";
            SimpleSnmp snmp = new SimpleSnmp(snmpAgent, snmpCommunity);
            // Create a request Pdu
            Pdu pdu = new Pdu();
            pdu.Type = SnmpConstants.GETBULK; // type GETBULK
            pdu.VbList.Add("1.3.6.1.2.1.1");
            pdu.NonRepeaters = 0;
            pdu.MaxRepetitions = 10;
            Dictionary&lt;Oid, AsnType&gt; result = snmp.GetBulk(pdu);
            if( result == null ) {
              Console.WriteLine("Request failed.");
            } else {
              foreach (KeyValuePair&lt;Oid, AsnType&gt; entry in result)
              {
                Console.WriteLine("{0} = {1}: {2}", entry.Key.ToString(), SnmpConstants.GetTypeName(entry.Value.Type),
                  entry.Value.ToString());
              }
            }
            </code>
            Will return:
            <code>
            1.3.6.1.2.1.1.1.0 = OctetString: "Dual core Intel notebook"
            1.3.6.1.2.1.1.2.0 = ObjectId: 1.3.6.1.9.233233.1.1
            1.3.6.1.2.1.1.3.0 = TimeTicks: 0d 0h 0m 1s 420ms
            1.3.6.1.2.1.1.4.0 = OctetString: "<EMAIL>"
            1.3.6.1.2.1.1.5.0 = OctetString: "milans-nbook"
            1.3.6.1.2.1.1.6.0 = OctetString: "Developer home"
            1.3.6.1.2.1.1.8.0 = TimeTicks: 0d 0h 0m 0s 10ms
            </code>
            </example>
            <param name="pdu">Request Protocol Data Unit</param>
            <returns>Result of the SNMP request in a dictionary format with Oid => AsnType values</returns>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.GetBulk(System.String[])">
            <summary>
            SNMP GET-BULK request
            </summary>
            <remarks>
            Performs a GetBulk SNMP v2 operation on a list of OIDs. This is a convenience function that
            calls GetBulk(Pdu) method.
            </remarks>
            <example>SNMP GET-BULK request:
            <code>
            String snmpAgent = "**********";
            String snmpCommunity = "private";
            SimpleSnmp snmp = new SimpleSnmp(snmpAgent, snmpCommunity);
            Dictionary&lt;Oid, AsnType&gt; result = snmp.GetBulk(new string[] { "1.3.6.1.2.1.1" });
            if( result == null ) {
              Console.WriteLine("Request failed.");
            } else {
              foreach (KeyValuePair&lt;Oid, AsnType&gt; entry in result)
              {
                Console.WriteLine("{0} = {1}: {2}", entry.Key.ToString(), SnmpConstants.GetTypeName(entry.Value.Type),
                  entry.Value.ToString());
              }
            }
            </code>
            </example>
            <param name="oidList">List of request OIDs in string dotted decimal format.</param>
            <returns>Result of the SNMP request in a dictionary format with Oid => AsnType values</returns>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.Set(SnmpSharpNet.SnmpVersion,SnmpSharpNet.Pdu)">
            <summary>
            SNMP SET request
            </summary>
            <example>Set operation in SNMP version 1:
            <code>
            String snmpAgent = "**********";
            String snmpCommunity = "private";
            SimpleSnmp snmp = new SimpleSnmp(snmpAgent, snmpCommunity);
            // Create a request Pdu
            Pdu pdu = new Pdu();
            pdu.Type = SnmpConstants.SET; // type SET
            Oid setOid = new Oid("1.3.6.1.2.1.1.1.0"); // sysDescr.0
            OctetString setValue = new OctetString("My personal toy");
            pdu.VbList.Add(setOid, setValue);
            Dictionary&lt;Oid, AsnType&gt; result = snmp.Set(SnmpVersion.Ver1, pdu);
            if( result == null ) {
              Console.WriteLine("Request failed.");
            } else {
              Console.WriteLine("Success!");
            }
            </code>
            
            To use SNMP version 2, change snmp.Set() method call first parameter to SnmpVersion.Ver2.
            </example>
            <param name="version">SNMP protocol version number. Acceptable values are SnmpVersion.Ver1 and SnmpVersion.Ver2</param>
            <param name="pdu">Request Protocol Data Unit</param>
            <returns>Result of the SNMP request in a dictionary format with Oid => AsnType values</returns>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.Set(SnmpSharpNet.SnmpVersion,SnmpSharpNet.Vb[])">
            <summary>
            SNMP SET request
            </summary>
            <example>Set operation in SNMP version 1:
            <code>
            String snmpAgent = "**********";
            String snmpCommunity = "private";
            SimpleSnmp snmp = new SimpleSnmp(snmpAgent, snmpCommunity);
            // Create a request Pdu
            List&lt;Vb&gt; vbList = new List&lt;Vb&gt;();
            Oid setOid = new Oid("1.3.6.1.2.1.1.1.0"); // sysDescr.0
            OctetString setValue = new OctetString("My personal toy");
            vbList.Add(new Vb(setOid, setValue));
            Dictionary&lt;Oid, AsnType&gt; result = snmp.Set(SnmpVersion.Ver1, list.ToArray());
            if( result == null ) {
              Console.WriteLine("Request failed.");
            } else {
              Console.WriteLine("Success!");
            }
            </code>
            
            To use SNMP version 2, change snmp.Set() method call first parameter to SnmpVersion.Ver2.
            </example>
            <param name="version">SNMP protocol version number. Acceptable values are SnmpVersion.Ver1 and SnmpVersion.Ver2</param>
            <param name="vbs">Vb array containing Oid/AsnValue pairs for the SET operation</param>
            <returns>Result of the SNMP request in a dictionary format with Oid => AsnType values</returns>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.Walk(SnmpSharpNet.SnmpVersion,System.String)">
            <summary>SNMP WALK operation</summary>
            <remarks>
            When using SNMP version 1, walk is performed using GET-NEXT calls. When using SNMP version 2, 
            walk is performed using GET-BULK calls.
            </remarks>
            <example>Example SNMP walk operation using SNMP version 1:
            <code>
            String snmpAgent = "**********";
            String snmpCommunity = "private";
            SimpleSnmp snmp = new SimpleSnmp(snmpAgent, snmpCommunity);
            Dictionary&lt;Oid, AsnType&gt; result = snmp.Walk(SnmpVersion.Ver1, "1.3.6.1.2.1.1");
            if( result == null ) {
              Console.WriteLine("Request failed.");
            } else {
            foreach (KeyValuePair&lt;Oid, AsnType&gt; entry in result)
            {
              Console.WriteLine("{0} = {1}: {2}", entry.Key.ToString(), SnmpConstants.GetTypeName(entry.Value.Type),
                entry.Value.ToString());
            }
            </code>
            Will return:
            <code>
            1.3.6.1.2.1.1.1.0 = OctetString: "Dual core Intel notebook"
            1.3.6.1.2.1.1.2.0 = ObjectId: 1.3.6.1.9.233233.1.1
            1.3.6.1.2.1.1.3.0 = TimeTicks: 0d 0h 0m 1s 420ms
            1.3.6.1.2.1.1.4.0 = OctetString: "<EMAIL>"
            1.3.6.1.2.1.1.5.0 = OctetString: "milans-nbook"
            1.3.6.1.2.1.1.6.0 = OctetString: "Developer home"
            1.3.6.1.2.1.1.8.0 = TimeTicks: 0d 0h 0m 0s 10ms
            </code>
            
            To use SNMP version 2, change snmp.Set() method call first parameter to SnmpVersion.Ver2.
            </example>
            <param name="version">SNMP protocol version. Acceptable values are SnmpVersion.Ver1 and 
            SnmpVersion.Ver2</param>
            <param name="rootOid">OID to start WALK operation from. Only child OIDs of the rootOid will be
            retrieved and returned</param>
            <returns>Oid => AsnType value mappings on success, empty dictionary if no data was found or
            null on error</returns>
        </member>
        <member name="P:SnmpSharpNet.SimpleSnmp.PeerIP">
            <summary>
            Get/Set peer IP address
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SimpleSnmp.PeerName">
            <summary>
            Get/Set peer name
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SimpleSnmp.PeerPort">
            <summary>
            Get/Set peer port number
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SimpleSnmp.Timeout">
            <summary>
            Get set timeout value in milliseconds
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SimpleSnmp.Retry">
            <summary>
            Get/Set maximum retry count
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SimpleSnmp.Community">
            <summary>
            Get/Set SNMP community name
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SimpleSnmp.NonRepeaters">
            <summary>
            Get/Set NonRepeaters value
            </summary>
            <remarks>NonRepeaters value will only be used by SNMPv2 GET-BULK requests. Any other
            request type will ignore this value.</remarks>
        </member>
        <member name="P:SnmpSharpNet.SimpleSnmp.MaxRepetitions">
            <summary>
            Get/Set MaxRepetitions value
            </summary>
            <remarks>MaxRepetitions value will only be used by SNMPv2 GET-BULK requests. Any other
            request type will ignore this value.</remarks>
        </member>
        <member name="M:SnmpSharpNet.SimpleSnmp.Resolve">
            <summary>
            Resolve peer name to an IP address
            </summary>
            <remarks>
            This method will not throw any exceptions, even on failed DNS resolution. Make
            sure you call SimpleSnmp.Valid property to verify class state.
            </remarks>
            <returns>true if DNS resolution is successful, otherwise false</returns>
        </member>
        <member name="P:SnmpSharpNet.SimpleSnmp.SuppressExceptions">
            <summary>
            Get/Set exception suppression flag.
            
            If exceptions are suppressed all methods return null on any and all errors. With suppression disabled, you can
            capture error details in try/catch blocks around method calls.
            </summary>
        </member>
        <member name="T:SnmpSharpNet.SnmpError">
            <summary>Helper returns error messages for SNMP v1 and v2 error codes</summary>
            <remarks>
            Helper class provides translation of SNMP version 1 and 2 error status codes to short, descriptive
            error messages.
            
            To use, call the static member <see cref="M:SnmpSharpNet.SnmpError.ErrorMessage(System.Int32)"/>.
            
            Example:
            <code>Console.WriteLine("Agent error: {0}",SnmpError.ErrorMessage(12));</code>
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.SnmpError.ErrorMessage(System.Int32)">
            <summary>
            Return SNMP version 1 and 2 error code (errorCode field in the <see cref="T:SnmpSharpNet.Pdu"/> class) as
            a short, descriptive string.
            </summary>
            <param name="errorCode">Error code sent by the agent</param>
            <returns>Short error message for the error code</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpError.#ctor">
            <summary>
            Private constructor.
            </summary>
        </member>
        <member name="T:SnmpSharpNet.AuthenticationDigests">
            <summary>
            Enumeration of available authentication digests
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AuthenticationDigests.None">
            <summary>
            Authentication hash method none. Used when authentication is disabled.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AuthenticationDigests.MD5">
            <summary>
            Authentication protocol is HMAC-MD5.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.AuthenticationDigests.SHA1">
            <summary>
            Authentication protocol is HMAC-SHA1.
            </summary>
        </member>
        <member name="T:SnmpSharpNet.SnmpAuthenticationException">
            <summary>
            Exception of this type is thrown when SNMP version 3 packet containing authentication information
            has failed authentication check.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpAuthenticationException.#ctor(System.String)">
            <summary>
            Standard constructor.
            </summary>
            <param name="msg">Error message</param>
        </member>
        <member name="T:SnmpSharpNet.SnmpDecodingException">
            <summary>
            Exception thrown on failure to decode BER encoded information.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpDecodingException.#ctor(System.String)">
            <summary>
            standard constructor
            </summary>
            <param name="msg">exception message</param>
        </member>
        <member name="T:SnmpSharpNet.SnmpInvalidPduTypeException">
            <summary>
            Exception thrown when specific PDU type was expected and a different type was received.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpInvalidPduTypeException.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="msg">Error message</param>
        </member>
        <member name="T:SnmpSharpNet.SnmpInvalidVersionException">
            <summary>
            Exception thrown when invalid SNMP version was encountered in the packet
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpInvalidVersionException.#ctor(System.String)">
            <summary>
            Standard constructor
            </summary>
            <param name="msg">Exception error message</param>
        </member>
        <member name="T:SnmpSharpNet.SnmpNetworkException">
            <summary>SNMP network exception</summary>
            <remarks>
            Exception thrown when network error was encountered. Network errors include host, network unreachable, connection refused, etc.
            
            One network exception that is not covered by this exception is request timeout.
            </remarks>
        </member>
        <member name="P:SnmpSharpNet.SnmpNetworkException.SystemException">
            <summary>
            Return system exception that caused raising of this Exception error.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpNetworkException.#ctor(System.Exception,System.String)">
            <summary>
            Standard constructor
            </summary>
            <param name="sysException">System exception that caused the error</param>
            <param name="msg">Error message</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpNetworkException.#ctor(System.String)">
            <summary>
            Constructor. Used when system exception did not cause the error and there is no parent
            exception associated with the error.
            </summary>
            <param name="msg">Error message</param>
        </member>
        <member name="T:SnmpSharpNet.SnmpPrivacyException">
            <summary>
            Privacy encryption or decryption exception
            </summary>
            <remarks>
            Exception thrown when errors were encountered related to the privacy protocol encryption and decryption operations.
            
            Use ParentException field to get the causing error details.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.SnmpPrivacyException._parentException">
            <summary>
            Exception that caused this exception to be thrown
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpPrivacyException.ParentException">
            <summary>
            Exception that caused this exception to be thrown
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpPrivacyException.#ctor(System.String)">
            <summary>
            Standard constructor initializes the exceptione error message
            </summary>
            <param name="msg">Error message</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpPrivacyException.#ctor(System.Exception,System.String)">
            <summary>
            Constructor initializes error message and parent exception
            </summary>
            <param name="ex">Parent exception</param>
            <param name="msg">Error message</param>
        </member>
        <member name="T:SnmpSharpNet.IAgentParameters">
            <summary>
            Every agent parameters class implements this interface
            </summary>
        </member>
        <member name="P:SnmpSharpNet.IAgentParameters.Version">
            <summary>
            Get SNMP version number.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.IAgentParameters.Valid">
            <summary>
            Check validity of the agent properties object.
            </summary>
            <returns>true if object has all the information needed, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.IAgentParameters.InitializePacket(SnmpSharpNet.SnmpPacket)">
            <summary>
            Initialize SNMP packet class with values contained in this class.
            </summary>
            <param name="packet">SNMP packet class</param>
        </member>
        <member name="M:SnmpSharpNet.IAgentParameters.Clone">
            <summary>
            Duplicate object
            </summary>
            <returns>Cloned copy of the object</returns>
        </member>
        <member name="T:SnmpSharpNet.PrivacyProtocols">
            <summary>
            Privacy protocol enumeration.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PrivacyProtocols.None">
            <summary>
            No privacy protocol. Data will not be encrypted
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PrivacyProtocols.DES">
            <summary>
            Privacy protocol is DES (56 bit encryption)
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PrivacyProtocols.AES128">
            <summary>
            Privacy protocol is AES-128 (128 bit key). For implementation details, see <see cref="T:SnmpSharpNet.PrivacyAES128"/> and
            <see cref="T:SnmpSharpNet.PrivacyAES"/> classes.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PrivacyProtocols.AES192">
            <summary>
            Privacy protocol is AES-192 (128 bit key). For implementation details, see <see cref="T:SnmpSharpNet.PrivacyAES192"/> and
            <see cref="T:SnmpSharpNet.PrivacyAES"/> classes.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PrivacyProtocols.AES256">
            <summary>
            Privacy protocol is AES-156 (256 bit key). For implementation details, see <see cref="T:SnmpSharpNet.PrivacyAES256"/> and
            <see cref="T:SnmpSharpNet.PrivacyAES"/> classes.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PrivacyProtocols.TripleDES">
            <summary>
            Privacy protocol is Triple-DES. For implementation details see <see cref="T:SnmpSharpNet.Privacy3DES"/>.
            </summary>
        </member>
        <member name="T:SnmpSharpNet.SecureAgentParameters">
            <summary>Secure SNMPv3 agent parameters</summary>
            <remarks>
            SNMP Agent specific values. This class stores values to access SNMP version 3
            agents.
            
            Pass this class with your request data (Pdu) to the request method of the target class to make
            a request.
            
            Based on the information in this class, an appropriate request will be made by the request class.
            
            Following request types are generated:
            
            * if EngineBoots and EngineTime are integer value 0 or if EngineId value is length 0, Discovery
            request is made and passed instance of the SecureAgentParameters is updated with returned values.
            
            * in all other cases, SNMP request is made to the agent
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._engineId">
            <summary>
            Authoritative engine id
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._engineBoots">
            <summary>
            Authoritative engine boots value
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._engineTime">
            <summary>
            Authoritative engine time value
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._engineTimeStamp">
            <summary>
            Time stamp when authoritative engine time value was last refreshed with data from the agent.
            
            This value is used to calculate up to date authoritative agent time value without having to
            repeat discovery process every 150 seconds.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._securityName">
            <summary>
            Security name value, or user name.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._privacyProtocol">
            <summary>
            Privacy protocol to use. For available protocols, see <see cref="T:SnmpSharpNet.PrivacyProtocols"/> enumeration.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._authenticationProtocol">
            <summary>
            Authentication digest to use in authNoPriv and authPriv security combinations. For available
            authentication digests, see <see cref="T:SnmpSharpNet.AuthenticationDigests"/> enumeration.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._privacySecret">
            <summary>
            Privacy secret (or privacy password)
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._authenticationSecret">
            <summary>
            Authentication secret (or authentication password)
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._contextEngineId">
            <summary>
            Context engine id. By default, this value is set to authoritative engine id value unless specifically
            set to a different value here.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._contextName">
            <summary>
            Context name. By default this value is a 0 length string (no context name). Set this value if you
            require it to be defined in ScopedPdu.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._maxMessageSize">
            <summary>
            Maximum message size. This value is by default set to 64KB and then updated by the maximum message
            size value in the response from the agent.
            
            This value should be the smallest message size supported by both the agent and manager.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._reportable">
            <summary>
            Reportable option flag. Set to true by default.
            
            This flag controls if reportable flag will be set in the packet. When this flag is set in the packet,
            agent will respond to invalid requests with Report packets. Without this flag being set, all invalid
            requests are silently dropped by the agent.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._privacyKey">
            <summary>
            Cached privacy key
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SecureAgentParameters._authenticationKey">
            <summary>
            Cached authentication key
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.#ctor(SnmpSharpNet.SecureAgentParameters)">
            <summary>
            Copy constructor. Initialize the class with the values of the parameter class values.
            </summary>
            <param name="second">Parameter class.</param>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.EngineId">
            <summary>
            Agent authoritative engine id
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.EngineBoots">
            <summary>
            SNMP version 3 agent engine boots value
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.EngineTimeStamp">
            <summary>
            Get engine time stamp value (last time engine boots and time values were retrieved from the SNMP agent).
            </summary>
            <returns>DateTime stamp of the time timeliness values were last refreshed</returns>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.EngineTime">
            <summary>
            SNMP version 3 agent engine time value.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.SecurityName">
            <summary>
            Security or user name configured on the SNMP version 3 agent.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.Privacy">
            <summary>
            Privacy protocol used. Acceptable values are members of <see cref="T:SnmpSharpNet.PrivacyProtocols"/> enum.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.PrivacySecret">
            <summary>
            Privacy secret. Length of the secret is dependent on the selected privacy method.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.Authentication">
            <summary>
            Authentication method. Acceptable values are members of <see cref="T:SnmpSharpNet.AuthenticationDigests"/> enum.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.AuthenticationSecret">
            <summary>
            Authentication secret. Secret length depends on the hash algorithm selected.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.Version">
            <summary>
            SNMP version. Only acceptable version is <see cref="F:SnmpSharpNet.SnmpVersion.Ver3"/>
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.ContextEngineId">
            <summary>
            Get SNMP version 3 context engine id. By default, this value will be set
            to the same engine id as authoritative engine id (EngineId). I haven't see a
            scenario where this value needs to be different by a manager but now there
            is an option to do it.
            
            To use the default operation, do not set this value or, if you've already set it,
            reset it to null (object.ContextEngineId.Reset()).
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.ContextName">
            <summary>
            Get SNMP version 3 context name
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.MaxMessageSize">
            <summary>
            Get SNMP version 3 maximum message size object
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.Reportable">
            <summary>
            Get/Set reportable flag status in the SNMP version 3 packet.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.noAuthNoPriv(System.String)">
            <summary>
            Prepare class for noAuthNoPriv operations. Set authentication and privacy protocols to none.
            </summary>
            <param name="securityName">User security name</param>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.authNoPriv(System.String,SnmpSharpNet.AuthenticationDigests,System.String)">
            <summary>
            Prepare class for authNoPriv operations. Set privacy protocol to none
            </summary>
            <param name="securityName">User security name</param>
            <param name="authDigest">Authentication protocol</param>
            <param name="authSecret">Authentication secret (password)</param>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.authPriv(System.String,SnmpSharpNet.AuthenticationDigests,System.String,SnmpSharpNet.PrivacyProtocols,System.String)">
            <summary>
            Prepare class for authPriv operations.
            </summary>
            <param name="securityName">User security name</param>
            <param name="authDigest">Authentication protocol</param>
            <param name="authSecret">Authentication secret (password)</param>
            <param name="privProtocol">Privacy protocol</param>
            <param name="privSecret">Privacy secret (encryption password)</param>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.PrivacyKey">
            <summary>
            Get/Set cached privacy key value
            </summary>
            <remarks>
            Privacy key is set by reference.
            </remarks>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.AuthenticationKey">
            <summary>
            Get/Set cached authentication key value
            </summary>
            <remarks>
            Authentication key value is set by reference.
            </remarks>
        </member>
        <member name="P:SnmpSharpNet.SecureAgentParameters.HasCachedKeys">
            <summary>
            Check if cached privacy or authentication keys are available
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.Valid">
            <summary>
            Checks validity of the class. 
            </summary>
            <returns>Returns false if all required values are not initialized, or if invalid
            combination of options is set, otherwise true.</returns>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.InitializePacket(SnmpSharpNet.SnmpPacket)">
            <summary>
            InitializePacket SNMP packet with values from this class. Works only on SNMP version 3 packets.
            </summary>
            <param name="packet">Instance of <see cref="T:SnmpSharpNet.SnmpV3Packet"/></param>
            <exception cref="T:SnmpSharpNet.SnmpInvalidVersionException">Thrown when parameter packet is not SnmpV3Packet</exception>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.UpdateValues(SnmpSharpNet.SnmpPacket)">
            <summary>
            Copy all relevant values from the SnmpV3Packet class. Do not use this class for
            updating the SNMP version 3 discovery process results because secret name, authentication
            and privacy values are updated as well which discovery process doesn't use.
            </summary>
            <param name="packet"><see cref="T:SnmpSharpNet.SnmpV3Packet"/> cast as <see cref="T:SnmpSharpNet.SnmpPacket"/></param>
            <exception cref="T:SnmpSharpNet.SnmpInvalidVersionException">Thrown when SNMP packet class other then version 3 
            is passed as parameter</exception>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.UpdateDiscoveryValues(SnmpSharpNet.SnmpPacket)">
            <summary>
            Update class values with SNMP version 3 discovery values from the supplied <see cref="T:SnmpSharpNet.SnmpV3Packet"/>
            class. Values updated are EngineId, EngineTime and EngineBoots.
            </summary>
            <param name="packet"><see cref="T:SnmpSharpNet.SnmpV3Packet"/> class cast as <see cref="T:SnmpSharpNet.SnmpPacket"/></param>
            <exception cref="T:SnmpSharpNet.SnmpInvalidVersionException">Thrown when SNMP packet class other then version 3 
            is passed as parameter</exception>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.UpdateTimeStamp">
            <summary>
            Updates engine time timestamp. This value is used to determine if agents engine time stored
            in this class is valid.
            
            Timestamp is saved as DateTime class by default initialized to DateTime.MinValue. Timestamp value
            is stored in GMT to make it portable (if it is saved on one computer and loaded on another that uses
            a different time zone).
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.ValidateEngineTime">
            <summary>
            Validate agents engine time. Valid engine time value is time that has been initialized to
            a value other then default (DateTime.MinValue is default set in the constructor) and that
            has been updated in the last 10 times the SNMP v3 timely window (150 seconds). In other words,
            valid time is any time value in the last 1500 seconds (or 25 minutes).
            </summary>
            <returns>True if engine time value is valid, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.GetCurrentEngineTime">
            <summary>
            Calculates and returns current agents engine time. <see cref="M:SnmpSharpNet.SecureAgentParameters.ValidateEngineTime"/> is called
            prior to calculation to make sure current engine time is timely enough to use.
            
            EngineTime is calculated as last received engine time + difference in seconds between the time
            stamp saved when last time value was received and current time (using the internal GMT clock).
            </summary>
            <returns>Adjusted engine time value or 0 if time is outside the time window.</returns>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.ValidateIncomingPacket(SnmpSharpNet.SnmpV3Packet)">
            <summary>
            Validate that incoming packet has arrived from the correct engine id and is using a correct
            combination of privacy and authentication values.
            </summary>
            <param name="packet">Received and parsed SNMP version 3 packet.</param>
            <returns>True if packet is valid, otherwise false.</returns>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown on following errors with ErrorCode:
            * ErrorCode = 0: SecureAgentParameters was updated after request was made but before reply was received (this is not allowed)
            * SnmpException.InvalidAuthoritativeEngineId: engine id in the reply does not match request
            * SnmpException.InvalidSecurityName: security name mismatch between request and reply packets
            * SnmpException.ReportOnNoReports: report packet received when we had reportable set to false in the request
            * SnmpException.UnsupportedNoAuthPriv: noAuthPriv is not supported
            </exception>
            <exception cref="T:SnmpSharpNet.SnmpPrivacyException">Thrown when configured privacy passwords in this class and in the packet class do not match</exception>
            <exception cref="T:SnmpSharpNet.SnmpAuthenticationException">Thrown when configured authentication passwords in this class and in the packet class do not match</exception>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.ResetKeys">
            <summary>
            Reset privacy and authentication keys to null.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.Reset">
            <summary>
            Reset the class. Initialize all member values to class defaults.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.Clone">
            <summary>
            Clone current object
            </summary>
            <returns>Duplicate object initialized with values from this class.</returns>
        </member>
        <member name="M:SnmpSharpNet.SecureAgentParameters.BuildCachedSecurityKeys">
            <summary>
            Build cached authentication and privacy encryption keys if they are appropriate for the selected security mode.
            </summary>
            <remarks>
            This method should be called after discovery process has been completed and all security related values
            have been set. For noAuthNoPriv, none of the keys are generated. authNoPriv will result in authentication
            key cached. authPriv will generate authentication and privacy keys.
            
            For successful key caching you need to set both relevant protocols and secret values.
            </remarks>
        </member>
        <member name="T:SnmpSharpNet.SnmpConstants">
            <summary>SNMP SMI version 1, version 2c and version 3 constants.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrNoError">
            <summary>No error</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrTooBig">
            <summary>Request too big</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrNoSuchName">
            <summary>Object identifier does not exist</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrBadValue">
            <summary>Invalid value</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrReadOnly">
            <summary>Requested invalid operation on a read only table</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrGenError">
            <summary>Generic error</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.enterpriseSpecific">
            <summary>Enterprise specific error</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrNoAccess">
            <summary>Access denied</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrWrongType">
            <summary>Incorrect type</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrWrongLength">
            <summary>Incorrect length</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrWrongEncoding">
            <summary>Invalid encoding</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrWrongValue">
            <summary>Object does not have correct value</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrNoCreation">
            <summary>Insufficient rights to perform create operation</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrInconsistentValue">
            <summary>Inconsistent value</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrResourceUnavailable">
            <summary>Requested resource is not available</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrCommitFailed">
            <summary>Unable to commit values</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrUndoFailed">
            <summary>Undo request failed</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrAuthorizationError">
            <summary>Authorization failed</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrNotWritable">
            <summary>Instance not writable</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ErrInconsistentName">
            <summary>Inconsistent object identifier</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.ColdStart">
            <summary>Cold start trap</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.WarmStart">
            <summary>Warm start trap</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.LinkDown">
            <summary>Link down trap</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.LinkUp">
            <summary>Link up trap</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.AuthenticationFailure">
            <summary>Authentication-failure trap</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.EgpNeighborLoss">
            <summary>EGP Neighbor Loss trap</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.EnterpriseSpecific">
            <summary>Enterprise Specific trap</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_INTEGER">
            <summary>Signed 32-bit integer ASN.1 data type. For implementation, see <see cref="T:SnmpSharpNet.Integer32"/></summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_INTEGER_STR">
            <summary>String representation of the AsnType.INTEGER type.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_STRING">
            <summary>Data type representing a sequence of zero or more 8-bit byte values. For implementation, see <see cref="T:SnmpSharpNet.OctetString"/></summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_STRING_STR">
            <summary>String representation of the AsnType.OCTETSTRING type.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_OBJECTID">
            <summary>Object id ASN.1 type. For implementation, see <see cref="T:SnmpSharpNet.Oid"/></summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_OBJECTID_STR">
            <summary>String representation of the SMI_OBJECTID type.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_NULL">
            <summary>Null ASN.1 value type. For implementation, see <see cref="T:SnmpSharpNet.Null"/>.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_NULL_STR">
            <summary>String representation of the SMI_NULL type.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_APPSTRING">
            <summary> An application string is a sequence of octets
            defined at the application level. Although the SMI
            does not define an Application String, it does define
            an IP Address which is an Application String of length
            four.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_APPSTRING_STR">
            <summary>String representation of the SMI_APPSTRING type.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_IPADDRESS">
            <summary> An IP Address is an application string of length four
            and is indistinguishable from the SMI_APPSTRING value.
            The address is a 32-bit quantity stored in network byte order.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_IPADDRESS_STR">
            <summary>String representation of the SMI_IPADDRESS type.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_COUNTER32">
            <summary> A non-negative integer that may be incremented, but not
            decremented. The value is a 32-bit unsigned quantity representing
            the range of zero to 2^32-1 (4,294,967,295). When the counter
            reaches its maximum value it wraps back to zero and starts again.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_COUNTER32_STR">
            <summary>String representation of the SMI_COUNTER32 type.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_GAUGE32">
            <summary> Represents a non-negative integer that may increase or
            decrease with a maximum value of 2^32-1. If the maximum
            value is reached the gauge stays latched until reset.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_GAUGE32_STR">
            <summary>String representation of the SMI_GAUGE32 type.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_UNSIGNED32">
            <summary> Used to represent the integers in the range of 0 to 2^32-1.
            This type is identical to the SMI_COUNTER32 and are
            indistinguishable in ASN.1
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_UNSIGNED32_STR">
            <summary>String representation of the SMI_UNSIGNED32 type.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_TIMETICKS">
            <summary> This represents a non-negative integer that counts time, modulo 2^32.
            The time is represented in hundredths (1/100th) of a second.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_TIMETICKS_STR">
            <summary>String representation of the SMI_TIMETICKS type.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_OPAQUE">
            <summary> Used to support the transport of arbitrary data. The
            data itself is encoded as an octet string, but may be in
            any format defined by ASN.1 or another standard.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_OPAQUE_STR">
            <summary>String representation of the SMI_OPAQUE type.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_COUNTER64">
            <summary> Defines a 64-bit unsigned counter. A counter is an integer that
            can be incremented, but cannot be decremented. A maximum value
            of 2^64 - 1 (18,446,744,073,709,551,615) can be represented.
            When the counter reaches it's maximum it wraps back to zero and
            starts again.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_COUNTER64_STR">
            <summary>String representation of the SMI_COUNTER64 type.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_UNKNOWN_STR">
            <summary>String representation of the unknown SMI data type.</summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_NOSUCHOBJECT">
            <summary> The SNMPv2 error representing that there is No-Such-Object
            for a particular object identifier. This error is the result
            of a requested object identifier that does not exist in the
            agent's tables
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_NOSUCHINSTANCE">
            <summary> The SNMPv2 error representing that there is No-Such-Instance
            for a particular object identifier. This error is the result
            of a requested object identifier instance does not exist in the
            agent's tables. 
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_ENDOFMIBVIEW">
            <summary> The SNMPv2 error representing the End-Of-Mib-View.
            This error variable will be returned by a SNMPv2 agent
            if the requested object identifier has reached the 
            end of the agent's mib table and there is no lexicographic 
            successor.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_SEQUENCE">
            <summary>
            SEQUENCE Variable Binding code. Hex value: 0x30
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SMI_PARTY_CLOCK">
            <summary> Defines an SNMPv2 Party Clock. The Party Clock is currently
            Obsolete, but included for backwards compatibility. Obsoleted in RFC 1902.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.SysUpTime">
            <summary>
            sysUpTime.0 OID is the first value in the VarBind array of SNMP version 2 TRAP packets
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.TrapObjectId">
            <summary>
            trapObjectID.0 OID is the second value in the VarBind array of SNMP version 2 TRAP packets
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.usmStatsUnsupportedSecLevels">
            <summary>
            SNMP version 3, USM error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.usmStatsNotInTimeWindows">
            <summary>
            SNMP version 3, USM error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.usmStatsUnknownSecurityNames">
            <summary>
            SNMP version 3, USM error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.usmStatsUnknownEngineIDs">
            <summary>
            SNMP version 3, USM error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.usmStatsWrongDigests">
            <summary>
            SNMP version 3, USM error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.usmStatsDecryptionErrors">
            <summary>
            SNMP version 3, USM error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.snmpUnknownSecurityModels">
            <summary>
            SNMP version 3, USM error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.snmpInvalidMsgs">
            <summary>
            SNMP version 3, USM error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.snmpUnknownPDUHandlers">
            <summary>
            SNMP version 3, USM error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpConstants.v3ErrorOids">
            <summary>
            Array of all SNMP version 3 REPORT packet error OIDs
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpConstants.GetSyntaxObject(System.Byte)">
            <summary>Used to create correct variable type object for the specified encoded type</summary>
            <param name="asnType">ASN.1 type code</param>
            <returns>A new object matching type supplied or null if type was not recognized.</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpConstants.GetSyntaxObject(System.String)">
            <summary>
            Return SNMP type object of the type specified by name. Supported variable types are:
            * <see cref="T:SnmpSharpNet.Integer32"/>
            * <see cref="T:SnmpSharpNet.Counter32"/>
            * <see cref="T:SnmpSharpNet.Gauge32"/>
            * <see cref="T:SnmpSharpNet.Counter64"/>
            * <see cref="T:SnmpSharpNet.TimeTicks"/>
            * <see cref="T:SnmpSharpNet.OctetString"/>
            * <see cref="T:SnmpSharpNet.IpAddress"/>
            * <see cref="T:SnmpSharpNet.Oid"/>
            * <see cref="T:SnmpSharpNet.Null"/>
            </summary>
            <param name="name">Name of the object type</param>
            <returns>New <see cref="T:SnmpSharpNet.AsnType"/> object.</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpConstants.GetTypeName(System.Byte)">
            <summary>
            Return string representation of the SMI value type.
            </summary>
            <param name="type">AsnType class Type member function value.</param>
            <returns>String formatted name of the SMI type.</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpConstants.DumpHex(System.Byte[])">
            <summary>
            Debugging function used to dump on the console supplied byte array in a format suitable for console output.
            </summary>
            <param name="data">Byte array data</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpConstants.IsValidVersion(System.Int32)">
            <summary>
            Check if SNMP version value is correct
            </summary>
            <param name="version">SNMP version value</param>
            <returns>true if valid SNMP version, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpConstants.#ctor">
            <summary>
            Private constructor to prevent the class with all static members from being instantiated.
            </summary>
        </member>
        <member name="T:SnmpSharpNet.SnmpException">
            <summary>
            SNMP generic exception. Thrown every time SNMP specific error is encountered.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.None">
            <summary>
            No error
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.UnsupportedSecurityModel">
            <summary>
            Security model specified in the packet is not supported
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.UnsupportedNoAuthPriv">
            <summary>
            Privacy enabled without authentication combination in a packet is not supported.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.InvalidAuthenticationParameterLength">
            <summary>
            Invalid length of the authentication parameter field. Expected length is 12 bytes when authentication is
            enabled. Same length is used for both MD5 and SHA-1 authentication protocols.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.AuthenticationFailed">
            <summary>
            Authentication of the received packet failed.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.UnsupportedPrivacyProtocol">
            <summary>
            Privacy protocol requested is not supported.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.InvalidPrivacyParameterLength">
            <summary>
            Invalid length of the privacy parameter field. Expected length depends on the privacy protocol. This exception
            can be raised when privacy packet contents are invalidly set by agent or if wrong privacy protocol is set in the
            packet class definition.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.InvalidAuthoritativeEngineId">
            <summary>
            Authoritative engine id is invalid.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.InvalidEngineBoots">
            <summary>
            Engine boots value is invalid
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.PacketOutsideTimeWindow">
            <summary>
            Received packet is outside the time window acceptable. Packet failed timeliness check.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.InvalidRequestId">
            <summary>
            Invalid request id in the packet.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.MaximumMessageSizeExceeded">
            <summary>
            SNMP version 3 maximum message size exceeded. Packet that was encoded will exceed maximum message
            size acceptable in this transaction.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.InvalidIAgentParameters">
            <summary>
            UdpTarget request cannot be processed because IAgentParameters does not contain required information
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.RequestTimedOut">
            <summary>
            Reply to a request was not received within the timeout period
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.NoDataReceived">
            <summary>
            Null data received on request.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.InvalidSecurityName">
            <summary>
            Security name (user name) in the reply does not match the name sent in request.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.ReportOnNoReports">
            <summary>
            Report packet was received when Reportable flag was set to false (we notified the peer that we do
            not receive report packets).
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.OidValueTypeChanged">
            <summary>
            Oid value type returned by an earlier operation does not match the value type returned by a subsequent entry.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException.InvalidOid">
            <summary>
            Specified Oid is invalid
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpException._errorCode">
            <summary>
            Error code. Provides a finer grained information about why the exception happened. This can be useful to
            the process handling the error to determine how critical the error that occured is and what followup actions
            to take.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpException.ErrorCode">
            <summary>
            Get/Set error code associated with the exception
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpException.#ctor">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpException.#ctor(System.String)">
            <summary>
            Standard constructor
            </summary>
            <param name="msg">SNMP Exception message</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpException.#ctor(System.Int32,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="errorCode">Error code associated with the exception</param>
            <param name="msg">Error message</param>
        </member>
        <member name="T:SnmpSharpNet.MutableByte">
            <summary>Mutable byte implementation class</summary>
            <remarks>
            Mutable byte class allows for manipulation of a byte array with
            operations like append, prepend.
            
            Functionality is implemented through temporary buffer creation
            and data duplication.
            
            <code>
            MutableByte buffer = new MutableByte();
            buffer += "test data";
            buffer.Append(". More test data");
            buffer.Prepend("This is ");
            Console.WriteLine(buffer.ToString()); // Prints out "This is test data. More test data"
            buffer.RemoveBeginning(8); // The buffer now holds "test data. More test data"
            buffer.Prepend("It could be "); // buffer is "It could be test data. More test data"
            buffer.RemoveEnd(" More test data".Length); // buffer: "It could be test data."
            buffer.Remove(12,5); // buffer: "It could be data"
            Console.WriteLine("{0}",Convert.ToChar(buffer[1])); // Output: "t"
            byte[] tmpBuffer = buffer; // Implicit conversion to byte[]
            buffer.Reset(); // Erase all the data from the buffer
            </code>
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.MutableByte._buffer">
            <summary>
            Internal byte buffer
            </summary>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.#ctor">
            <summary>
            Standard constructor. Initializes the internal buffer to null.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.#ctor(System.Byte[])">
            <summary>
            Constructor. Initialize internal buffer with supplied value.
            </summary>
            <param name="buf">Byte array to copy into internal buffer</param>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.#ctor(System.Byte[],System.Byte[])">
            <summary>
            Create new <see cref="T:SnmpSharpNet.MutableByte"/> class initialized by adding two byte buffers together. If
            one of the supplied byte arrays is value null then <see cref="T:System.ArgumentNullException"/> is thrown.
            </summary>
            <param name="buf1">First byte array</param>
            <param name="buf2">Second byte array</param>
            <exception cref="T:System.ArgumentNullException">If one or both arguments are null or length 0</exception>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.#ctor(System.Byte[],System.Int32)">
            <summary>
            Create new <see cref="T:SnmpSharpNet.MutableByte"/> class initialized with data from the supplied array up to length of buflen. Internaly,
            a call is made to MutableByte.Set(buf[],int) to initialize the new class data buffer.
            </summary>
            <param name="buf">Array used to initialize the class data</param>
            <param name="buflen">Number of bytes to use from the argument array to initialize the class.</param>
        </member>
        <member name="P:SnmpSharpNet.MutableByte.Value">
            <summary>
            Get byte[] buffer value. This property is internal because it exposes the internal byte array.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.MutableByte.Length">
            <summary>
            Byte buffer current length
            </summary>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Set(System.Byte[])">
            <summary>
            Set internal buffer to supplied value. Overwrites existing data.
            </summary>
            <param name="buf">Value to copy into internal buffer</param>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Set(System.Byte[],System.Int32)">
            <summary>
            Copy source buffer array up to length into the class.
            </summary>
            <param name="buf">Source byte array</param>
            <param name="length">Number of items to copy</param>
            <exception cref="T:System.ArgumentNullException">Thrown if buf argument is null or length of zero</exception>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Set(System.Byte)">
            <summary>
            Set internal buffer to size 1 and copy supplied byte value into it
            </summary>
            <param name="buf">Byte value to copy into internal byte array of size 1</param>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Set(System.Int32,System.Byte)">
            <summary>
            Set value at specified position to the supplied value
            </summary>
            <param name="position">Zero based offset from the beginning of the buffer</param>
            <param name="value">Value to set</param>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Set(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Set class value to the contents of the supplied array starting from offset with specified length
            </summary>
            <param name="value">Value to set the class to</param>
            <param name="offset">From the value start copying data from this offset</param>
            <param name="length">Byte count to copy</param>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Set(System.String)">
            <summary>
            Set class value with bytes from the string. UTF8 encoding is assumed.
            </summary>
            <param name="value">String value</param>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Append(System.Byte[])">
            <summary>
            Append data to the internal buffer
            </summary>
            <param name="buf">Byte array to append to the internal byte array</param>
            <exception cref="T:System.ArgumentNullException">Thrown when argument buf is null or length of zero</exception>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Append(System.Byte)">
            <summary>
            Append a single byte value to the internal buffer
            </summary>
            <param name="buf">Byte value to append to the internal buffer</param>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Insert(System.Int32,System.Byte[])">
            <summary>
            Insert byte array at position
            </summary>
            <param name="position">Insert position</param>
            <param name="buf">Byte array to insert at specified position</param>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Insert(System.Int32,System.Byte)">
            <summary>
            Insert single byte at specified location
            </summary>
            <param name="position">Location to perform insert (0 based)</param>
            <param name="buf">Byte value to insert</param>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Prepend(System.Byte[])">
            <summary>
            Prepend (insert at beginning) a byte array
            </summary>
            <param name="buf">Byte array to prepend</param>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Prepend(System.Byte)">
            <summary>
            Prepend (add at the beginning) a single byte value
            </summary>
            <param name="buf">Byte value to prepend</param>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.RemoveBeginning(System.Int32)">
            <summary>
            Remove bytes from the beginning of the array
            </summary>
            <param name="count">Number of bytes to remove</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when count points beyond the bounds of the internal byte array</exception>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.RemoveEnd(System.Int32)">
            <summary>
            Remove number of byte values from the end of the internal buffer
            </summary>
            <param name="count">Number of bytes to remove</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when count points beyond the bounds of the internal byte array</exception>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Remove(System.Int32,System.Int32)">
            <summary>
            Remove array byte members starting with position start for the length length bytes.
            </summary>
            <param name="start">Start position of bytes to remove</param>
            <param name="count">How many bytes to remove</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown if internal buffer is null or length zero, if start argument
            is less then zero or past the end of the internal byte array, and if argument count is greater then length of the
            internal byte array, start + count is past greater then the length of the buffer array or if argument count is less then 1.</exception>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Get(System.Int32,System.Int32)">
            <summary>
            Get sub-array
            </summary>
            <param name="position">Start of the sub-array. Zero based.</param>
            <param name="length">Count of bytes to copy</param>
            <returns>MutableByte array containing the sub-array.</returns>
            <exception cref="T:System.OverflowException">Thrown when position starts before the beginning of the array (position is less then 0) or 
            position + length is greater then the length of the byte array contained in the object.</exception>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.op_Addition(SnmpSharpNet.MutableByte,System.Byte[])">
            <summary>
            Add <see cref="T:SnmpSharpNet.MutableByte"/> and byte array values into a new MutableByte class.
            </summary>
            <param name="buf1"><see cref="T:SnmpSharpNet.MutableByte"/> class</param>
            <param name="buf2"><see cref="T:SnmpSharpNet.MutableByte"/> class</param>
            <returns>New <see cref="T:SnmpSharpNet.MutableByte"/> class containing concatenated result</returns>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.op_Addition(SnmpSharpNet.MutableByte,SnmpSharpNet.MutableByte)">
            <summary>
            Add <see cref="T:SnmpSharpNet.MutableByte"/> buffer values.
            </summary>
            <param name="buf1"><see cref="T:SnmpSharpNet.MutableByte"/> class</param>
            <param name="buf2"><see cref="T:SnmpSharpNet.MutableByte"/> class</param>
            <returns>New <see cref="T:SnmpSharpNet.MutableByte"/> class containing concatenated result</returns>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.op_Addition(SnmpSharpNet.MutableByte,System.Byte)">
            <summary>
            Add a MutableByte array and a single byte value
            </summary>
            <param name="buf1">MutableByte array</param>
            <param name="b">Byte value</param>
            <returns>New MutableByte array with values added</returns>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.op_Equality(SnmpSharpNet.MutableByte,SnmpSharpNet.MutableByte)">
            <summary>
            Compare two <see cref="T:SnmpSharpNet.MutableByte"/> class contents
            </summary>
            <param name="buf1"><see cref="T:SnmpSharpNet.MutableByte"/> class</param>
            <param name="buf2"><see cref="T:SnmpSharpNet.MutableByte"/> class</param>
            <returns>true if the same, otherwise falseB</returns>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.op_Inequality(SnmpSharpNet.MutableByte,SnmpSharpNet.MutableByte)">
            <summary>
            Negative compare.
            </summary>
            <param name="buf1">First MutableByte array</param>
            <param name="buf2">Second MutableByte array</param>
            <returns>true if class values are not equal, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.op_Implicit(SnmpSharpNet.MutableByte)~System.Byte[]">
            <summary>
            Allow implicit casting of this object as a byte array for any callers.
            </summary>
            <param name="obj">MutableByte object whose values should be cast as byte array</param>
            <returns>Byte array represented in the MutableObject class.</returns>
        </member>
        <member name="P:SnmpSharpNet.MutableByte.Item(System.Int32)">
            <summary>
            Index operator. Index access to the underlying byte array
            </summary>
            <param name="index">Index to access</param>
            <returns>byte array value</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown if requested byte index is outside the size of the internal byte array</exception>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.op_LessThan(SnmpSharpNet.MutableByte,SnmpSharpNet.MutableByte)">
            <summary>
            Lesser then operator overload.
            </summary>
            <param name="firstClass">First MutableByte class</param>
            <param name="secondClass">Second MutableByte class</param>
            <returns>True if firstClass is lesser then second class, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.op_GreaterThan(SnmpSharpNet.MutableByte,SnmpSharpNet.MutableByte)">
            <summary>
            Greater then operator overload.
            </summary>
            <param name="firstClass">First MutableByte class</param>
            <param name="secondClass">Second MutableByte class</param>
            <returns>True if firstClass is greater then second class, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Equals(System.Object)">
            <summary>
            Compares MutableByte object against another MutableByte object or a byte array
            </summary>
            <param name="obj">Object to compare class value with. Argument can be a byte array or an instance of MutableByte class.</param>
            <returns>Returns true if objects match, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Equals(System.Byte[],System.Byte[])">
            <summary>
            Compare two byte arrays
            </summary>
            <param name="buf1">First byte array</param>
            <param name="buf2">Second byte array</param>
            <returns>True if array contents are the same, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.GetHashCode">
            <summary>
            Returns object hash code. Just calls the base class implementation.
            </summary>
            <returns>Base class hash code value</returns>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.ToString">
            <summary>
            Convert array to a string
            </summary>
            <returns>String representation of the object as a hex string</returns>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.ToString(System.Int32,System.Int32)">
            <summary>
            Hexadecimal data dump of the specific range of buffer values
            </summary>
            <param name="start">Start position for data dump (0 based)</param>
            <param name="length">Number of bytes to include in the dump</param>
            <returns>String representation of the selected range.</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when start and length arguments point to internal byte array locations that are outside of the array bounds.</exception>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Clone">
            <summary>
            Clone object
            </summary>
            <returns>Cloned copy of the object cast as <see cref="T:System.Object"/></returns>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Reset">
            <summary>
            Reset object data to null
            </summary>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.Clear">
            <summary>
            Reset object data to null
            </summary>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.CompareTo(SnmpSharpNet.MutableByte)">
            <summary>
            Compare class to another MutableByte class.
            </summary>
            <param name="other">Class to compare with</param>
            <returns>-1 if class is less then, 0 if equal or 1 if greater then class it's compared against</returns>
        </member>
        <member name="M:SnmpSharpNet.MutableByte.CompareTo(System.Byte[])">
            <summary>
            Compare class to a byte[] array.
            </summary>
            <param name="other">Byte array to compare with</param>
            <returns>-1 if class is less then, 0 if equal or 1 if greater then array it's compared against</returns>
        </member>
        <member name="T:SnmpSharpNet.Pdu">
            <summary>
            SNMP Protocol Data Unit
            </summary>
            <remarks>
            SNMP PDU class that is the bases for all SNMP requests and replies. It is capable of processing
            SNMPv1 GET, GET-NEXT, REPLY and SNMPv2 GET, GET-NEXT, GET-BULK, REPLY, V2TRAP, INFORM and REPORT PDUs.
            <code>
            Pdu pdu = new Pdu();
            pdu.Type = PduType.Get;
            pdu.VbList.AddVb("1.3.6.1.2.1.1.1.0");
            pdu.VbList.AddVb("1.3.6.1.2.1.1.2.0");
            </code>
            
            By default, Pdu class initializes the RequestId (unique identifier of each SNMP request made by the manager)
            with a random value. User can force a new, random request id generation at the time packet is encoding by
            changing RequestId to 0. If you wish to set a specific RequestId, you can do it this way:
            
            <code>
            Pdu pdu = new Pdu();
            pdu.Type = PduType.GetNext;
            pdu.RequestId = 11; // Set a custom request id
            pdu.VbList.AddVb("1.3.6.1.2.1.1");
            </code>
            
            Pdu types with special options are notification PDUs, V2TRAP and INFORM, and Get-Bulk requests.
            
            Get-Bulk request is available in version 2c and 3 of the SNMP. Two special options can be set for these
            requests, NonRepeaters and MaxRepetitions. 
            
            NonRepeaters is a value telling the agent how many OIDs in the VbList are to be treated as a single 
            GetNext request.
            
            MaxRepeaters tells the agent how many variable bindings to return in a single Pdu for each requested Oid.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.Pdu._vbs">
            <summary>
            Variable binding collection
            </summary>
        </member>
        <member name="F:SnmpSharpNet.Pdu._errorStatus">
            <summary>
            Error status value.
            </summary>
            <remarks>
            See <see cref="T:SnmpSharpNet.SnmpConstants"/> class for definition of error values. If no error
            is encountered, this value is set to 0.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.Pdu._errorIndex">
            <summary>
            Error index value.
            </summary>
            <remarks>
            Points to the Vb sequence that caused the error. If not Vb the cause of the
            error, or if there is no error, this value is 0.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.Pdu._requestId">
            <summary>
            Request id value.
            </summary>
            <remarks>
            Integer32 value that uniquely represents this request. Used to match requests and replies.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.Pdu._trapTimeStamp">
            <summary>
            SNMPv2 trap first Vb is the trap time stamp. To create an SNMPv2 TRAP packet, set the timestamp value
            in this variable
            </summary>
        </member>
        <member name="F:SnmpSharpNet.Pdu._trapObjectID">
            <summary>
            SNMPv2 trap second Vb is the trap object ID.
            </summary>
            <remarks>
            This variable should be set to the trap OID and will be inserted
            into the encoded packet.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.Pdu.#ctor">
            <summary>
            Constructor.
            </summary>
            <remarks>
            Initializes all values to NULL and PDU type to GET
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.Pdu.#ctor(SnmpSharpNet.PduType)">
            <summary>
            Constructor.
            </summary>
            <remarks>
            Create Pdu of specific type.
            </remarks>
            <param name="pduType">Pdu type. For available values see <see cref="T:SnmpSharpNet.PduType"/></param>
        </member>
        <member name="M:SnmpSharpNet.Pdu.#ctor(SnmpSharpNet.VbCollection)">
            <summary>
            Constructor.
            </summary>
            <remarks>
            Sets the VarBind list to the Clone copy of the supplied list.
            </remarks>
            <param name="vbs">VarBind list to initialize the internal VbList to.</param>
        </member>
        <member name="M:SnmpSharpNet.Pdu.#ctor(SnmpSharpNet.VbCollection,SnmpSharpNet.PduType,System.Int32)">
            <summary>
            Constructor.
            </summary>
            <remarks>
            Initializes PDU class with supplied values.
            </remarks>
            <param name="vbs">VarBind list</param>
            <param name="type">PDU type</param>
            <param name="requestId">Request id</param>
        </member>
        <member name="M:SnmpSharpNet.Pdu.#ctor(SnmpSharpNet.Pdu)">
            <summary>
            Constructor
            </summary>
            <remarks>
            Initialize class from the passed pdu class.
            </remarks>
            <param name="pdu">Pdu class to use as source of information to initilize this class.</param>
        </member>
        <member name="M:SnmpSharpNet.Pdu.Set(SnmpSharpNet.AsnType)">
            <summary>
            Copy values from another Pdu class.
            </summary>
            <param name="value"><see cref="T:SnmpSharpNet.Pdu"/> cast as AsnType</param>
            <exception cref="T:System.ArgumentNullException">Thrown when received argument is null</exception>
        </member>
        <member name="M:SnmpSharpNet.Pdu.SetVbList(SnmpSharpNet.VbCollection)">
            <summary>
            Set VbList
            </summary>
            <remarks>
            Copy variable bindings from argument <see cref="T:SnmpSharpNet.VbCollection"/> into this classes variable 
            binding collection
            </remarks>
            <param name="value"><see cref="T:SnmpSharpNet.VbCollection"/> to copy variable bindings from</param>
        </member>
        <member name="M:SnmpSharpNet.Pdu.Reset">
            <summary>
            Reset VbList.
            </summary>
            <remarks>
            Remove all entries in the VbList collections.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.Pdu.GetPdu(SnmpSharpNet.VbCollection)">
            <summary>
            Create SNMP-GET Pdu from VbList
            </summary>
            <remarks>
            Helper static function to create GET PDU from the supplied VarBind list. Don't forget to set
            request id for the PDU.
            </remarks>
            <param name="vbs">VarBind list</param>
            <returns>Newly constructed GET PDU</returns>
        </member>
        <member name="M:SnmpSharpNet.Pdu.GetPdu">
            <summary>
            Create Get Pdu with empty VarBind array
            </summary>
            <returns>Instance of Get Pdu</returns>
        </member>
        <member name="M:SnmpSharpNet.Pdu.SetPdu(SnmpSharpNet.VbCollection)">
            <summary>
            Create SNMP-SET Pdu
            </summary>
            <remarks>
            Helper static function to create SET PDU from the supplied VarBind list. Don't forget to set
            request id for the PDU.
            </remarks>
            <param name="vbs">VarBind list</param>
            <returns>Newly constructed SET PDU</returns>
        </member>
        <member name="M:SnmpSharpNet.Pdu.SetPdu">
            <summary>
            Create Set Pdu with empty VarBind array
            </summary>
            <returns>Instance of Set Pdu</returns>
        </member>
        <member name="M:SnmpSharpNet.Pdu.GetNextPdu(SnmpSharpNet.VbCollection)">
            <summary>
            Create SNMP-GetNext Pdu
            </summary>
            <remarks>
            Helper static function to create GETNEXT PDU from the supplied VarBind list. Don't forget to set
            request id for the PDU.
            </remarks>
            <param name="vbs">VarBind list</param>
            <returns>Newly constructed GETNEXT PDU</returns>
        </member>
        <member name="M:SnmpSharpNet.Pdu.GetNextPdu">
            <summary>
            Create GetNext Pdu with empty VarBind array
            </summary>
            <returns>Instance of GetNext Pdu</returns>
        </member>
        <member name="M:SnmpSharpNet.Pdu.GetBulkPdu(SnmpSharpNet.VbCollection)">
            <summary>
            Create SNMP-GetBulk Pdu
            </summary>
            <remarks>
            Helper static function to create GETBULK PDU from the supplied VarBind list. MaxRepetitions are set to
            256 and nonRepeaters are set to 0.
            </remarks>
            <param name="vbs">VarBind list</param>
            <returns>Newly constructed GETBULK PDU</returns>
        </member>
        <member name="M:SnmpSharpNet.Pdu.GetBulkPdu">
            <summary>
            Create GetBulk Pdu with empty VarBind array. By default initializes NonRepeaters to 0 and MaxRepetitions to 100
            </summary>
            <returns>Instance of GetBulk Pdu</returns>
        </member>
        <member name="P:SnmpSharpNet.Pdu.ErrorStatus">
            <summary>
            ErrorStatus Pdu value
            </summary>
            <remarks>
            Stores error status returned by the SNMP agent. Value 0 represents no error. Valid for all
            Pdu types except GetBulk requests.
            </remarks>
            <exception cref="T:SnmpSharpNet.SnmpInvalidPduTypeException">Thrown when property is access for GetBulk Pdu</exception>
        </member>
        <member name="P:SnmpSharpNet.Pdu.ErrorIndex">
            <summary>
            ErrorIndex Pdu value
            </summary>
            <remarks>
            Error index points to the VbList entry that ErrorStatus error code refers to. Valid for all Pdu types
            except GetBulk requests.
            </remarks>
            <see cref="P:SnmpSharpNet.Pdu.ErrorStatus"/>
            <exception cref="T:SnmpSharpNet.SnmpInvalidPduTypeException">Thrown when property is access for GetBulk Pdu</exception>
        </member>
        <member name="P:SnmpSharpNet.Pdu.RequestId">
            <summary>
            SNMP packet request id that is sent to the SNMP agent. SET this value before making SNMP requests.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.Pdu.Type">
            <summary>
            Get or SET the PDU type. Available types are GET, GETNEXT, SET, GETBULK. PDU types are defined in Pdu class.
            </summary>
            <seealso cref="F:SnmpSharpNet.PduType.Get"/>
            <seealso cref="F:SnmpSharpNet.PduType.GetNext"/>
            <seealso cref="F:SnmpSharpNet.PduType.Set"/>
            <seealso cref="F:SnmpSharpNet.PduType.Response"/>"/>
            * version 2 specific:
            <seealso cref="F:SnmpSharpNet.PduType.GetBulk"/>
        </member>
        <member name="P:SnmpSharpNet.Pdu.MaxRepetitions">
            <summary>
            Tells SNMP Agent how many VBs to include in a single request. Only valid on GETBULK requests.
            </summary>
            <exception cref="T:SnmpSharpNet.SnmpInvalidPduTypeException">Thrown when PDU type is not GET-BULK</exception>
        </member>
        <member name="P:SnmpSharpNet.Pdu.NonRepeaters">
            <summary>
            Get/Set GET-BULK NonRepeaters value
            </summary>
            <remarks>
            Non repeaters variable tells the SNMP Agent how many GETNEXT like variables to retrieve (single Vb returned
            per request) before MaxRepetitions value takes affect. If you wish to retrieve as many values as you can
            in a single request, set this value to 0.
            </remarks>
            <exception cref="T:SnmpSharpNet.SnmpInvalidPduTypeException">Thrown when PDU type is not GET-BULK</exception>
        </member>
        <member name="P:SnmpSharpNet.Pdu.VbList">
            <summary>
            VarBind list
            </summary>
        </member>
        <member name="P:SnmpSharpNet.Pdu.TrapSysUpTime">
            <summary>
            Get TRAP TimeStamp class from SNMPv2 TRAP and INFORM PDUs
            </summary>
        </member>
        <member name="P:SnmpSharpNet.Pdu.TrapObjectID">
            <summary>
            Get TRAP ObjectID class from SNMPv2 TRAP and INFORM PDUs
            </summary>
            <exception cref="T:SnmpSharpNet.SnmpInvalidPduTypeException">Thrown when property is access for a Pdu of a type other then V2TRAP, INFORM or RESPONSE</exception>
        </member>
        <member name="M:SnmpSharpNet.Pdu.GetVb(System.Int32)">
            <summary>
            Get VB from VarBind list at the specified index
            </summary>
            <param name="index">Index position of the Vb in the array. Zero based.</param>
            <returns>Vb at the specified location in the array</returns>
        </member>
        <member name="P:SnmpSharpNet.Pdu.VbCount">
            <summary>
            Return the number of VB entries in the VarBind list
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Pdu.DeleteVb(System.Int32)">
            <summary>
            Delete VB from the specified location in the VarBind list
            </summary>
            <param name="pos">0 based VB location</param>
        </member>
        <member name="M:SnmpSharpNet.Pdu.encode(SnmpSharpNet.MutableByte)">
            <summary>
            Encode Pdu class to BER byte buffer
            </summary>
            <remarks>
            Encodes the protocol data unit using the passed encoder and stores
            the results in the passed buffer. An exception is thrown if an 
            error occurs with the encoding of the information. 
            </remarks>
            <param name="buffer">The buffer to write the encoded information.</param>
        </member>
        <member name="M:SnmpSharpNet.Pdu.decode(System.Byte[],System.Int32)">
            <summary>
            Decode BER encoded Pdu
            </summary>
            <remarks>
            Decodes the protocol data unit from the passed buffer. If an error
            occurs during the decoding sequence then an AsnDecodingException is
            thrown by the method. The value is decoded using the AsnEncoder
            passed to the object.
            </remarks>
            <param name="buffer">BER encoded buffer</param>
            <param name="offset">The offset byte to begin decoding</param>
            <returns>Buffer position after the decoded value</returns>
            <exception cref="T:System.OverflowException">Thrown when header points to more data then is available.</exception>
        </member>
        <member name="M:SnmpSharpNet.Pdu.ToString">
            <summary>
            Return string dump of the Pdu class.
            </summary>
            <returns>String content of the Pdu class.</returns>
        </member>
        <member name="M:SnmpSharpNet.Pdu.Clone">
            <summary>
            Clone this object
            </summary>
            <returns>Copy of this object cast as type System.Object</returns>
        </member>
        <member name="M:SnmpSharpNet.Pdu.Equals(System.Object)">
            <summary>
            Check class equality with argument.
            
            Accepted argument types are:
            * Integer32 - compared against the request id
            * Pdu - compared against PduType, request id and contents of VarBind list
            </summary>
            <param name="obj">Integer32 or Pdu to compare</param>
            <returns>True if equal, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.Pdu.GetHashCode">
            <summary>
            Returns hash code representing class value.
            </summary>
            <returns>Class value hash code</returns>
        </member>
        <member name="P:SnmpSharpNet.Pdu.Item(System.Int32)">
            <summary>
            Indexed access to VarBind collection of the Pdu.
            </summary>
            <param name="index">Index position of the VarBind entry</param>
            <returns>VarBind entry at the specified index</returns>
            <exception cref="T:System.IndexOutOfRangeException">Thrown when index is outside the bounds of the collection</exception>
        </member>
        <member name="P:SnmpSharpNet.Pdu.Item(SnmpSharpNet.Oid)">
            <summary>
            Access variable bindings using Vb Oid value
            </summary>
            <param name="oid">Required Oid value</param>
            <returns>Variable binding with the Oid matching the parameter, otherwise null</returns>
        </member>
        <member name="P:SnmpSharpNet.Pdu.Item(System.String)">
            <summary>
            Access variable bindings using Vb Oid value in the string format
            </summary>
            <param name="oid">Oid value in string representation</param>
            <returns>Variable binding with the Oid matching the parameter, otherwise null</returns>
        </member>
        <member name="M:SnmpSharpNet.Pdu.GetEnumerator">
            <summary>
            Get VarBind collection enumerator.
            </summary>
            <returns>Enumerator</returns>
        </member>
        <member name="M:SnmpSharpNet.Pdu.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Get VarBind collection enumerator.
            </summary>
            <returns>Enumerator</returns>
        </member>
        <member name="T:SnmpSharpNet.SnmpErrorStatusException">
            <summary>
            Exception thrown by <see cref="T:SnmpSharpNet.SimpleSnmp"/> methods when SNMP request returned a SnmpStatus error in the reply and
            SuppressExceptions flag is set to false.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpErrorStatusException._errorStatus">
            <summary>
            SNMP reply ErrorStatus value
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpErrorStatusException._errorIndex">
            <summary>
            SNMP reply ErrorIndex value
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpErrorStatusException.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpErrorStatusException.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
            Constructor
            </summary>
            <param name="msg">Exception message</param>
            <param name="status">ErrorStatus value</param>
            <param name="index">ErrorIndex value</param>
        </member>
        <member name="P:SnmpSharpNet.SnmpErrorStatusException.ErrorStatus">
            <summary>
            Get/Set SNMP ErrorStatus value
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpErrorStatusException.ErrorIndex">
            <summary>
            Get/Set SNMP ErrorIndex value
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpErrorStatusException.Message">
            <summary>
            Get exception message
            </summary>
        </member>
        <member name="T:SnmpSharpNet.SnmpPacket">
            <summary>Base SNMP packet class.</summary>
            <remarks>
            All SNMP packets begin with the SMI_SEQUENCE header and SNMP protocol version number.
            This class parses and encodes these values. Derived classes parse further information from SNMP packets.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.SnmpPacket._protocolVersion">
            <summary>
            SNMP protocol version
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpPacket.Version">
            <summary>
            SNMP Protocol version
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpPacket.Pdu">
            <summary>
            Get Pdu
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpPacket.#ctor">
            <summary>
            Constructor. Sets SNMP version to SNMPV1.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpPacket.#ctor(SnmpSharpNet.SnmpVersion)">
            <summary>
            Constructor. Initialize SNMP version as supplied.
            </summary>
            <param name="protocolVersion">Protocol version. Acceptable values are SnmpConstants.SNMPV1, 
            SnmpConstants.SNMPV2 and SnmpConstants.SNMPV3</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpPacket.decode(System.Byte[],System.Int32)">
            <summary>
            Decode SNMP packet header. This class decodes the initial sequence and SNMP protocol version
            number.
            </summary>
            <param name="buffer">BER encoded SNMP packet</param>
            <param name="length">Packet length</param>
            <returns>Offset position after the initial sequence header and protocol version value</returns>
            <exception cref="T:SnmpSharpNet.SnmpDecodingException">Thrown when invalid sequence type is found at the start of the SNMP packet being decoded</exception>
        </member>
        <member name="M:SnmpSharpNet.SnmpPacket.encode">
            <summary>
            Place holder for derived class implementations.
            </summary>
            <returns>Nothing</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpPacket.encode(SnmpSharpNet.MutableByte)">
            <summary>
            Wrap BER encoded SNMP information contained in the parameter <see cref="T:SnmpSharpNet.MutableByte"/> class.
            
            Information in the parameter is prepended by the SNMP version field and wrapped in a sequence header.
            
            Derived classes call this method to finalize SNMP packet encoding.
            </summary>
            <param name="buffer">Buffer containing BER encoded SNMP information</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpPacket.GetProtocolVersion(System.Byte[],System.Int32)">
            <summary>
            Get SNMP protocol version from the packet. This routine does not verify if version number is valid. Caller
            should verify that returned value represents a valid SNMP protocol version number.
            
            <code>
            int protocolVersion = Packet.GetProtocolVersion(inPacket, inLength);
            if( protocolVersion != -1 )
            {
                if( protocolVersion == SnmpConstants.SNMPV1 || protocolVersion == SnmpConstants.SNMPV2 || protocolVersion == SnmpConstants.SNMPV3 )
                {
                    // do something
                }
                else
                {
                    Console.WriteLine("Invalid SNMP protocol version.");
                }
            }
            else
            {
                Console.WriteLine("Invalid SNMP packet.");
            }
            </code>
            </summary>
            <param name="buffer">BER encoded SNMP packet</param>
            <param name="bufferLength">Length of the BER encoded packet</param>
            <returns>Returns SNMP protocol version, if packet is not valid returned value is -1.</returns>
            <exception cref="T:SnmpSharpNet.SnmpDecodingException">Thrown when invalid sequence type is found at the start of the SNMP packet being decoded</exception>
        </member>
        <member name="P:SnmpSharpNet.SnmpPacket.IsReport">
            <summary>
            Packet is a report
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpPacket.IsRequest">
            <summary>
            Packet is a request
            </summary>
            <remarks>
            Checks if the class content is a SNMP Get, GetNext, GetBulk or Set request.
            </remarks>
        </member>
        <member name="P:SnmpSharpNet.SnmpPacket.IsResponse">
            <summary>
            Packet is a response
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpPacket.IsNotification">
            <summary>
            Packet is a notification
            </summary>
        </member>
        <member name="T:SnmpSharpNet.SnmpV1Packet">
            <summary>
            SNMP version 1 packet class.
            </summary>
            <remarks>
            Supported request types are SNMP-GET, SNMP-GETNEXT, SNMP-SET and SNMP-RESPONSE.
            
            Available packet classes are:
            <ul>
            <li><see cref="T:SnmpSharpNet.SnmpV1Packet"/></li>
            <li><see cref="T:SnmpSharpNet.SnmpV1TrapPacket"/></li>
            <li><see cref="T:SnmpSharpNet.SnmpV2Packet"/></li>
            <li><see cref="T:SnmpSharpNet.SnmpV3Packet"/></li>
            </ul>
            
            This class is provided to simplify encoding and decoding of packets and to provide consistent interface
            for users who wish to handle transport part of protocol on their own without using the <see cref="T:SnmpSharpNet.UdpTarget"/> 
            class.
            
            <see cref="T:SnmpSharpNet.SnmpPacket"/> and derived classes have been developed to implement SNMP version 1, 2 and 3 packet 
            support. 
            
            For SNMP version 1 and 2 packet, <see cref="T:SnmpSharpNet.SnmpV1Packet"/> and <see cref="T:SnmpSharpNet.SnmpV2Packet"/> classes 
            provides  sufficient support for encoding and decoding data to/from BER buffers to satisfy requirements 
            of most applications. 
            
            SNMP version 3 on the other hand requires a lot more information to be passed to the encoder method and 
            returned by the decode method. While using SnmpV3Packet class for full packet handling is possible, transport
            specific class <see cref="T:SnmpSharpNet.UdpTarget"/> uses <see cref="T:SnmpSharpNet.SecureAgentParameters"/> class to store protocol
            version 3 specific information that carries over from request to request when used on the same SNMP agent
            and therefore simplifies both initial definition of agents configuration (mostly security) as well as
            removes the need for repeated initialization of the packet class for subsequent requests.
            
            If you decide not to use transport helper class(es) like <see cref="T:SnmpSharpNet.UdpTarget"/>, BER encoding and
            decoding and packets is easily done with SnmpPacket derived classes.
            
            Example, SNMP version 1 packet encoding:
            <code>
            SnmpV1Packet packetv1 = new SnmpV1Packet();
            packetv1.Community.Set("public");
            packetv1.Pdu.Set(mypdu);
            byte[] berpacket = packetv1.encode();
            </code>
            
            Example, SNMP version 1 packet decoding:
            <code>
            SnmpV1Packet packetv1 = new SnmpV1Packet();
            packetv1.decode(inbuffer,inlen);
            </code>
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.SnmpV1Packet.#ctor">
            <summary>
            Standard constructor.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpV1Packet.#ctor(System.String)">
            <summary>
            Standard constructor.
            </summary>
            <param name="snmpCommunity">SNMP community name for the packet</param>
        </member>
        <member name="F:SnmpSharpNet.SnmpV1Packet._snmpCommunity">
            <summary>
            SNMP community name
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV1Packet.Community">
            <summary>
            Get SNMP community value used by SNMP version 1 and version 2 protocols.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpV1Packet._pdu">
            <summary>
            SNMP Protocol Data Unit
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV1Packet.Pdu">
            <summary>
            Access to the packet <see cref="P:SnmpSharpNet.SnmpV1Packet.Pdu"/>.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpV1Packet.decode(System.Byte[],System.Int32)">
            <summary>
            Decode received SNMP packet.
            </summary>
            <param name="buffer">BER encoded packet buffer</param>
            <param name="length">BER encoded packet buffer length</param>
            <returns>Buffer position after the decoded packet.</returns>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown when invalid encoding has been found in the packet</exception>
            <exception cref="T:System.OverflowException">Thrown when parsed header points to more data then is available in the packet</exception>
            <exception cref="T:SnmpSharpNet.SnmpInvalidVersionException">Thrown when parsed packet is not SNMP version 1</exception>
            <exception cref="T:SnmpSharpNet.SnmpInvalidPduTypeException">Thrown when received PDU is of a type not supported by SNMP version 1</exception>
        </member>
        <member name="M:SnmpSharpNet.SnmpV1Packet.encode(SnmpSharpNet.MutableByte)">
            <summary>
            Replacement for the base class encode method.  
            </summary>
            <param name="buffer">Buffer</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpV1Packet.encode">
            <summary>
            Encode SNMP packet for sending.
            </summary>
            <returns>BER encoded SNMP packet.</returns>
            <exception cref="T:SnmpSharpNet.SnmpInvalidPduTypeException">Thrown when PDU being encoded is not a valid SNMP version 1 PDU. Acceptable 
            protocol version 1 operations are GET, GET-NEXT, SET and RESPONSE.</exception>
        </member>
        <member name="M:SnmpSharpNet.SnmpV1Packet.ToString">
            <summary>
            String representation of the SNMP v1 Packet contents.
            </summary>
            <returns>String representation of the class.</returns>
        </member>
        <member name="T:SnmpSharpNet.SnmpV1TrapPacket">
            <summary>SNMP version 1 TRAP packet class.</summary>
            <remarks>
            Available packet classes are:
            <ul>
            <li><see cref="T:SnmpSharpNet.SnmpV1Packet"/></li>
            <li><see cref="T:SnmpSharpNet.SnmpV1TrapPacket"/></li>
            <li><see cref="T:SnmpSharpNet.SnmpV2Packet"/></li>
            <li><see cref="T:SnmpSharpNet.SnmpV3Packet"/></li>
            </ul>
            
            This class is provided to simplify encoding and decoding of packets and to provide consistent interface
            for users who wish to handle transport part of protocol on their own without using the <see cref="T:SnmpSharpNet.UdpTarget"/> 
            class.
            
            <see cref="T:SnmpSharpNet.SnmpPacket"/> and derived classes have been developed to implement SNMP packet support. For
            SNMP version 1 and 2 packet, <see cref="T:SnmpSharpNet.SnmpV1Packet"/> and <see cref="T:SnmpSharpNet.SnmpV2Packet"/> classes provides 
            sufficient support for encoding and decoding data to/from BER buffers to satisfy requirements of most 
            applications. 
            
            SNMP version 3 on the other hand requires a lot more information to be passed to the encoder method and 
            returned by the decode method. Attempt of implementing SNMP version 3 as part of <see cref="T:SnmpSharpNet.SnmpV3Packet"/> 
            class was operational but required too many function arguments to operate so a different interface was 
            developed using dedicated <see cref="T:SnmpSharpNet.SnmpV3Packet"/> class. 
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.SnmpV1TrapPacket._pdu">
            <summary>
            SNMP Protocol Data Unit
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV1TrapPacket.Pdu">
            <summary>
            Access to the packet <see cref="P:SnmpSharpNet.SnmpV1TrapPacket.TrapPdu"/>.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV1TrapPacket.TrapPdu">
            <summary>
            Get TrapPdu
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpV1TrapPacket._snmpCommunity">
            <summary>
            SNMP community name
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV1TrapPacket.Community">
            <summary>
            Get SNMP community value used by SNMP version 1 and version 2 protocols.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpV1TrapPacket.#ctor">
            <summary>
            Standard constructor.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpV1TrapPacket.#ctor(System.String)">
            <summary>
            Standard constructor.
            </summary>
            <param name="snmpCommunity">SNMP community name for the packet</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpV1TrapPacket.decode(System.Byte[],System.Int32)">
            <summary>
            Decode received packet. This method overrides the base implementation that cannot be used with this type of the packet.
            </summary>
            <param name="buffer">Packet buffer</param>
            <param name="length">Buffer length</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpV1TrapPacket.encode">
            <summary>
            Encode SNMP packet for sending.
            </summary>
            <returns>BER encoded SNMP packet.</returns>
        </member>
        <member name="T:SnmpSharpNet.SnmpV2Packet">
            <summary>
            SNMP version 2 packet class.
            </summary>
            
            <remarks>
            Available packet classes are:
            <ul>
            <li><see cref="T:SnmpSharpNet.SnmpV1Packet"/></li>
            <li><see cref="T:SnmpSharpNet.SnmpV1TrapPacket"/></li>
            <li><see cref="T:SnmpSharpNet.SnmpV2Packet"/></li>
            <li><see cref="T:SnmpSharpNet.SnmpV3Packet"/></li>
            </ul>
            
            This class is provided to simplify encoding and decoding of packets and to provide consistent interface
            for users who wish to handle transport part of protocol on their own without using the <see cref="T:SnmpSharpNet.UdpTarget"/> 
            class.
            
            <see cref="T:SnmpSharpNet.SnmpPacket"/> and derived classes have been developed to implement SNMP version 1, 2 and 3 packet 
            support. 
            
            For SNMP version 1 and 2 packet, <see cref="T:SnmpSharpNet.SnmpV1Packet"/> and <see cref="T:SnmpSharpNet.SnmpV2Packet"/> classes 
            provides  sufficient support for encoding and decoding data to/from BER buffers to satisfy requirements 
            of most applications. 
            
            SNMP version 3 on the other hand requires a lot more information to be passed to the encoder method and 
            returned by the decode method. While using SnmpV3Packet class for full packet handling is possible, transport
            specific class <see cref="T:SnmpSharpNet.UdpTarget"/> uses <see cref="T:SnmpSharpNet.SecureAgentParameters"/> class to store protocol
            version 3 specific information that carries over from request to request when used on the same SNMP agent
            and therefore simplifies both initial definition of agents configuration (mostly security) as well as
            removes the need for repeated initialization of the packet class for subsequent requests.
            
            If you decide not to use transport helper class(es) like <see cref="T:SnmpSharpNet.UdpTarget"/>, BER encoding and
            decoding and packets is easily done with SnmpPacket derived classes.
            
            Example, SNMP version 2 packet encoding:
            <code>
            SnmpV2Packet packetv2 = new SnmpV2Packet();
            packetv2.Community.Set("public");
            packetv2.Pdu.Set(mypdu);
            byte[] berpacket = packetv2.encode();
            </code>
            
            Example, SNMP version 2 packet decoding:
            <code>
            SnmpV2Packet packetv2 = new SnmpV2Packet();
            packetv2.decode(inbuffer,inlen);
            </code>
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.SnmpV2Packet._snmpCommunity">
            <summary>
            SNMP community name
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV2Packet.Community">
            <summary>
            Get SNMP community value used by SNMP version 1 and version 2 protocols.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpV2Packet._pdu">
            <summary>
            SNMP Protocol Data Unit
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV2Packet.Pdu">
            <summary>
            Access to the packet <see cref="P:SnmpSharpNet.SnmpV2Packet.Pdu"/>.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpV2Packet.#ctor">
            <summary>
            Standard constructor.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpV2Packet.#ctor(System.String)">
            <summary>
            Standard constructor.
            </summary>
            <param name="snmpCommunity">SNMP community name for the packet</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpV2Packet.decode(System.Byte[],System.Int32)">
            <summary>
            Decode received SNMP packet.
            </summary>
            <param name="buffer">BER encoded packet buffer</param>
            <param name="length">BER encoded packet buffer length</param>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown when invalid encoding has been found in the packet</exception>
            <exception cref="T:System.OverflowException">Thrown when parsed header points to more data then is available in the packet</exception>
            <exception cref="T:SnmpSharpNet.SnmpInvalidVersionException">Thrown when parsed packet is not SNMP version 1</exception>
            <exception cref="T:SnmpSharpNet.SnmpInvalidPduTypeException">Thrown when received PDU is of a type not supported by SNMP version 1</exception>
        </member>
        <member name="M:SnmpSharpNet.SnmpV2Packet.encode">
            <summary>
            Encode SNMP packet for sending.
            </summary>
            <returns>BER encoded SNMP packet.</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpV2Packet.BuildInformResponse">
            <summary>
            Build SNMP RESPONSE packet for the received INFORM packet.
            </summary>
            <returns>SNMP version 2 packet containing RESPONSE to the INFORM packet contained in the class instance.</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpV2Packet.BuildInformResponse(SnmpSharpNet.SnmpV2Packet)">
            <summary>
            Build SNMP RESPONSE packet for the INFORM packet class.
            </summary>
            <param name="informPacket">SNMP INFORM packet</param>
            <returns>SNMP version 2 packet containing RESPONSE to the INFORM packet contained in the parameter.</returns>
            <exception cref="T:SnmpSharpNet.SnmpInvalidPduTypeException">Parameter is not an INFORM SNMP version 2 packet class</exception>
            <exception cref="T:SnmpSharpNet.SnmpInvalidVersionException">Parameter is not a SNMP version 2 packet</exception>
        </member>
        <member name="M:SnmpSharpNet.SnmpV2Packet.ToString">
            <summary>
            String representation of the SNMP v1 Packet contents.
            </summary>
            <returns>String representation of the class.</returns>
        </member>
        <member name="T:SnmpSharpNet.SnmpV3Packet">
             <summary>
             SNMP version 3 packet implementation class.
             </summary>
             
             <remarks>
             Available packet classes are:
             <ul>
             <li><see cref="T:SnmpSharpNet.SnmpV1Packet"/></li>
             <li><see cref="T:SnmpSharpNet.SnmpV1TrapPacket"/></li>
             <li><see cref="T:SnmpSharpNet.SnmpV2Packet"/></li>
             <li><see cref="T:SnmpSharpNet.SnmpV3Packet"/></li>
             </ul>
             
             This class is provided to simplify encoding and decoding of packets and to provide consistent interface
             for users who wish to handle transport part of protocol on their own without using the <see cref="T:SnmpSharpNet.UdpTarget"/> 
             class.
             
             <see cref="T:SnmpSharpNet.SnmpPacket"/> and derived classes have been developed to implement SNMP version 1, 2 and 3 packet 
             support. 
             
             For SNMP version 1 and 2 packet, <see cref="T:SnmpSharpNet.SnmpV1Packet"/> and <see cref="T:SnmpSharpNet.SnmpV2Packet"/> classes 
             provide sufficient support for encoding and decoding data to/from BER buffers to satisfy requirements 
             of most applications. 
             
             SNMP version 3 on the other hand requires a lot more information to be passed to the encoder method and 
             returned by the decode method. While using SnmpV3Packet class for full packet handling is possible, transport
             specific class <see cref="T:SnmpSharpNet.UdpTarget"/> uses <see cref="T:SnmpSharpNet.SecureAgentParameters"/> class to store protocol
             version 3 specific information that carries over from request to request when used on the same SNMP agent
             and therefore simplifies both initial definition of agents configuration (mostly security) as well as
             removes the need for repeated initialization of the packet class for subsequent requests.
             
             If you decide not to use transport helper class(es) like <see cref="T:SnmpSharpNet.UdpTarget"/>, BER encoding and
             decoding and packets is easily done with SnmpPacket derived classes.
             
             Example, SNMP version 1 packet encoding:
             <code>
             SnmpV1Packet packetv1 = new SnmpV1Packet();
             packetv1.Community.Set("public");
             packetv1.Pdu.Set(mypdu);
             byte[] berpacket = packetv1.encode();
             </code>
             
             Example, SNMP version 3 noAuthNoPriv encoding:
             <code>
             SnmpV3Packet packetv3 = new SnmpV3Packet();
             packetv3.noAuthNoPriv("myusername");
             packetv3.SetEngineTime(engineTime, engineBoots); // See SNMPv3 discovery process for details
             packetv3.SetEngineId(engineId); // See SNMPv3 discovery process for details
             packetv3.IsReportable = true;
             packetv3.Pdu.Set(mypdu);
             byte[] berpacket = packetv3.encode();
             </code>
            
             Example, SNMP version 3 authNoPriv using MD5 authentication packet encoding:
             <code>
             SnmpV3Packet packetv3 = new SnmpV3Packet();
             packetv3.authNoPriv("myusername", "myAuthenticationPassword", AuthenticationDigests.MD5);
             packetv3.SetEngineTime(engineTime, engineBoots); // See SNMPv3 discovery process for details
             packetv3.SetEngineId(engineId); // See SNMPv3 discovery process for details
             packetv3.IsReportable = true;
             packetv3.Pdu.Set(mypdu);
             byte[] berpacket = packetv3.encode();
             </code>
            
             Example, SNMP version 3 authPriv using MD5 authentication and DES encryption packet encoding:
             <code>
             SnmpV3Packet packetv3 = new SnmpV3Packet();
             packetv3.authPriv("myusername", "myAuthenticationPassword", AuthenticationDigests.MD5,
            		"myPrivacyPassword", PrivacyProtocols.DES);
             packetv3.SetEngineTime(engineTime, engineBoots); // See SNMPv3 discovery process for details
             packetv3.SetEngineId(engineId); // See SNMPv3 discovery process for details
             packetv3.IsReportable = true;
             packetv3.Pdu.Set(mypdu);
             byte[] berpacket = packetv3.encode();
             </code>
             
             When decoding SNMP version 3 packets, SnmpV3Packet class needs to be initialized with the same values
             security values as a request does. This includes, authoritative engine id, engine boots and engine time,
             if authentication is used, authentication digest and password and for encryption, password and privacy
             protocol used. Without these parameters packet class will not be able to verify the incoming packet and
             responses will be discarded even if they are valid.
             </remarks>
        </member>
        <member name="F:SnmpSharpNet.SnmpV3Packet._messageId">
            <summary>
            SNMP version 3 message id. Uniquly identifies the message.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV3Packet.MessageId">
            <summary>
            Get SNMP version 3 message id object.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpV3Packet._maxMessageSize">
            <summary>
            Maximum message size. In the discovery packet, set it to the maximum acceptable size = 64KB. Agent will
            return the maximum value it is ready to handle so you should stick with that value in all following
            requests.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV3Packet.MaxMessageSize">
            <summary>
            Get maximum message size to be sent to the agent in the request.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpV3Packet._msgFlags">
            <summary>
            Message flags field in the SNMP v3 packet. Message flag field defined Authentication (0/1), Privacy (0/1) and
            Reportable (0/1) fields. Each bit is 1 bit long and with 0 denoting disabled feature, 1 enabled feature.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV3Packet.MsgFlags">
            <summary>
            Message flags interface. Allows you to directly set or clear SNMP version 3 header flags field.
            
            Available flags are MsgFlags.Authentication, MsgFlags.Privacy and MsgFlags.Reportable.
            
            Please be careful how you use this property. After setting authentication or privacy parameters to true,
            you will need to update <see cref="T:SnmpSharpNet.UserSecurityModel"/> authentication and privacy types to the correct
            values otherwise encoding/decoding will not work.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpV3Packet._securityModel">
            <summary>
            Security model code. Only supported security model is UserSecurityModel (integer value 3)
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpV3Packet._userSecurityModel">
            <summary>
            Internal <see cref="T:SnmpSharpNet.UserSecurityModel"/> class.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV3Packet.USM">
            <summary>
            Get <see cref="T:SnmpSharpNet.UserSecurityModel"/> class reference.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpV3Packet._scopedPdu">
            <summary>
            Scoped PDU class.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV3Packet.Pdu">
            <summary>
            Override base class implementation. Returns class ScopedPdu cast as Pdu
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV3Packet.ScopedPdu">
            <summary>
            Access packet ScopedPdu class.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.#ctor">
            <summary>
            Standard constructor.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.#ctor(SnmpSharpNet.ScopedPdu)">
            <summary>
            Constructor.
            </summary>
            <remarks>
            Sets internal ScopedPdu class to the argument supplied instance of the
            class. This is a good cheat that will allow you direct access to the internal ScopedPdu class
            since it is not cloned but assigned to the internal variable.
            </remarks>
            <param name="pdu"><see cref="P:SnmpSharpNet.SnmpV3Packet.ScopedPdu"/> class assigned to the class</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.#ctor(SnmpSharpNet.SecureAgentParameters)">
            <summary>
            Constructor.
            </summary>
            <remarks>
            Create new SNMPv3 packet class and initialize security parameters
            </remarks>
            <param name="param">Initialization SNMPv3 security parameters</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.#ctor(SnmpSharpNet.SecureAgentParameters,SnmpSharpNet.ScopedPdu)">
            <summary>
            Constructor
            </summary>
            <remarks>
            Create new SNMPv3 packet class and initialize security parameters and ScopedPdu.
            </remarks>
            <param name="param">SNMPv3 security parameters</param>
            <param name="pdu">ScopedPdu assigned to the class</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.NoAuthNoPriv">
            <summary>
            Set class security to no authentication and no privacy. User name is set to "initial" (suitable for
            SNMP version 3 discovery process). Change username before using if discovery is not being performed.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.NoAuthNoPriv(System.Byte[])">
            <summary>
            Set class security to no authentication and no privacy with the specific user name.
            </summary>
            <param name="userName">User name</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.authNoPriv(System.Byte[],System.Byte[],SnmpSharpNet.AuthenticationDigests)">
            <summary>
            Set class security to enabled authentication and no privacy. To perform authentication,
            authentication password needs to be supplied and authentication protocol to be used
            to perform authentication.
            
            This method does not initialize the packet user name. Use SNMPV3Packet.SecurityName
            method to set the security name (also called user name) for this request.
            </summary>
            <param name="userName">User name</param>
            <param name="authenticationPassword">Authentication password to use in authenticating the message. This
            value has to match the password configured on the agent.</param>
            <param name="authenticationProtocol">Authentication protocol to use. Available authentication protocols are:
            <see cref="F:SnmpSharpNet.AuthenticationDigests.MD5"/> for HMAC-MD5 authentication, and <see cref="F:SnmpSharpNet.AuthenticationDigests.SHA1"/>
            for HMAC-SHA1 message authentication.</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.authPriv(System.Byte[],System.Byte[],SnmpSharpNet.AuthenticationDigests,System.Byte[],SnmpSharpNet.PrivacyProtocols)">
            <summary>
            Set packet security to authentication enabled and privacy protection enabled (SNMP v3 mode authPriv)
            </summary>
            <param name="userName">User name</param>
            <param name="authenticationPassword">Authentication password</param>
            <param name="authenticationProtocol">Authentication protocol. See definitions in <see cref="T:SnmpSharpNet.AuthenticationDigests"/> enumeration.</param>
            <param name="privacyPassword">Privacy protection password.</param>
            <param name="privacyProtocol">Privacy protocol. See definitions in <see cref="T:SnmpSharpNet.PrivacyProtocols"/> enumeration.</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.SetEngineTime(System.Int32,System.Int32)">
            <summary>
            Set engine time and boots values
            </summary>
            <param name="engineBoots">Authoritative engine boots value retrived from the agent during discovery procedure.</param>
            <param name="engineTime">Engine time value.</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.SetEngineId(System.Byte[])">
            <summary>
            Set authoritative engine id
            </summary>
            <param name="engineId">Authoritative engine id</param>
        </member>
        <member name="P:SnmpSharpNet.SnmpV3Packet.IsReportable">
            <summary>
            Get or set SNMP version 3 packet Reportable flag in the message flags section. By default this value is set to true.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.SnmpV3Packet.IsDiscoveryPacket">
            <summary>
            Packet is a discovery request
            </summary>
            <remarks>
            Class checks if Engine id, engine boots and engine time values are set to default values (null, 0 and 0). If they are
            packet is probably a discovery packet, otherwise it is not an false is returned
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.GetUSM(System.Byte[],System.Int32)">
            <summary>
            "Look-ahead" decode of SNMP packet header including USM information
            </summary>
            <remarks>
            Decode first component of the SNMP version 3 packet allowing the caller to retrieve USM SecureName needed to retrieve
            client security parameters that will allow authentication and privacy decryption to take place.
            
            This method is used to support Agent like behavior or to handle unsolicited packets like TRAP and INFORMs. In all of
            these cases, sender of packets will forward a packet without a request being sent by you. In turn, you will need
            to parse enough of the packet to retrieve SecureName which you can use to retrieve security parameters associated with
            that user and attempt to authorize and privacy decrypt the received packet.
            
            Only use this method when your application is acting as an Agent or if you need to process TRAP and INFORM packets.
            </remarks>
            <param name="berBuffer">Raw SNMP version 3 packet</param>
            <param name="length">SNMP version 3 packet length</param>
            <returns>UserSecurityModel class parsed from the parameter SNMP version 3 packet</returns>
            <exception cref="T:SnmpSharpNet.SnmpInvalidVersionException">Thrown when attempting to parse an SNMP packet that is not version 3</exception>
            <exception cref="T:System.OverflowException">Thrown when header specifies packet length that is longer then the amount of data received.</exception>
            <exception cref="T:SnmpSharpNet.SnmpDecodingException">Thrown when invalid sequence is enountered while decoding global message data sequence</exception>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown with SnmpException.UnsupportedNoAuthPriv when packet is using privacy without authentication (not allowed)</exception>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown with SnmpException.UnsupportedSecurityModel when packet is sent with security model other then USM (only USM is defined in SNMPv3 standard)</exception>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.decode(System.Byte[],System.Int32)">
            <summary>
            Decode SNMP version 3 packet. This method will perform authentication check and decode privacy protected <see cref="P:SnmpSharpNet.SnmpV3Packet.ScopedPdu"/>. This method will
            not check for the timeliness of the packet, correct engine boot value or engine id because it does not have a reference to the engine time prior to this call.
            </summary>
            <param name="berBuffer">BER encoded SNMP version 3 packet buffer</param>
            <param name="length">Buffer length</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.decode(System.Byte[],System.Int32,System.Byte[],System.Byte[])">
            <summary>
            Decode SNMP version 3 packet. This method will perform authentication check and decode privacy protected <see cref="P:SnmpSharpNet.SnmpV3Packet.ScopedPdu"/>. This method will
            not check for the timeliness of the packet, correct engine boot value or engine id because it does not have a reference to the engine time prior to this call.
            </summary>
            <param name="berBuffer">BER encoded SNMP version 3 packet buffer</param>
            <param name="length">Buffer length</param>
            <param name="authKey">Authentication key (not password)</param>
            <param name="privKey">Privacy key (not password)</param>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.encode">
            <summary>
            Encode SNMP version 3 packet
            </summary>
            <remarks>
            Before encoding the packet into a byte array you need to ensure all required information is
            set. Examples of required information is request type, Vbs (Oid + values pairs), USM settings including
            SecretName, authentication method and secret (if needed), privacy method and secret (if needed), etc.
            </remarks>
            <returns>Byte array BER encoded SNMP packet.</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.encode(System.Byte[],System.Byte[])">
            <summary>
            Encode SNMP version 3 packet
            </summary>
            <param name="authKey">Authentication key (not password)</param>
            <param name="privKey">Privacy key (not password)</param>
            <remarks>
            Before encoding the packet into a byte array you need to ensure all required information is
            set. Examples of required information is request type, Vbs (Oid + values pairs), USM settings including
            SecretName, authentication method and secret (if needed), privacy method and secret (if needed), etc.
            </remarks>
            <returns>Byte array BER encoded SNMP packet.</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.GenerateAuthenticationKey">
            <summary>
            Generate authentication key from authentication password and engine id
            </summary>
            <returns>Authentication key on success or null on failure</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.GeneratePrivacyKey">
            <summary>
            Generate privacy key from authentication password and engine id
            </summary>
            <returns>Privacy key on success or null on failure</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.DiscoveryRequest">
            <summary>
            Build an SNMP version 3 packet suitable for use in discovery process.
            </summary>
            <returns>Discovery process prepared SNMP version 3 packet.</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.DiscoveryResponse(System.Int32,System.Int32,SnmpSharpNet.OctetString,System.Int32,System.Int32,System.Int32)">
            <summary>
            Build SNMP discovery response packet.
            </summary>
            <remarks>
            Manager application has to be able to respond to discovery requests to be able to handle
            SNMPv3 INFORM notifications.
            
            In an INFORM packet, engineId value is set to the manager stations id (unlike all other requests
            where agent is the authoritative SNMP engine). For the agent to discover appropriate manager engine
            id, boots and time values (required for authentication and privacy packet handling), manager has to
            be able to respond to the discovery request.
            </remarks>
            <param name="messageId">Message id from the received discovery packet</param>
            <param name="requestId">Request id from the received discovery packets Pdu</param>
            <param name="engineId">Local engine id</param>
            <param name="engineBoots">Number of times local SNMP engine has been restarted</param>
            <param name="engineTime">Time since the engine was started in seconds</param>
            <param name="unknownEngineIdCount">Number of discovery packets received by the local SNMP engine</param>
            <returns>SNMP v3 packet properly formatted as a response to a discovery request</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.BuildInformResponse">
            <summary>
            Build SNMP RESPONSE packet for the received INFORM packet.
            </summary>
            <returns>SNMP version 3 packet containing RESPONSE to the INFORM packet contained in the class instance.</returns>
        </member>
        <member name="M:SnmpSharpNet.SnmpV3Packet.BuildInformResponse(SnmpSharpNet.SnmpV3Packet)">
            <summary>
            Build SNMP RESPONSE packet for the INFORM packet class.
            </summary>
            <param name="informPacket">SNMP INFORM packet</param>
            <returns>SNMP version 3 packet containing RESPONSE to the INFORM packet contained in the parameter.</returns>
            <exception cref="T:SnmpSharpNet.SnmpInvalidPduTypeException">Parameter is not an INFORM SNMP version 3 packet class</exception>
            <exception cref="T:SnmpSharpNet.SnmpInvalidVersionException">Parameter is not a SNMP version 3 packet</exception>
        </member>
        <member name="T:SnmpSharpNet.AuthenticationMD5">
            <summary>
            MD5 Authentication class.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationMD5.#ctor">
            <summary>
            Standard constructor
            </summary>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationMD5.authenticate(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            Authenticate packet and return authentication parameters value to the caller
            </summary>
            <param name="authenticationSecret">User authentication secret</param>
            <param name="engineId">SNMP agent authoritative engine id</param>
            <param name="wholeMessage">Message to authenticate</param>
            <returns>Authentication parameters value</returns>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationMD5.authenticate(System.Byte[],System.Byte[])">
            <summary>
            Authenticate packet and return authentication parameters value to the caller
            </summary>
            <param name="authKey">Pre-generated authentication key</param>
            <param name="wholeMessage">Message being authenticated</param>
            <returns>Authentication parameters value</returns>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationMD5.authenticateIncomingMsg(System.Byte[],System.Byte[],System.Byte[],SnmpSharpNet.MutableByte)">
            <summary>
            Verifies correct MD5 authentication of the frame. Prior to calling this method, you have to extract authentication
            parameters from the wholeMessage and reset authenticationParameters field in the USM information block to 12 0x00
            values.
            </summary>
            <param name="userPassword">User password</param>
            <param name="engineId">Authoritative engine id</param>
            <param name="authenticationParameters">Extracted USM authentication parameters</param>
            <param name="wholeMessage">Whole message with authentication parameters zeroed (0x00) out</param>
            <returns>True if message authentication has passed the check, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationMD5.authenticateIncomingMsg(System.Byte[],System.Byte[],SnmpSharpNet.MutableByte)">
            <summary>
            Verify MD5 authentication of a packet.
            </summary>
            <param name="authKey">Authentication key (not password)</param>
            <param name="authenticationParameters">Authentication parameters extracted from the packet being authenticated</param>
            <param name="wholeMessage">Entire packet being authenticated</param>
            <returns>True on authentication success, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationMD5.PasswordToKey(System.Byte[],System.Byte[])">
            <summary>
            Convert user password to acceptable authentication key.
            </summary>
            <param name="userPassword">Authentication password</param>
            <param name="engineID">Authoritative engine id</param>
            <returns>Localized authentication key</returns>
            <exception cref="T:SnmpSharpNet.SnmpAuthenticationException">Thrown when key length is less then 8 bytes</exception>
        </member>
        <member name="P:SnmpSharpNet.AuthenticationMD5.DigestLength">
            <summary>
            Length of the digest generated by the authentication protocol
            </summary>
        </member>
        <member name="P:SnmpSharpNet.AuthenticationMD5.Name">
            <summary>
            Return protocol name.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationMD5.ComputeHash(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Compute hash using authentication protocol.
            </summary>
            <param name="data">Data to hash</param>
            <param name="offset">Compute hash from the source buffer offset</param>
            <param name="count">Compute hash for source data length</param>
            <returns>Hash value</returns>
        </member>
        <member name="T:SnmpSharpNet.Authentication">
            <summary>Authentication helper class</summary>
            <remarks>
            Helper class to make dealing with multiple (if 2 qualifies as multiple) authentication protocols in a
            transparent way.
            
            Calling class keeps the authentication protocol selection (as defined on the agent) in an integer
            variable that can have 3 values: <see cref="F:SnmpSharpNet.AuthenticationDigests.None"/>, <see cref="F:SnmpSharpNet.AuthenticationDigests.MD5"/>, or
            <see cref="F:SnmpSharpNet.AuthenticationDigests.SHA1"/>. Using <see cref="M:SnmpSharpNet.Authentication.GetInstance(SnmpSharpNet.AuthenticationDigests)"/>, calling method can
            get authentication protocol implementation class instance cast as <see cref="T:SnmpSharpNet.IAuthenticationDigest"/> interface
            and perform authentication operations (either authenticate outgoing packets to verify authentication of incoming packets)
            without needing to further care about which authentication protocol is used.
            
            Example of how to use this class:
            <code>
            IAuthenticationDigest authenticationImplementation = Authentication.GetInstance(AuthenticationDigests.MD5);
            authenticationImplementation.authenticateIncomingMsg(...);
            authenticationImplementation.authenticateOutgoingMsg(...);
            </code>
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.Authentication.GetInstance(SnmpSharpNet.AuthenticationDigests)">
            <summary>
            Get instance of authentication protocol.
            </summary>
            <param name="authProtocol">Authentication protocol code. Available codes are <see cref="F:SnmpSharpNet.AuthenticationDigests.MD5"/>,
            <see cref="F:SnmpSharpNet.AuthenticationDigests.SHA1"/> or <see cref="F:SnmpSharpNet.AuthenticationDigests.None"/></param>
            <returns>Instance of the authentication protocol or null if unrecognized authentication protocol or value
            <see cref="F:SnmpSharpNet.AuthenticationDigests.None"/> is passed.</returns>
        </member>
        <member name="M:SnmpSharpNet.Authentication.#ctor">
            <summary>
            Constructor. Private to prevent the class from being instantiated.
            </summary>
        </member>
        <member name="T:SnmpSharpNet.AuthenticationSHA1">
            <summary>
            SHA-1 Authentication class.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationSHA1.#ctor">
            <summary>
            Standard constructor.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationSHA1.authenticate(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            Authenticate packet and return authentication parameters value to the caller
            </summary>
            <param name="authenticationSecret">User authentication secret</param>
            <param name="engineId">SNMP agent authoritative engine id</param>
            <param name="wholeMessage">Message to authenticate</param>
            <returns>Authentication parameters value</returns>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationSHA1.authenticate(System.Byte[],System.Byte[])">
            <summary>
            Authenticate packet and return authentication parameters value to the caller
            </summary>
            <param name="authKey">Authentication key (not password)</param>
            <param name="wholeMessage">Message to authenticate</param>
            <returns>Authentication parameters value</returns>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationSHA1.authenticateIncomingMsg(System.Byte[],System.Byte[],System.Byte[],SnmpSharpNet.MutableByte)">
            <summary>
            Verifies correct SHA-1 authentication of the frame. Prior to calling this method, you have to extract authentication
            parameters from the wholeMessage and reset authenticationParameters field in the USM information block to 12 0x00
            values.
            </summary>
            <param name="userPassword">User password</param>
            <param name="engineId">Authoritative engine id</param>
            <param name="authenticationParameters">Extracted USM authentication parameters</param>
            <param name="wholeMessage">Whole message with authentication parameters zeroed (0x00) out</param>
            <returns>True if message authentication has passed the check, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationSHA1.authenticateIncomingMsg(System.Byte[],System.Byte[],SnmpSharpNet.MutableByte)">
            <summary>
            Verify SHA-1 authentication of a packet.
            </summary>
            <param name="authKey">Authentication key (not password)</param>
            <param name="authenticationParameters">Authentication parameters extracted from the packet being authenticated</param>
            <param name="wholeMessage">Entire packet being authenticated</param>
            <returns>True on authentication success, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationSHA1.PasswordToKey(System.Byte[],System.Byte[])">
            <summary>
            Convert user password to acceptable authentication key.
            </summary>
            <param name="userPassword">User password</param>
            <param name="engineID">Authoritative engine id</param>
            <returns>Localized authentication key</returns>
            <exception cref="T:SnmpSharpNet.SnmpAuthenticationException">Thrown when key length is less then 8 bytes</exception>
        </member>
        <member name="P:SnmpSharpNet.AuthenticationSHA1.DigestLength">
            <summary>
            Length of the digest generated by the authentication protocol
            </summary>
        </member>
        <member name="P:SnmpSharpNet.AuthenticationSHA1.Name">
            <summary>
            Return authentication protocol name
            </summary>
        </member>
        <member name="M:SnmpSharpNet.AuthenticationSHA1.ComputeHash(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Compute hash using authentication protocol.
            </summary>
            <param name="data">Data to hash</param>
            <param name="offset">Compute hash from the source buffer offset</param>
            <param name="count">Compute hash for source data length</param>
            <returns>Hash value</returns>
        </member>
        <member name="T:SnmpSharpNet.IAuthenticationDigest">
            <summary>
            Authentication digest interface. Interface defines authentication methods
            for incoming and outgoing requests.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.IAuthenticationDigest.authenticate(System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            Authentication outgoing message
            </summary>
            <param name="userPassword">Authentication secret</param>
            <param name="engineId">SNMP version 3 agent engine id</param>
            <param name="wholeMessage">Message to authenticate</param>
            <returns>Authentication parameters</returns>
        </member>
        <member name="M:SnmpSharpNet.IAuthenticationDigest.authenticate(System.Byte[],System.Byte[])">
            <summary>
            Authentication outgoing message
            </summary>
            <param name="authKey">Authentication key</param>
            <param name="wholeMessage">Message to authenticate</param>
            <returns>Authentication parameters</returns>
        </member>
        <member name="P:SnmpSharpNet.IAuthenticationDigest.DigestLength">
            <summary>
            Returns the size of the native algorithm. This value does not represent the size of the digest
            that is stored inside the USM authentication parameters header but real length generated by it.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.IAuthenticationDigest.authenticateIncomingMsg(System.Byte[],System.Byte[],System.Byte[],SnmpSharpNet.MutableByte)">
            <summary>
            Authenticate incoming messages
            </summary>
            <param name="authentiationSecret">Authentication user secret (password)</param>
            <param name="engineId">SNMP agent authoritative engine id</param>
            <param name="authenticationParameters">Authentication parameters from the incoming packet</param>
            <param name="wholeMessage">Entire message with authentication parameters field zeroed out</param>
            <returns>True if packet is authenticated, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.IAuthenticationDigest.authenticateIncomingMsg(System.Byte[],System.Byte[],SnmpSharpNet.MutableByte)">
            <summary>
            Authenticate incoming messages
            </summary>
            <param name="authKey">Authentication key</param>
            <param name="authenticationParameters">Authentication parameters from the incoming packet</param>
            <param name="wholeMessage">Entire message with authentication parameters field zeroed out</param>
            <returns>True if packet is authenticated, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.IAuthenticationDigest.PasswordToKey(System.Byte[],System.Byte[])">
            <summary>
            Convert password to a key
            </summary>
            <param name="passwordString">Authentication key</param>
            <param name="engineID">Authoritative engine id</param>
            <returns>Key value</returns>
        </member>
        <member name="M:SnmpSharpNet.IAuthenticationDigest.ComputeHash(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Compute hash using authentication protocol.
            </summary>
            <param name="data">Data to hash</param>
            <param name="offset">Compute hash from the source buffer offset</param>
            <param name="count">Compute hash for source data length</param>
            <returns>Hash value</returns>
        </member>
        <member name="P:SnmpSharpNet.IAuthenticationDigest.Name">
            <summary>
            Return authentication protocol name.
            </summary>
        </member>
        <member name="T:SnmpSharpNet.IPrivacyProtocol">
            <summary>
            Privacy protocol interface.
            </summary>
            <remarks>
            Defines encryption and decryption methods for all privacy protocols allowing for any of the
            available protocols to be cast as the interface and called to perform privacy operations on packets.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.IPrivacyProtocol.Encrypt(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32,System.Byte[]@,SnmpSharpNet.IAuthenticationDigest)">
            <summary>
            Encrypt <see cref="T:SnmpSharpNet.ScopedPdu"/> data BER encoded in a byte array.
            </summary>
            <param name="unencryptedData">BER encoded <see cref="T:SnmpSharpNet.ScopedPdu"/> byte array that needs to be encrypted</param>
            <param name="offset">Offset within the BER encoded byte array to start encryption operation from.</param>
            <param name="length">Length of data to encrypt</param>
            <param name="encryptionKey">Encryption key</param>
            <param name="engineBoots">Authoritative engine boots value. Retrieved as part of SNMP v3 discovery process.</param>
            <param name="engineTime">Authoritative engine time value. Retrieved as part of SNMP v3 discovery process.</param>
            <param name="privacyParameters">Byte array that will receive privacy parameters information that is the result of the
            encryption procedure.</param>
            <param name="authDigest">Authentication digest class reference. Only needed for TripleDES privacy protocol. Can be null for all other
            privacy protocols.</param>
            <returns>Byte array containing encrypted <see cref="T:SnmpSharpNet.ScopedPdu"/> BER encoded data</returns>
        </member>
        <member name="M:SnmpSharpNet.IPrivacyProtocol.Decrypt(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32,System.Byte[])">
            <summary>
            Decrypt <see cref="T:SnmpSharpNet.ScopedPdu"/> BER encoded byte array.
            </summary>
            <param name="cryptedData">Encrypted data byte array</param>
            <param name="offset">Offset within the buffer to start decryption process from</param>
            <param name="length">Length of data to decrypt</param>
            <param name="key">Decryption key</param>
            <param name="engineBoots">Authoritative engine boots value. Retrieved as part of SNMP v3 discovery procedure</param>
            <param name="engineTime">Authoritative engine time value. Retrieved as part of SNMP v3 discovery procedure</param>
            <param name="privacyParameters">Privacy parameters parsed from the incoming packet.</param>
            <returns>Byte array containing decrypted <see cref="T:SnmpSharpNet.ScopedPdu"/> in BER encoded format.</returns>
        </member>
        <member name="P:SnmpSharpNet.IPrivacyProtocol.MinimumKeyLength">
            <summary>
            Get minimum acceptable encryption/decryption key length for the protocol.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.IPrivacyProtocol.MaximumKeyLength">
            <summary>
            Get maximum acceptable encryption/decryption key length for the protocol.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.IPrivacyProtocol.PrivacyParametersLength">
            <summary>SNMP version 3 packet privacy parameters field size</summary>
            <remarks>
            Get length of the privacy parameters byte array that is generated by the encryption method and used by the
            decryption method.
            </remarks>
        </member>
        <member name="P:SnmpSharpNet.IPrivacyProtocol.Name">
            <summary>
            Privacy protocol name
            </summary>
        </member>
        <member name="M:SnmpSharpNet.IPrivacyProtocol.GetEncryptedLength(System.Int32)">
            <summary>
            Calculates and returns length of the buffer that is the result of the encryption method.
            </summary>
            <param name="scopedPduLength">Length of the buffer that is needs to be encrypted.</param>
            <returns>Length of the encrypted byte array after the call to Encrypt method.</returns>
        </member>
        <member name="M:SnmpSharpNet.IPrivacyProtocol.ExtendShortKey(System.Byte[],System.Byte[],System.Byte[],SnmpSharpNet.IAuthenticationDigest)">
            <summary>Extend short encryption key</summary>
            <remarks>
            Some privacy protocols require generated key to be extended to match the minimum key size
            required by the encryption alghoritm. Where required, this method should be called by the
            inheriting class as part of PasswordToKey() method call without the need for user to call it.
            </remarks>
            <param name="shortKey">Key that needs to be extended</param>
            <param name="password">Privacy password as configured on the SNMP agent.</param>
            <param name="engineID">Authoritative engine id. Value is retrieved as part of SNMP v3 discovery procedure</param>
            <param name="authProtocol">Authentication protocol class instance cast as <see cref="T:SnmpSharpNet.IAuthenticationDigest"/></param>
            <returns>Extended key value</returns>
        </member>
        <member name="P:SnmpSharpNet.IPrivacyProtocol.CanExtendShortKey">
            <summary>
            Returns true if derived class supports key extension, otherwise false.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.IPrivacyProtocol.PasswordToKey(System.Byte[],System.Byte[],SnmpSharpNet.IAuthenticationDigest)">
            <summary>
            Convert privacy password into encryption key using packet authentication hash.
            </summary>
            <param name="secret">Privacy user secret</param>
            <param name="engineId">Authoritative engine id of the snmp agent</param>
            <param name="authProtocol">Authentication protocol</param>
            <returns>Encryption key</returns>
            <exception cref="T:SnmpSharpNet.SnmpPrivacyException">Thrown when key size is shorter then MinimumKeyLength</exception>
        </member>
        <member name="T:SnmpSharpNet.MsgFlags">
            <summary>
            Message flags in the SNMP v3 header.
            </summary>
            <remarks>
            Message flags hold flags that
            indicate if packet is authenticated, privacy protected and if
            report reply is expected on errors.
            
            Message flags field is a 1 byte <see cref="T:SnmpSharpNet.OctetString"/> encoded
            field.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.MsgFlags.FLAG_AUTH">
            <summary>
            Bit value that, when set, indicates that packet has been authenticated.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.MsgFlags.FLAG_PRIV">
            <summary>
            Bit value that, when set, indicates that packet has been privacy protected.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.MsgFlags.FLAG_REPORTABLE">
            <summary>
            Bit value that, when set, indicates that sender of the packet expects report packet to be sent by the agent on errors.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.MsgFlags._authenticationFlag">
            <summary>
            True if authentication is used to secure the packet, otherwise false.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.MsgFlags.Authentication">
            <summary>
            Get/Set authentication flag. Value is true if authentication is used, false if packet is not authenticated
            </summary>
        </member>
        <member name="F:SnmpSharpNet.MsgFlags._privacyFlag">
            <summary>
            True if ScopedPdu portion of the packet is privacy protected with encryption, otherwise false.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.MsgFlags.Privacy">
            <summary>
            Get/Set privacy flag. Value is true if privacy protection is used, false if packet is not privacy protected.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.MsgFlags._reportableFlag">
            <summary>
            True if reportable flag is set, otherwise false.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.MsgFlags.Reportable">
            <summary>
            Get/Set reportable flag. Value is true if sender expects report packet on errors, false if sender does
            not expect report packets.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.MsgFlags.#ctor">
            <summary>
            Standard constructor. All flags are set to false by default.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.MsgFlags.#ctor(System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Constructor. Initialize individual flag values.
            </summary>
            <param name="authentication">true if authentication is used, otherwise false</param>
            <param name="privacy">true if privacy protection is used, otherwise false</param>
            <param name="reportable">true if report is expected, otherwise false</param>
        </member>
        <member name="M:SnmpSharpNet.MsgFlags.encode(SnmpSharpNet.MutableByte)">
            <summary>
            Encode SNMP v3 message flag field
            </summary>
            <param name="buffer">Buffer to append encoded value to</param>
        </member>
        <member name="M:SnmpSharpNet.MsgFlags.decode(System.Byte[],System.Int32)">
            <summary>
            Decode message flags from the BER encoded buffer starting at specified offset.
            </summary>
            <param name="buffer">BER encoded buffer</param>
            <param name="offset">Offset within the buffer to start decoding process</param>
            <returns>Buffer position after the decoded value</returns>
        </member>
        <member name="M:SnmpSharpNet.MsgFlags.Clone">
            <summary>
            Clone this class.
            </summary>
            <returns>Cloned class cast as Object</returns>
        </member>
        <member name="M:SnmpSharpNet.MsgFlags.ToString">
            <summary>
            Return string representation of the object.
            </summary>
            <returns>String representation of the class values.</returns>
        </member>
        <member name="T:SnmpSharpNet.PrivacyAES">
            <summary>AES privacy protocol implementation class.</summary>
        </member>
        <member name="F:SnmpSharpNet.PrivacyAES._salt">
            <summary>
            Salt value
            </summary>
        </member>
        <member name="F:SnmpSharpNet.PrivacyAES._keyBytes">
            <summary>
            AES protocol key bytes. Valid values are 16 (for AES128), 24 (AES192) or 32 (AES256).
            </summary>
        </member>
        <member name="M:SnmpSharpNet.PrivacyAES.#ctor(System.Int32)">
            <summary>
            Standard constructor.
            </summary>
            <param name="keyBytes">Key size in bytes. Acceptable values are
            16 = 128-bit key size, 24 = 192-bit key size, or 32 = 256-bit key size.</param>
        </member>
        <member name="M:SnmpSharpNet.PrivacyAES.NextSalt">
            <summary>
            Get next salt Int64 value. Used internally to encrypt data.
            </summary>
            <returns>Random Int64 value</returns>
        </member>
        <member name="M:SnmpSharpNet.PrivacyAES.Encrypt(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32,System.Byte[]@,SnmpSharpNet.IAuthenticationDigest)">
            <summary>
            Encrypt <see cref="T:SnmpSharpNet.ScopedPdu"/> data BER encoded in a byte array.
            </summary>
            <param name="unencryptedData">BER encoded <see cref="T:SnmpSharpNet.ScopedPdu"/> byte array that needs to be encrypted</param>
            <param name="offset">Offset within the BER encoded byte array to start encryption operation from.</param>
            <param name="length">Length of data to encrypt</param>
            <param name="key">Encryption key</param>
            <param name="engineBoots">Authoritative engine boots value. Retrieved as part of SNMP v3 discovery process.</param>
            <param name="engineTime">Authoritative engine time value. Retrieved as part of SNMP v3 discovery process.</param>
            <param name="privacyParameters">Byte array that will receive privacy parameters information that is the result of the
            encryption procedure.</param>
            <param name="authDigest">Authentication digest reference. Not used by AES protocol and can be null</param>
            <returns>Byte array containing encrypted <see cref="T:SnmpSharpNet.ScopedPdu"/> BER encoded data</returns>
        </member>
        <member name="M:SnmpSharpNet.PrivacyAES.Decrypt(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32,System.Byte[])">
            <summary>
            Decrypt <see cref="T:SnmpSharpNet.ScopedPdu"/> BER encoded byte array.
            </summary>
            <param name="cryptedData">Encrypted data byte array</param>
            <param name="offset">Offset within the buffer to start decryption process from</param>
            <param name="length">Length of data to decrypt</param>
            <param name="key">Decryption key</param>
            <param name="engineBoots">Authoritative engine boots value. Retrieved as part of SNMP v3 discovery procedure</param>
            <param name="engineTime">Authoritative engine time value. Retrieved as part of SNMP v3 discovery procedure</param>
            <param name="privacyParameters">Privacy parameters parsed from the incoming packet.</param>
            <returns>Byte array containing decrypted <see cref="T:SnmpSharpNet.ScopedPdu"/> in BER encoded format.</returns>
        </member>
        <member name="P:SnmpSharpNet.PrivacyAES.MinimumKeyLength">
            <summary>
            Get minimum encryption/decryption key length required by the protocol.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.PrivacyAES.MaximumKeyLength">
            <summary>
            Get maximum encryption/decryption key length required by the protocol.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.PrivacyAES.PrivacyParametersLength">
            <summary>
            Get length of the privacy parameters byte array that is generated by the encryption method and used by the
            decryption method.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.PrivacyAES.GetEncryptedLength(System.Int32)">
            <summary>
            Calculates and returns length of the buffer that is the result of the encryption method.
            </summary>
            <param name="scopedPduLength">Length of the buffer that is needs to be encrypted.</param>
            <returns>Length of the encrypted byte array after the call to Encrypt method.</returns>
        </member>
        <member name="M:SnmpSharpNet.PrivacyAES.ExtendShortKey(System.Byte[],System.Byte[],System.Byte[],SnmpSharpNet.IAuthenticationDigest)">
            <summary>
            Some protocols support a method to extend the encryption or decryption key when supplied key
            is too short.
            </summary>
            <param name="shortKey">Key that needs to be extended</param>
            <param name="password">Privacy password as configured on the SNMP agent.</param>
            <param name="engineID">Authoritative engine id. Value is retrieved as part of SNMP v3 discovery procedure</param>
            <param name="authProtocol">Authentication protocol class instance cast as <see cref="T:SnmpSharpNet.IAuthenticationDigest"/></param>
            <returns>Extended key value</returns>
        </member>
        <member name="P:SnmpSharpNet.PrivacyAES.Name">
            <summary>
            Privacy protocol name. Returns string "AES"
            </summary>
        </member>
        <member name="P:SnmpSharpNet.PrivacyAES.CanExtendShortKey">
            <summary>
            AES implementation supports extending of a short encryption key. Always returns true.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.PrivacyAES.PasswordToKey(System.Byte[],System.Byte[],SnmpSharpNet.IAuthenticationDigest)">
            <summary>
            Convert privacy password into encryption key using packet authentication hash.
            </summary>
            <param name="secret">Privacy user secret</param>
            <param name="engineId">Authoritative engine id of the snmp agent</param>
            <param name="authProtocol">Authentication protocol</param>
            <returns>Encryption key</returns>
            <exception cref="T:SnmpSharpNet.SnmpPrivacyException">Thrown when key size is shorter then MinimumKeyLength</exception>
        </member>
        <member name="T:SnmpSharpNet.PrivacyAES128">
            <summary>
            AES 128-bit key size privacy protocol implementation class.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.PrivacyAES128.#ctor">
            <summary>
            Standard constructor. Initializes the base <see cref="T:SnmpSharpNet.PrivacyAES"/> class with key size 16 bytes (128-bit).
            </summary>
        </member>
        <member name="P:SnmpSharpNet.PrivacyAES128.Name">
            <summary>
            Returns privacy protocol name "AES128"
            </summary>
        </member>
        <member name="T:SnmpSharpNet.PrivacyDES">
            <summary>DES privacy protocol implementation class.</summary>
            <remarks>
            SNMP Version 3 DES privacy protocol implementation.
            
            DES requires an encryption key be provided of 16 bytes in length. Class will accept longer key values (which will be
            trimmed to 16 bytes) but both encrypt and decrypt operations will fail if key is shorter then required value length.
            
            Decryption operation depends on USM header privacyParameters field value which is generated by the Encrypt method. Make
            sure privacyParameters argument value is correctly inserted into the target packet to enable SNMP agent to decrypt the
            message.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.PrivacyDES._salt">
            <summary>
            Internal salt value. As per RFC standard, salt value is initialized in the constructor and incremented by 1 for each
            subsequent packet.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.PrivacyDES.#ctor">
            <summary>
            Standard constructor.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.PrivacyDES.NextSalt">
            <summary>
            Returns next salt value.
            </summary>
            <returns>32-bit integer salt value in network byte order (big endian)</returns>
        </member>
        <member name="M:SnmpSharpNet.PrivacyDES.Encrypt(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32,System.Byte[]@,SnmpSharpNet.IAuthenticationDigest)">
            <summary>
            Encrypt ScopedPdu using DES encryption protocol
            </summary>
            <param name="unencryptedData">Unencrypted ScopedPdu byte array</param>
            <param name="offset">Offset to start encryption</param>
            <param name="length">Length of data to encrypt</param>
            <param name="key">Encryption key. Key has to be at least 32 bytes is length</param>
            <param name="engineBoots">Authoritative engine boots value</param>
            <param name="engineTime">Authoritative engine time value. Not used for DES</param>
            <param name="privacyParameters">Privacy parameters out buffer. This field will be filled in with information
            required to decrypt the information. Output length of this field is 8 bytes and space has to be reserved
            in the USM header to store this information</param>
            <param name="authDigest">Authentication digest class reference. Not used by DES and can be null.</param>
            <returns>Encrypted byte array</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when encryption key is null or length of the encryption key is too short.</exception>
        </member>
        <member name="M:SnmpSharpNet.PrivacyDES.Decrypt(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32,System.Byte[])">
            <summary>
            Decrypt DES encrypted ScopedPdu
            </summary>
            <param name="encryptedData">Source data buffer</param>
            <param name="offset">Offset within the buffer to start decryption process</param>
            <param name="length">Length of data to decrypt</param>
            <param name="key">Decryption key. Key length has to be 32 bytes in length or longer (bytes beyond 32 bytes are ignored).</param>
            <param name="engineBoots">Authoritative engine boots value</param>
            <param name="engineTime">Authoritative engine time value</param>
            <param name="privacyParameters">Privacy parameters extracted from USM header</param>
            <returns>Decrypted byte array</returns>
            <exception cref="T:System.ArgumentNullException">Thrown when encrypted data is null or length == 0</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when encryption key length is less then 32 byte or if privacy parameters
            argument is null or length other then 8 bytes</exception>
        </member>
        <member name="P:SnmpSharpNet.PrivacyDES.MinimumKeyLength">
            <summary>
            Returns minimum encryption/decryption key length. For DES, returned value is 16.
            </summary>
            <remarks>
            DES protocol requires an 8 byte encryption key and additional 8 bytes are used for generating the
            encryption IV.
            </remarks>
        </member>
        <member name="P:SnmpSharpNet.PrivacyDES.MaximumKeyLength">
            <summary>
            Return maximum encryption/decryption key length. For DES, returned value is 16
            </summary>
            <remarks>
            DES protocol requires an 8 byte encryption key and additional 8 bytes are used for generating the
            encryption IV.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.PrivacyDES.GetEncryptedLength(System.Int32)">
            <summary>
            Get final encrypted length
            </summary>
            <param name="scopedPduLength">BER encoded ScopedPdu data length</param>
            <returns>Length of encrypted byte array</returns>
        </member>
        <member name="P:SnmpSharpNet.PrivacyDES.PrivacyParametersLength">
            <summary>
            Returns the length of privacyParameters USM header field. For DES, field length is 8.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.PrivacyDES.ExtendShortKey(System.Byte[],System.Byte[],System.Byte[],SnmpSharpNet.IAuthenticationDigest)">
            <summary>
            Operation not used by DES. Key length has to be 16 bytes of encryption/decryption operation will fail.
            
            When called, shortKey is returned.
            </summary>
            <param name="shortKey">Encryption key</param>
            <param name="password">Privacy password</param>
            <param name="engineID">Authoritative engine id</param>
            <param name="authProtocol">Authentication protocol class instance</param>
            <returns>unaltered shortKey value</returns>
        </member>
        <member name="P:SnmpSharpNet.PrivacyDES.Name">
            <summary>
            Privacy protocol name
            </summary>
        </member>
        <member name="P:SnmpSharpNet.PrivacyDES.CanExtendShortKey">
            <summary>
            DES implementation does NOT support extending of a short encryption key. Always returns false.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.PrivacyDES.GetSalt(System.Int32)">
            <summary>
            Get DES encryption salt value. Salt value is generated by concatenating engineBoots value with
            the random integer value.
            </summary>
            <param name="engineBoots">SNMP engine boots value</param>
            <returns>Salt byte array 8 byte in length</returns>
        </member>
        <member name="M:SnmpSharpNet.PrivacyDES.GetKey(System.Byte[])">
            <summary>
            Extract and return DES encryption key.
            
            Privacy password is 16 bytes in length. Only the first 8 bytes are used as DES password. Remaining
            8 bytes are used as pre-IV value.
            </summary>
            <param name="privacyPassword">16 byte privacy password</param>
            <returns>8 byte DES encryption password</returns>
            <exception cref="T:SnmpSharpNet.SnmpPrivacyException">Thrown when privacy password is less then 16 bytes long</exception>
        </member>
        <member name="M:SnmpSharpNet.PrivacyDES.GetIV(System.Byte[],System.Byte[])">
            <summary>
            Generate IV from the privacy key and salt value returned by GetSalt method.
            </summary>
            <param name="privacyKey">16 byte privacy key</param>
            <param name="salt">Salt value returned by GetSalt method</param>
            <returns>IV value used in the encryption process</returns>
            <exception cref="T:SnmpSharpNet.SnmpPrivacyException">Thrown when privacy key is less then 16 bytes long.</exception>
        </member>
        <member name="M:SnmpSharpNet.PrivacyDES.PasswordToKey(System.Byte[],System.Byte[],SnmpSharpNet.IAuthenticationDigest)">
            <summary>
            Convert privacy password into encryption key using packet authentication hash.
            </summary>
            <param name="secret">Privacy user secret</param>
            <param name="engineId">Authoritative engine id of the snmp agent</param>
            <param name="authProtocol">Authentication protocol</param>
            <returns>Encryption key</returns>
            <exception cref="T:SnmpSharpNet.SnmpPrivacyException">Thrown when key size is shorter then MinimumKeyLength</exception>
        </member>
        <member name="T:SnmpSharpNet.PrivacyProtocol">
            <summary>Privacy protocol helper class.</summary>
            <remarks>
            This class is used to define privacy protocol encryption type in other
            classes using integer constants representing each protocol supported, and allows for easy instantiation
            of privacy protocol when used for encryption or decryption of data in a encryption method independent way.
            
            Example of how to use this class:
            <code>
            int myPrivacyProtocol = PrivacyProtocol.AES128;
            
            IPrivacyProtocol privacyImplementation = PrivacyProtocol.GetInstance(myPrivacyProtocol);
            byte[] result = privacyImplementation.Encrypt(....);
            </code>
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.PrivacyProtocol.GetInstance(SnmpSharpNet.PrivacyProtocols)">
            <summary>
            Based on the supplied privacyProtocol, return instance of the privacy protocol implementation class.
            </summary>
            <param name="privProtocol">Privacy protocol code. Available protocols are <see cref="F:SnmpSharpNet.PrivacyProtocols.DES"/>, 
            <see cref="F:SnmpSharpNet.PrivacyProtocols.AES128"/>, <see cref="F:SnmpSharpNet.PrivacyProtocols.AES192"/>, <see cref="F:SnmpSharpNet.PrivacyProtocols.AES256"/> and
            <see cref="F:SnmpSharpNet.PrivacyProtocols.TripleDES"/>.</param>
            <returns>Privacy protocol implementation class on success. If privacy protocol is <see cref="F:SnmpSharpNet.PrivacyProtocols.None"/>
            then null is returned.</returns>
        </member>
        <member name="M:SnmpSharpNet.PrivacyProtocol.#ctor">
            <summary>
            Private constructor. This class cannot be instantiated.
            </summary>
        </member>
        <member name="T:SnmpSharpNet.ScopedPdu">
            <summary>SNMP Version 3 Scoped Protocol Data Unit.</summary>
            <remarks>
            ScopedPdu extends the <see cref="T:SnmpSharpNet.Pdu"/> class by adding SNMP version 3 specific Context Engine Id and Context Name
            variables to the beginning of the packet.
            
            Context engine id is retrieved from the agent using the SNMP version 3 standard defined discovery process. Context
            name is used to define which subsection of the agents MIB user is allowed (or wants) to access.
            
            When creating a new ScopedPdu, for a valid request, ContextEngineId value has to be set for a valid reply to be
            received. Context value is implementation specific (depends on the configuration of the agent) and might not be
            required for a successful request.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.ScopedPdu._contextEngineId">
            <summary>
            Context Engine Id variable.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.ScopedPdu._contextName">
            <summary>
            Context name variable
            </summary>
        </member>
        <member name="P:SnmpSharpNet.ScopedPdu.ContextEngineId">
            <summary>
            Get/Set context engine id value.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.ScopedPdu.ContextName">
            <summary>
            Get/Set context name value
            </summary>
        </member>
        <member name="M:SnmpSharpNet.ScopedPdu.#ctor">
            <summary>
            Standard constructor.
            </summary>
            <remarks>
            Intializes the ScopedPdu type to SNMP-GET. For details see base
            class <see cref="T:SnmpSharpNet.Pdu"/> standard constructor definition.
            
            Context engine id and name variables are initialized to 0 length values.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.ScopedPdu.#ctor(SnmpSharpNet.PduType)">
            <summary>
            Constructor.
            </summary>
            <param name="pduType">Pdu type value. For available types see <see cref="T:SnmpSharpNet.PduType"/>.</param>
        </member>
        <member name="M:SnmpSharpNet.ScopedPdu.#ctor(SnmpSharpNet.PduType,System.Int32)">
            <summary>Constructor.</summary>
            <remarks>
            Standard constructor that sets ScopedPdu request type and request id. For valid types see <see cref="T:SnmpSharpNet.PduType"/>.
            
            By default, requestId value is set to a random value. Second argument allows caller to specify request
            id for this packet
            </remarks>
            <param name="pduType">Pdu type value.</param>
            <param name="requestId">Request id for the ScopedPdu instance.</param>
        </member>
        <member name="M:SnmpSharpNet.ScopedPdu.#ctor(SnmpSharpNet.Pdu)">
            <summary>
            Constructor.
            </summary>
            <param name="pdu">Initialize class from this <see cref="T:SnmpSharpNet.Pdu"/> class</param>
        </member>
        <member name="M:SnmpSharpNet.ScopedPdu.encode(SnmpSharpNet.MutableByte)">
            <summary>
            Convert <see cref="T:SnmpSharpNet.ScopedPdu"/> into a BER encoded byte array. Resulting byte array is appended
            to the argument specified <see cref="T:SnmpSharpNet.MutableByte"/> class.
            
            Privacy operations are not performed by this method. Value encoded and returned by this method is
            suitable for sending in NoAuthNoPriv or AuthNoPriv security configurations. If privacy is required,
            caller will have to perform encryption and decryption operations after BER encoding is performed.
            
            In privacy protected SNMP version 3 packets, ScopedPdu is 1) encrypted using configured encryption
            method, 2) added to a <see cref="T:SnmpSharpNet.OctetString"/> field, and 3) appended to the data buffer.
            
            Because privacy operation is intrusive, it is recommended that BER encoding of the ScopedPdu packet
            is stored in a temporary <see cref="T:SnmpSharpNet.MutableByte"/> class, where it can be privacy protected and
            added to the <see cref="T:SnmpSharpNet.OctetString"/> class for final encoding into the target SNMP v3 packet.
            </summary>
            <param name="buffer"><see cref="T:SnmpSharpNet.MutableByte"/> class passed by reference that encoded ScopedPdu
            value is appended to.</param>
        </member>
        <member name="M:SnmpSharpNet.ScopedPdu.decode(System.Byte[],System.Int32)">
            <summary>
            Decode BER encoded <see cref="T:SnmpSharpNet.ScopedPdu"/> values. This method does not perform SNMP v3 privacy operations
            and is not aware of privacy requirements.
            
            To decode a privacy protected SNMP v3 packet, you will need to a) extract <see cref="T:SnmpSharpNet.OctetString"/> value 
            holding encrypted <see cref="T:SnmpSharpNet.ScopedPdu"/> data, b) decrypt the encrypted ScopedPdu data into an unecrypted
            byte array, c) pass unencrypted <see cref="T:SnmpSharpNet.ScopedPdu"/> and BER encoded byte array to this method for
            final data conversion from BER into individual sequences and variables.
            </summary>
            <param name="buffer">Buffer holding BER encoded <see cref="T:SnmpSharpNet.ScopedPdu"/> data</param>
            <param name="offset">Offset within the buffer BER encoded <see cref="T:SnmpSharpNet.ScopedPdu"/> data begins.</param>
            <returns>Offset position after parsed ScopedPdu</returns>
            <exception cref="T:SnmpSharpNet.SnmpDecodingException">Error was encountered when decoding the PDU</exception>
            <exception cref="T:System.OverflowException">Thrown when buffer is too short to contain the PDU</exception>
        </member>
        <member name="T:SnmpSharpNet.SNMPV3ReportError">
            <summary>
            SNMP class translates SNMP version 3 report errors into error strings.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SNMPV3ReportError.TranslateError(SnmpSharpNet.SnmpV3Packet)">
            <summary>
            Search variable bindings list in the passed packet for usm error OIDs and return
            error string value.
            </summary>
            <param name="packet">Packet to search for error OIDs</param>
            <returns>Error string if found in the packet, otherwise an empty string.</returns>
        </member>
        <member name="T:SnmpSharpNet.UserSecurityModel">
            <summary>
            User security model implementation class.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UserSecurityModel._engineId">
            <summary>
            Authoritative engine id
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UserSecurityModel._engineBoots">
            <summary>
            Authoritative engine boots value
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UserSecurityModel._engineTime">
            <summary>
            Authoritative engine time value
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UserSecurityModel._securityName">
            <summary>
            SNMP version 3 security name (or user name)
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UserSecurityModel._authentication">
            <summary>
            Authentication digest enumeration value. For acceptable values see <see cref="T:SnmpSharpNet.AuthenticationDigests"/>
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UserSecurityModel._authenticationSecret">
            <summary>
            Authentication secret
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UserSecurityModel._authenticationParameters">
            <summary>
            Authentication parameters in authNoPriv and authPriv requests
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UserSecurityModel._privacy">
            <summary>
            Privacy protocol. For valid values see <see cref="T:SnmpSharpNet.PrivacyProtocols"/>
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UserSecurityModel._privacySecret">
            <summary>
            Privacy secret
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UserSecurityModel._privacyParameters">
            <summary>
            Privacy parameters in authPriv requests
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UserSecurityModel.EngineId">
            <summary>
            Get SNMP version 3 agent authoritative engine id object
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UserSecurityModel.EngineBoots">
            <summary>
            Get SNMP version 3 agent authoritative engine boots object
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UserSecurityModel.EngineTime">
            <summary>
            Get SNMP version 3 agent authoritative engine time object
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UserSecurityModel.AuthenticationParameters">
            <summary>
            Get SNMP version 3 authentication parameter field object
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UserSecurityModel.SetEngineTime(System.Int32,System.Int32)">
            <summary>
            Set SNMP version 3 agent engine time related values.
            </summary>
            <param name="engineTime">SNMP version 3 agent engine time value</param>
            <param name="engineBoots">SNMP version 3 agent engine boot value</param>
        </member>
        <member name="P:SnmpSharpNet.UserSecurityModel.Authentication">
            <summary>
            Get/Set hash to use for SNMP version 3 authentication. For available values see <see cref="T:SnmpSharpNet.AuthenticationDigests"/>
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UserSecurityModel.SecurityName">
            <summary>
            Security name (or user name)
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UserSecurityModel.AuthenticationSecret">
            <summary>
            Authentication secret (or password).
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UserSecurityModel.PrivacySecret">
            <summary>
            Privacy secret (or password)
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UserSecurityModel.Privacy">
            <summary>
            Get/set privacy protocol value. For available privacy protocols, see <see cref="T:SnmpSharpNet.PrivacyProtocols"/>
            enumeration.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UserSecurityModel.PrivacyParameters">
            <summary>
            Get privacy parameters object.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UserSecurityModel.#ctor">
            <summary>
            Standard constructor.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UserSecurityModel.#ctor(SnmpSharpNet.UserSecurityModel)">
            <summary>
            Copy constructor.
            </summary>
            <param name="value">Class to copy values from</param>
        </member>
        <member name="M:SnmpSharpNet.UserSecurityModel.Authenticate(SnmpSharpNet.MutableByte@)">
            <summary>
            Authenticate SNMP version 3 message.
            
            Before calling this member, entire SNMP version 3 packet needs to be encoded. After authentication
            process is completed, authenticationParameters value in the USM header is updated and SNMPv3 packet
            needs to be re-encoded to include it in the BER encoded stream prior to transmission.
            </summary>
            <param name="wholePacket">SNMP version 3 BER encoded packet.</param>
        </member>
        <member name="M:SnmpSharpNet.UserSecurityModel.Authenticate(System.Byte[],SnmpSharpNet.MutableByte@)">
            <summary>
            Authenticate SNMP version 3 message.
            
            Before calling this member, entire SNMP version 3 packet needs to be encoded. After authentication
            process is completed, authenticationParameters value in the USM header is updated and SNMPv3 packet
            needs to be re-encoded to include it in the BER encoded stream prior to transmission.
            </summary>
            <param name="authKey">Authentication key (not password)</param>
            <param name="wholePacket">SNMP version 3 BER encoded packet.</param>
        </member>
        <member name="M:SnmpSharpNet.UserSecurityModel.IsAuthentic(SnmpSharpNet.MutableByte)">
            <summary>
            Authenticate incoming packet
            </summary>
            <param name="wholePacket">Received BER encoded SNMP version 3 packet</param>
            <returns>True if packet is successfully authenticated, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.UserSecurityModel.IsAuthentic(System.Byte[],SnmpSharpNet.MutableByte)">
            <summary>
            Authenticate incoming packet
            </summary>
            <param name="authKey">Authentication key (not password)</param>
            <param name="wholePacket">Received BER encoded SNMP version 3 packet</param>
            <returns>True if packet is successfully authenticated, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.UserSecurityModel.encode(SnmpSharpNet.MutableByte)">
            <summary>BER encode security model field.</summary>
            <remarks>
            USM security model is a SEQUENCE encoded inside a OCTETSTRING. To encode it, first encode the sequence
            of class values then "wrap" it inside a OCTETSTRING field
            </remarks>
            <param name="buffer">Buffer to store encoded USM security model header</param>
        </member>
        <member name="M:SnmpSharpNet.UserSecurityModel.decode(System.Byte[],System.Int32)">
            <summary>
            Decode USM portion of the SNMP version 3 packet.
            </summary>
            <param name="buffer">Received SNMP packet BER encoded</param>
            <param name="offset">Offset within the buffer to start decoding USM information</param>
            <returns>Buffer position after the decoded value</returns>
            <exception cref="T:SnmpSharpNet.SnmpDecodingException">Thrown when decoding enountered invalid data type in USM information</exception>
            <exception cref="T:System.OverflowException">Thrown when packet is too small to contain information length specified in header</exception>
        </member>
        <member name="M:SnmpSharpNet.UserSecurityModel.Clone">
            <summary>
            Clone object
            </summary>
            <returns>Duplicate copy of the object</returns>
        </member>
        <member name="M:SnmpSharpNet.UserSecurityModel.Valid">
            <summary>
            Checks for validity and completeness of information in this class. This method doesn't "know" what you
            are trying to do so it tests for minimal information required.
            </summary>
            <returns>True if information is valid and complete enough for a successful request, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.UserSecurityModel.Reset">
            <summary>
            Reset USM object to default values. All OctetString and MutableByte members are reset to 0 length and
            privacy and authentication protocols are set to none.
            </summary>
        </member>
        <member name="T:SnmpSharpNet.SnmpVariableType">
            <summary>
            Class containing SNMP type constants usable in switch/case evaluation of variable types.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.Counter32">
            <summary>
            SNMP type Counter32
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.Counter64">
            <summary>
            SNMP type Counter64
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.EndOfMibView">
            <summary>
            SNMP type EndOfMibView
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.Gauge32">
            <summary>
            SNMP type Gauge32
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.Integer">
            <summary>
            SNMP type Integer
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.IPAddress">
            <summary>
            SNMP type IPAddress
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.NoSuchInstance">
            <summary>
            SNMP type NoSuchInstance
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.NoSuchObject">
            <summary>
            SNMP type NoSuchObject
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.Null">
            <summary>
            SNMP type Null
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.OctetString">
            <summary>
            SNMP type OctetString
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.Oid">
            <summary>
            SNMP type Oid
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.Opaque">
            <summary>
            SNMP type Opaque
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.Sequence">
            <summary>
            SNMP type Sequence
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.TimeTicks">
            <summary>
            SNMP type TimeTicks
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.Unsigned32">
            <summary>
            SNMP type Unsigned32
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVariableType.VarBind">
            <summary>
            SNMP type VarBind
            </summary>
        </member>
        <member name="M:SnmpSharpNet.SnmpVariableType.#ctor">
            <summary>
            Private constructor to prevent the class with all static members from being instantiated.
            </summary>
        </member>
        <member name="T:SnmpSharpNet.SnmpVersion">
            <summary>
            SNMP Version number enumeration
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVersion.Ver1">
            <summary>
            SNMP version 1
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVersion.Ver2">
            <summary>
            SNMP version 2c
            </summary>
        </member>
        <member name="F:SnmpSharpNet.SnmpVersion.Ver3">
            <summary>
            SNMP version 3
            </summary>
        </member>
        <member name="T:SnmpSharpNet.TrapAgent">
            <summary>
            Send SNMP Trap notifications
            </summary>
            <remarks>
            TrapAgent class is used to hide Socket operations from users and provide an easy method to send
            Trap notifications.
            
            To use the class, you can use the TrapAgent class protocol specific members, recommended when you
            expect to send a lot of notifications, or a static helper TrapAgent.SendTrap method which will
            construct a new socket for each call.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.TrapAgent._sock">
            <summary>
            Internal Socket class
            </summary>
        </member>
        <member name="M:SnmpSharpNet.TrapAgent.#ctor">
            <summary>
            Constructor.
            </summary>
            <remarks>
            Constructor initializes an internal Socket used to send traps. Socket is initialized by selecting a
            random UDP port number.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.TrapAgent.Finalize">
            <summary>
            Destructor.
            </summary>
            <remarks>Destructors only purpose is to close the Socket used by the class.</remarks>
        </member>
        <member name="M:SnmpSharpNet.TrapAgent.SendV1Trap(SnmpSharpNet.SnmpV1TrapPacket,SnmpSharpNet.IpAddress,System.Int32)">
            <summary>
            Send SNMP version 1 Trap notification
            </summary>
            <param name="packet">SNMP v1 Trap packet class</param>
            <param name="peer">Manager (receiver) IP address</param>
            <param name="port">Manager (receiver) UDP port number</param>
        </member>
        <member name="M:SnmpSharpNet.TrapAgent.SendV1Trap(SnmpSharpNet.IpAddress,System.Int32,System.String,SnmpSharpNet.Oid,SnmpSharpNet.IpAddress,System.Int32,System.Int32,System.UInt32,SnmpSharpNet.VbCollection)">
            <summary>
            Construct and send SNMP v1 Trap
            </summary>
            <param name="receiver">Receiver IP address</param>
            <param name="receiverPort">Receiver UDP port number</param>
            <param name="community">SNMP community name</param>
            <param name="senderSysObjectID">Senders sysObjectID</param>
            <param name="senderIpAdress">Sender IP address</param>
            <param name="genericTrap">Generic trap code</param>
            <param name="specificTrap">Specific trap code</param>
            <param name="senderUpTime">Senders sysUpTime</param>
            <param name="varList">Variable binding list</param>
        </member>
        <member name="M:SnmpSharpNet.TrapAgent.SendV2Trap(SnmpSharpNet.SnmpV2Packet,SnmpSharpNet.IpAddress,System.Int32)">
            <summary>
            Send SNMP version 2 Trap notification
            </summary>
            <param name="packet">SNMP v2 Trap packet class</param>
            <param name="peer">Manager (receiver) IP address</param>
            <param name="port">Manager (receiver) UDP port number</param>
        </member>
        <member name="M:SnmpSharpNet.TrapAgent.SendV2Trap(SnmpSharpNet.IpAddress,System.Int32,System.String,System.UInt32,SnmpSharpNet.Oid,SnmpSharpNet.VbCollection)">
            <summary>
            Construct and send SNMP v2 Trap
            </summary>
            <param name="receiver">Trap receiver IP address</param>
            <param name="receiverPort">Trap receiver UDP port number</param>
            <param name="community">SNMP community name</param>
            <param name="senderUpTime">Sender sysUpTime</param>
            <param name="trapObjectID">Trap ObjectID</param>
            <param name="varList">Variable binding list</param>
        </member>
        <member name="M:SnmpSharpNet.TrapAgent.SendV3Trap(SnmpSharpNet.SnmpV3Packet,SnmpSharpNet.IpAddress,System.Int32)">
            <summary>
            Send SNMP version 3 Trap notification
            </summary>
            <param name="packet">SNMP v3 Trap packet class</param>
            <param name="peer">Manager (receiver) IP address</param>
            <param name="port">Manager (receiver) UDP port number</param>
        </member>
        <member name="M:SnmpSharpNet.TrapAgent.SendV3Trap(SnmpSharpNet.IpAddress,System.Int32,System.Byte[],System.Int32,System.Int32,System.String,System.UInt32,SnmpSharpNet.Oid,SnmpSharpNet.VbCollection)">
            <summary>
            Construct and send SNMP v3 noAuthNoPriv Trap
            </summary>
            <param name="receiver">Trap receiver IP address</param>
            <param name="receiverPort">Trap receiver UDP port number</param>
            <param name="engineId">Sender SNMP engineId</param>
            <param name="senderEngineBoots">Sender SNMP engine boots</param>
            <param name="senderEngineTime">Sender SNMP engine time</param>
            <param name="senderUserName">Security (user) name</param>
            <param name="senderUpTime">Sender upTime</param>
            <param name="trapObjectID">Trap object ID</param>
            <param name="varList">Variable binding list</param>
        </member>
        <member name="M:SnmpSharpNet.TrapAgent.SendV3Trap(SnmpSharpNet.IpAddress,System.Int32,System.Byte[],System.Int32,System.Int32,System.String,System.UInt32,SnmpSharpNet.Oid,SnmpSharpNet.VbCollection,SnmpSharpNet.AuthenticationDigests,System.Byte[])">
            <summary>
            Construct and send SNMP v3 authNoPriv Trap
            </summary>
            <param name="receiver">Trap receiver IP address</param>
            <param name="receiverPort">Trap receiver UDP port number</param>
            <param name="engineId">Sender SNMP engineId</param>
            <param name="senderEngineBoots">Sender SNMP engine boots</param>
            <param name="senderEngineTime">Sender SNMP engine time</param>
            <param name="senderUserName">Security (user) name</param>
            <param name="senderUpTime">Sender upTime</param>
            <param name="trapObjectID">Trap object ID</param>
            <param name="varList">Variable binding list</param>
            <param name="authDigest">Authentication digest. <see cref="T:SnmpSharpNet.AuthenticationDigests"/> enumeration for
            available digests</param>
            <param name="authSecret">Authentication secret</param>
        </member>
        <member name="M:SnmpSharpNet.TrapAgent.SendV3Trap(SnmpSharpNet.IpAddress,System.Int32,System.Byte[],System.Int32,System.Int32,System.String,System.UInt32,SnmpSharpNet.Oid,SnmpSharpNet.VbCollection,SnmpSharpNet.AuthenticationDigests,System.Byte[],SnmpSharpNet.PrivacyProtocols,System.Byte[])">
            <summary>
            Construct and send SNMP v3 authPriv Trap
            </summary>
            <param name="receiver">Trap receiver IP address</param>
            <param name="receiverPort">Trap receiver UDP port number</param>
            <param name="engineId">Sender SNMP engineId</param>
            <param name="senderEngineBoots">Sender SNMP engine boots</param>
            <param name="senderEngineTime">Sender SNMP engine time</param>
            <param name="senderUserName">Security (user) name</param>
            <param name="senderUpTime">Sender upTime</param>
            <param name="trapObjectID">Trap object ID</param>
            <param name="varList">Variable binding list</param>
            <param name="authDigest">Authentication digest. See <see cref="T:SnmpSharpNet.AuthenticationDigests"/> enumeration for
            available digests</param>
            <param name="authSecret">Authentication secret</param>
            <param name="privProtocol">Privacy protocol. See <see cref="T:SnmpSharpNet.PrivacyProtocols"/> enumeration for
            available privacy protocols.</param>
            <param name="privSecret">Privacy secret</param>
        </member>
        <member name="M:SnmpSharpNet.TrapAgent.SendTrap(SnmpSharpNet.SnmpPacket,SnmpSharpNet.IpAddress,System.Int32)">
            <summary>
            Send SNMP Trap notification
            </summary>
            <remarks>
            Helper function to allow for seamless sending of SNMP notifications for all protocol versions.
            
            packet parameter should be appropriately formatted SNMP notification in SnmpV1TrapPacket,
            SnmpV2Packet or SnmpV3Packet class cast as SnmpPacket class.
            
            Function will determine which version of the notification is to be used by checking the type
            of the packet parameter and call appropriate TrapAgent member function to send it.
            </remarks>
            <param name="packet">SNMP trap packet</param>
            <param name="peer">Manager (receiver) IP address</param>
            <param name="port">Manager (receiver) UDP port number</param>
        </member>
        <member name="T:SnmpSharpNet.TrapPdu">
            <summary>SNMP version 1 TRAP Protocol Data Unit</summary>
            <remarks>
            Trap PDU for SNMP version 1 is a PDU with a unique layout requiring a dedicated class. SNMP versions
            2 and 3 use standard PDU type for V2TRAP notifications.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.TrapPdu._enterprise">
            <summary>Trap enterprise Oid</summary>
        </member>
        <member name="F:SnmpSharpNet.TrapPdu._agentAddr">
            <summary>The IP Address of the remote agent sending the trap.</summary>
        </member>
        <member name="F:SnmpSharpNet.TrapPdu._generic">
            <summary>Generic trap code</summary>
        </member>
        <member name="F:SnmpSharpNet.TrapPdu._specific">
            <summary>Specific trap code.</summary>
        </member>
        <member name="F:SnmpSharpNet.TrapPdu._timeStamp">
            <summary>sysUpTime timestamp of the trap event</summary>
        </member>
        <member name="F:SnmpSharpNet.TrapPdu._variables">
            <summary>Variable binding list</summary>
        </member>
        <member name="P:SnmpSharpNet.TrapPdu.AgentAddress">
            <summary>Get remote agent's IP address.</summary>
        </member>
        <member name="P:SnmpSharpNet.TrapPdu.Generic">
            <summary>Get/Set generic code trap value object</summary>
        </member>
        <member name="P:SnmpSharpNet.TrapPdu.Specific">
            <summary>Get/Set specific code trap value object</summary>
        </member>
        <member name="P:SnmpSharpNet.TrapPdu.TimeStamp">
            <summary>Get timeticks trap value object</summary>
        </member>
        <member name="P:SnmpSharpNet.TrapPdu.Count">
            <summary> Returns the number oid/value pairs in the variable binding contained in the PDU</summary>
        </member>
        <member name="M:SnmpSharpNet.TrapPdu.#ctor">
            <summary>Constructor</summary>
        </member>
        <member name="M:SnmpSharpNet.TrapPdu.#ctor(SnmpSharpNet.TrapPdu)">
            <summary> Constructs a new trap pdu that is identical to the
            passed pdu.
            </summary>
            <param name="second">The object to copy.
            </param>
        </member>
        <member name="M:SnmpSharpNet.TrapPdu.Set(System.String)">
            <summary>
            Not implemented. Throws NotImplementedException.
            </summary>
            <param name="value">Irrelevant</param>
        </member>
        <member name="P:SnmpSharpNet.TrapPdu.Type">
            <summary>
            Get PDU type.
            </summary>
            <remarks>Always returns PduType.Trap</remarks>
        </member>
        <member name="M:SnmpSharpNet.TrapPdu.Set(SnmpSharpNet.TrapPdu)">
            <summary>
            Initialize the class with values from another <see cref="T:SnmpSharpNet.TrapPdu"/> class.
            </summary>
            <param name="second">TrapPdu class whose values are used to initialize this class.</param>
        </member>
        <member name="P:SnmpSharpNet.TrapPdu.Enterprise">
            <summary>Get trap enterprise identifier</summary>
        </member>
        <member name="P:SnmpSharpNet.TrapPdu.VbList">
            <summary>
            Get <see cref="T:SnmpSharpNet.VbCollection"/> variable binding list.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.TrapPdu.VbCount">
            <summary>
            Return number of entries in the VbList
            </summary>
        </member>
        <member name="M:SnmpSharpNet.TrapPdu.encode(SnmpSharpNet.MutableByte)">
            <summary>ASN.1 encode SNMP version 1 trap</summary>
            <param name="buffer"><see cref="T:SnmpSharpNet.MutableByte"/> buffer to the end of which encoded values are appended.</param>
        </member>
        <member name="M:SnmpSharpNet.TrapPdu.decode(System.Byte[],System.Int32)">
            <summary>Decode BER encoded SNMP version 1 trap packet</summary>
            <param name="buffer">BER encoded buffer</param>
            <param name="offset">Offset in the packet to start decoding from</param>
            <returns>Buffer position after the decoded value.</returns>
            <exception cref="T:SnmpSharpNet.SnmpException">Invalid SNMP Pdu type received. Not an SNMP version 1 Trap PDU.</exception>
            <exception cref="T:SnmpSharpNet.SnmpException">Invalid Variable Binding list encoding.</exception>
        </member>
        <member name="M:SnmpSharpNet.TrapPdu.Clone">
            <summary>
            Clone object
            </summary>
            <returns>Cloned copy of this object.</returns>
        </member>
        <member name="T:SnmpSharpNet.Counter32">
            <summary>SMI Counter32 type implementation.</summary>
            <remarks>
            Counter32 value type is a 32-bit unsigned integer object that
            is incremented by an agent until maximum unsigned integer value
            is reached. When maximum value is reached, Counter32 value will
            roll over to 0.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.Counter32.#ctor">
            <summary>Constructor</summary>
        </member>
        <member name="M:SnmpSharpNet.Counter32.#ctor(SnmpSharpNet.Counter32)">
            <summary>Constructor</summary>
            <param name="second">Copy parameter</param>
        </member>
        <member name="M:SnmpSharpNet.Counter32.#ctor(SnmpSharpNet.UInteger32)">
            <summary>Constructor</summary>
            <param name="uint32">UInteger32 value</param>
        </member>
        <member name="M:SnmpSharpNet.Counter32.#ctor(System.String)">
            <summary>Constructor</summary>
            <param name="val">Unsigned integer encoded in a string.</param>
        </member>
        <member name="M:SnmpSharpNet.Counter32.#ctor(System.UInt32)">
            <summary>Constructor.</summary>
            <param name="val">UInt32 value</param>
        </member>
        <member name="M:SnmpSharpNet.Counter32.Clone">
            <summary>Duplicate current object</summary>
            <returns>Duplicate of the current object cast as Object class.</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter32.Diff(SnmpSharpNet.Counter32,SnmpSharpNet.Counter32)">
            <summary>
            Return difference between two Counter32 values taking counter roll-over into account.
            </summary>
            <param name="first">First or older value</param>
            <param name="second">Second or newer value</param>
            <returns>Difference between the two values</returns>
        </member>
        <member name="T:SnmpSharpNet.Counter64">
            <summary>ASN.1 Counter64 value implementation.</summary>
            <remarks>
            Counter64 value is unsigned 64-bit integer value that is incremented by the agent
            until maximum value is reached. When maximum value is reached, Counter64 value will
            roll over to 0.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.Counter64._value">
            <summary>
            Internal 64-bit unsigned integer value.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Counter64.#ctor">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Counter64.#ctor(System.Int64)">
            <summary>Constructor.</summary>
            <param name="value">Value to set class value.
            </param>
        </member>
        <member name="M:SnmpSharpNet.Counter64.#ctor(SnmpSharpNet.Counter64)">
            <summary>
            Constructor
            </summary>
            <param name="value">Copy value</param>
        </member>
        <member name="M:SnmpSharpNet.Counter64.#ctor(System.UInt64)">
            <summary>Constructor.</summary>
            <param name="value">Value to initialize the class with.
            </param>
        </member>
        <member name="M:SnmpSharpNet.Counter64.#ctor(System.String)">
            <summary>Constructor.</summary>
            <remarks>
            Initialize the class by parsing a 64-bit unsigned integer value
            from the supplied string value.
            </remarks>
            <param name="value">64-bit unsigned integer value encoded as a string.
            </param>
        </member>
        <member name="P:SnmpSharpNet.Counter64.Value">
            <summary>Get/Set class 64-bit unsigned value
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Counter64.Set(SnmpSharpNet.AsnType)">
            <summary>
            SET class value from another Counter64 class cast as <seealso cref="T:SnmpSharpNet.AsnType"/>.
            </summary>
            <param name="value">Counter64 class cast as <seealso cref="T:SnmpSharpNet.AsnType"/></param>
            <exception cref="T:System.ArgumentException">Argument is not Counter64 type.</exception>
        </member>
        <member name="M:SnmpSharpNet.Counter64.Set(System.String)">
            <summary>
            Parse an Counter64 value from a string.
            </summary>
            <param name="value">String containing an Counter64 value</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Argument length is 0 (zero)</exception>
            <exception cref="T:System.ArgumentException">Unable to parse Counter64 value from the argument.</exception>
        </member>
        <member name="M:SnmpSharpNet.Counter64.Clone">
            <summary>Duplicate current object.</summary>
            <returns>Duplicate of the current object.</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.GetHashCode">
            <summary>
            Return class value hash code
            </summary>
            <returns>Int32 hash of the class stored value</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.Equals(System.Object)">
            <summary>
            Compare class value against the object argument. Supported argument types are 
            <see cref="T:SnmpSharpNet.Counter64"/> and UInt64.
            </summary>
            <param name="obj">Object to compare values with</param>
            <returns>True if object value is the same as this class, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.ToString">
            <summary>
            Returns the string representation of the object.
            </summary>
            <returns>String representation of the object value</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.op_Implicit(SnmpSharpNet.Counter64)~System.UInt64">
            <summary>
            Implicit casting of Counter64 value as UInt64 value
            </summary>
            <param name="value">Counter64 class whose value is cast as UInt64 value</param>
            <returns>UInt64 value of the Counter64 class.</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.encode(SnmpSharpNet.MutableByte)">
            <summary>BER encode class value</summary>
            <param name="buffer">MutableByte to append BER encoded value to.
            </param>
        </member>
        <member name="M:SnmpSharpNet.Counter64.decode(System.Byte[],System.Int32)">
            <summary>Decode BER encoded Counter64 value
            </summary>
            <param name="buffer">The encoded ASN.1 data</param>
            <param name="offset">Offset to start value decoding from.</param>
            <returns>Offset after the parsed value.</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.CompareTo(System.UInt64)">
            <summary>
            Compare class value with the UInt64 variable
            </summary>
            <param name="other">Variable to compare with</param>
            <returns>less then 0 if if parameter is less then, 0 if paramater is equal and greater then 0 if parameter is greater then the class value</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.CompareTo(SnmpSharpNet.Counter64)">
            <summary>
            Compare class value with the value of the second class
            </summary>
            <param name="other">Class whose value we are comparing with</param>
            <returns>less then 0 if if parameter is less then, 0 if paramater is equal and greater then 0 if parameter is greater then the class value</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.op_Equality(SnmpSharpNet.Counter64,SnmpSharpNet.Counter64)">
            <summary>
            Check for equality of class values
            </summary>
            <param name="first">First class value to compare</param>
            <param name="second">Second class value to compare</param>
            <returns>True if values are the same, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.op_Inequality(SnmpSharpNet.Counter64,SnmpSharpNet.Counter64)">
            <summary>
            Negative comparison
            </summary>
            <param name="first">First class value to compare</param>
            <param name="second">Second class value to compare</param>
            <returns>False if values are the same, otherwise true</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.op_GreaterThan(SnmpSharpNet.Counter64,SnmpSharpNet.Counter64)">
            <summary>
            Greater then operator
            </summary>
            <remarks>Compare two Counter64 class values and return true if first class value is greater then second.</remarks>
            <param name="first">First class</param>
            <param name="second">Second class</param>
            <returns>True if first class value is greater then second class value, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.op_LessThan(SnmpSharpNet.Counter64,SnmpSharpNet.Counter64)">
            <summary>
            Less then operator
            </summary>
            <remarks>Compare two Counter64 class values and return true if first class value is less then second.</remarks>
            <param name="first">First class</param>
            <param name="second">Second class</param>
            <returns>True if first class value is less then second class value, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.op_Addition(SnmpSharpNet.Counter64,SnmpSharpNet.Counter64)">
            <summary>
            Addition operator.
            </summary>
            <remarks>
            Add two Counter64 object values. Values of the two objects are added and
            a new class is instantiated with the result. Original values of the two parameter classes
            are preserved.
            </remarks>
            <param name="first">First Counter64 object</param>
            <param name="second">Second Counter64 object</param>
            <returns>New object with values of the 2 parameter objects added. If both parameters are null
            references then null is returned. If either of the two parameters is null, the non-null objects
            value is set as the value of the new object and returned.</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.op_Subtraction(SnmpSharpNet.Counter64,SnmpSharpNet.Counter64)">
            <summary>
            Subtraction operator
            </summary>
            <remarks>
            Subtract the value of the second Counter64 class value from the first Counter64 class value. 
            Values of the two objects are subtracted and a new class is instantiated with the result. 
            Original values of the two parameter classes are preserved.
            </remarks>
            <param name="first">First Counter64 object</param>
            <param name="second">Second Counter64 object</param>
            <returns>New object with subtracted values of the 2 parameter objects. If both parameters are null
            references then null is returned. If either of the two parameters is null, the non-null objects
            value is set as the value of the new object and returned.</returns>
        </member>
        <member name="M:SnmpSharpNet.Counter64.Diff(SnmpSharpNet.Counter64,SnmpSharpNet.Counter64)">
            <summary>
            Return difference between two Counter64 values taking counter roll-over into account.
            </summary>
            <param name="first">First or older value</param>
            <param name="second">Second or newer value</param>
            <returns>Difference between the two values</returns>
        </member>
        <member name="T:SnmpSharpNet.EndOfMibView">
            <summary>
            Returned when end of MIB has been reached when performing GET-NEXT or GET-BULK operations.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.EndOfMibView.#ctor">
            <summary> The default class construtor.</summary>
        </member>
        <member name="M:SnmpSharpNet.EndOfMibView.#ctor(SnmpSharpNet.EndOfMibView)">
            <summary> The class copy constructor.
            </summary>
            <param name="second">The object to copy into self.
            </param>
        </member>
        <member name="M:SnmpSharpNet.EndOfMibView.Clone">
            <summary> Returns a duplicate object of self. 
            </summary>
            <returns> A duplicate of self
            </returns>
        </member>
        <member name="M:SnmpSharpNet.EndOfMibView.decode(System.Byte[],System.Int32)">
            <summary>Decode ASN.1 encoded end-of-mib-view SNMP version 2 MIB value</summary>
            <param name="buffer">The encoded buffer</param>
            <param name="offset">The offset of the first byte of encoded data</param>
            <returns>Offset after the decoded value</returns>
        </member>
        <member name="M:SnmpSharpNet.EndOfMibView.encode(SnmpSharpNet.MutableByte)">
            <summary>
            ASN.1 encode end-of-mib-view SNMP version 2 MIB value
            </summary>
            <param name="buffer">MutableByte to append encoded variable to</param>
        </member>
        <member name="M:SnmpSharpNet.EndOfMibView.ToString">
            <summary> Returns the string representation of the object.
            </summary>
            <returns>String prepresentation of the object.</returns>
        </member>
        <member name="T:SnmpSharpNet.EthernetAddress">
            <summary>EthernetAddress class encapsulates a 6 byte OctetString
            representing an Ethernet MAC address.
            </summary>
            <remarks>THis class doesn't not represent a distinct ASN.1 data type. It is a helper
            class to allow users to perform MAC address specific operations on OctetString values.</remarks>
        </member>
        <member name="M:SnmpSharpNet.EthernetAddress.#ctor">
            <summary>Constructor. Initialize the class to 0000.0000.0000
            </summary>
        </member>
        <member name="M:SnmpSharpNet.EthernetAddress.#ctor(System.Byte[])">
            <summary>
            Constructor. Initialize the class with the value of the byte array.
            </summary>
            <param name="data">Byte array whose value is used to initialize the class.
            </param>
        </member>
        <member name="M:SnmpSharpNet.EthernetAddress.#ctor(SnmpSharpNet.EthernetAddress)">
            <summary>
            Constructor. Initialize the class with the value from the argument class.
            </summary>
            <param name="second">Class whose value is copied to initialize this class.
            </param>
        </member>
        <member name="M:SnmpSharpNet.EthernetAddress.#ctor(SnmpSharpNet.OctetString)">
            <summary>Constructor. Initialize the class with the value from the <see cref="T:SnmpSharpNet.OctetString"/> argument.
            </summary>
            <param name="second">Class whose value is used to initialize this class.
            </param>
        </member>
        <member name="M:SnmpSharpNet.EthernetAddress.Clone">
            <summary> Create a new object that is a duplicate of the
            current object.
            </summary>
            <returns> A newly created duplicate object.
            </returns>
        </member>
        <member name="M:SnmpSharpNet.EthernetAddress.Set(System.String)">
            <summary>Parses hex string representing an Ethernet MAC address to the enternal format. Ethernet
            address has to contain 12 hex characters (1-9 or A-F) to be parsed correctly. Special formatting is
            ignored so both 0000.0010.0000 and 00-00-00-10-00-00 will be parsed ok.
            </summary>
            <param name="value">Ethernet address represented as a string.
            </param>
        </member>
        <member name="M:SnmpSharpNet.EthernetAddress.ToString">
            <summary>
            Return Ethernet MAC address as a string formatted as: xxxx.xxxx.xxxx
            </summary>
            <returns>String representation of the object value.</returns>
        </member>
        <member name="T:SnmpSharpNet.Gauge32">
            <summary>ASN.1 Gauge32 value class.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Gauge32.#ctor">
            <summary> Constructs the default counter object.
            The initial value is defined
            by the super class default constructor
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Gauge32.#ctor(SnmpSharpNet.Gauge32)">
            <summary> Constructs a new object with the same value
            as the passed object.
            </summary>
            <param name="second">The object to recover values from.
            </param>
        </member>
        <member name="M:SnmpSharpNet.Gauge32.#ctor(SnmpSharpNet.UInteger32)">
            <summary> Constructs a new object with the value
            constrained in the UInteger32 object.
            </summary>
            <param name="uint32">The UInteger32 object to copy.
            </param>
        </member>
        <member name="M:SnmpSharpNet.Gauge32.#ctor(System.String)">
            <summary>Constructor. Initialize class value with the unsigned integer 32-bit value
            encoded as string in the argument.
            </summary>
            <param name="val">32-bit unsigned integer encoded as a string
            </param>
        </member>
        <member name="M:SnmpSharpNet.Gauge32.#ctor(System.UInt32)">
            <summary>
            Constructor
            </summary>
            <param name="val">Initializing value</param>
        </member>
        <member name="M:SnmpSharpNet.Gauge32.Clone">
            <summary>Duplicate current object.
            </summary>
            <returns>Copy of the object.</returns>
        </member>
        <member name="T:SnmpSharpNet.Integer32">
            <summary>ASN.1 Integer32 class.</summary>
            <remarks>
            This class defines the SNMP 32-bit signed integer
            used by the SNMP SMI. This class also serves as a 
            base class for any additional SNMP SMI types that 
            exits now or may be defined in the future.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.Integer32._value">
            <summary>Internal class value</summary>
        </member>
        <member name="M:SnmpSharpNet.Integer32.#ctor">
            <summary>Constructor
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Integer32.#ctor(System.Int32)">
            <summary>Constructor
            </summary>
            <param name="val">Class value initializer
            </param>
        </member>
        <member name="M:SnmpSharpNet.Integer32.#ctor(SnmpSharpNet.Integer32)">
            <summary>Copy constructor
            </summary>
            <param name="second">Class value initializer
            </param>
        </member>
        <member name="M:SnmpSharpNet.Integer32.#ctor(System.String)">
            <summary>Constructor
            </summary>
            <param name="val">Integer value in a string format.
            </param>
        </member>
        <member name="M:SnmpSharpNet.Integer32.Set(SnmpSharpNet.AsnType)">
            <summary>
            SET class value from another Integer32 class cast as <see cref="T:SnmpSharpNet.AsnType"/>.
            </summary>
            <param name="value">Integer32 class cast as <see cref="T:SnmpSharpNet.AsnType"/></param>
            <exception cref="T:System.ArgumentException">Argument is not Integer32 type.</exception>
        </member>
        <member name="M:SnmpSharpNet.Integer32.Set(System.String)">
            <summary>
            Parse an Integer32 value from a string.
            </summary>
            <param name="value">String containing an Integer32 value</param>
            <exception cref="T:System.ArgumentOutOfRangeException">Argument string is length == 0</exception>
            <exception cref="T:System.ArgumentException">Unable to parse Integer32 value from the argument.</exception>
        </member>
        <member name="P:SnmpSharpNet.Integer32.Value">
            <summary>
            Get/SET internal integer value
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Integer32.Clone">
            <summary> Returns a duplicate of the current object.</summary>
            <returns> A newly allocated duplicate object.</returns>
        </member>
        <member name="M:SnmpSharpNet.Integer32.ToString">
            <summary> Returns the string representation of the object.</summary>
            <returns>String representation of the class value.</returns>
        </member>
        <member name="M:SnmpSharpNet.Integer32.op_Implicit(SnmpSharpNet.Integer32)~System.Int32">
            <summary>
            Implicit casting of Integer32 value as Int32 value
            </summary>
            <param name="value">Integer32 class whose value is cast as Int32 value</param>
            <returns>Int32 value of the Integer32 class.</returns>
        </member>
        <member name="M:SnmpSharpNet.Integer32.GetHashCode">
            <summary>
            Return class value hash code
            </summary>
            <returns>Int32 hash of the class stored value</returns>
        </member>
        <member name="M:SnmpSharpNet.Integer32.SetRandom">
            <summary>
            Set class value to a random integer.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Integer32.encode(SnmpSharpNet.MutableByte)">
            <summary> Used to encode the integer value into an ASN.1 buffer.
            The passed encoder defines the method for encoding the
            data.
            </summary>
            <param name="buffer">Buffer target to write the encoded data</param>
        </member>
        <member name="M:SnmpSharpNet.Integer32.decode(System.Byte[],System.Int32)">
            <summary> Used to decode the integer value from the BER buffer.
            The passed encoder is used to decode the BER encoded information
            and the integer value is stored in the internal object.
            </summary>
            <param name="buffer">Buffer holding BER encoded data</param>
            <param name="offset">Offset in the buffer to start parsing from</param>
            <returns>Buffer position after the decoded value</returns>
        </member>
        <member name="M:SnmpSharpNet.Integer32.CompareTo(SnmpSharpNet.Integer32)">
            <summary>
            Compare implementation that will compare this class value with the value of another <see cref="T:SnmpSharpNet.Integer32"/> class.
            </summary>
            <param name="other">Integer32 value to compare class value with.</param>
            <returns>less then 0 if if parameter is less then, 0 if parameter is equal and greater then 0 if parameter is greater then the class value</returns>
        </member>
        <member name="M:SnmpSharpNet.Integer32.CompareTo(System.Int32)">
            <summary>
            Compare implementation that will compare this class value with argument Int32 value.
            </summary>
            <param name="other">Int32 value to compare class value with.</param>
            <returns>less then 0 if if parameter is less then, 0 if parameter is equal and greater then 0 if parameter is greater then the class value</returns>
        </member>
        <member name="M:SnmpSharpNet.Integer32.Equals(System.Object)">
            <summary>
            Compare class value against the object argument. Supported argument types are 
            <see cref="T:SnmpSharpNet.Integer32"/> and Int32.
            </summary>
            <param name="obj">Object to compare values with</param>
            <returns>True if object value is the same as this class, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.Integer32.op_Equality(SnmpSharpNet.Integer32,SnmpSharpNet.Integer32)">
            <summary>
            Comparison operator
            </summary>
            <param name="first">First <see cref="T:SnmpSharpNet.Integer32"/> class value to compare</param>
            <param name="second">Second <see cref="T:SnmpSharpNet.Integer32"/> class value to compare</param>
            <returns>True if class values match, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.Integer32.op_Inequality(SnmpSharpNet.Integer32,SnmpSharpNet.Integer32)">
            <summary>
            Negative comparison operator
            </summary>
            <param name="first">First <see cref="T:SnmpSharpNet.Integer32"/> class value to compare</param>
            <param name="second">Second <see cref="T:SnmpSharpNet.Integer32"/> class value to compare</param>
            <returns>True if class values do NOT match, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.Integer32.op_Addition(SnmpSharpNet.Integer32,SnmpSharpNet.Integer32)">
            <summary>
            Addition operator.
            </summary>
            <remarks>
            Add two Integer32 object values. Values of the two objects are added and
            a new class is instantiated with the result. Original values of the two parameter classes
            are preserved.
            </remarks>
            <param name="first">First Integer32 object</param>
            <param name="second">Second Integer32 object</param>
            <returns>New object with values of the 2 parameter objects added. If both parameters are null
            references then null is returned. If either of the two parameters is null, the non-null objects
            value is set as the value of the new object and returned.</returns>
        </member>
        <member name="M:SnmpSharpNet.Integer32.op_Subtraction(SnmpSharpNet.Integer32,SnmpSharpNet.Integer32)">
            <summary>
            Subtraction operator
            </summary>
            <remarks>
            Subtract the value of the second Integer32 class value from the first Integer32 class value. 
            Values of the two objects are subtracted and a new class is instantiated with the result. 
            Original values of the two parameter classes are preserved.
            </remarks>
            <param name="first">First Integer32 object</param>
            <param name="second">Second Integer32 object</param>
            <returns>New object with subtracted values of the 2 parameter objects. If both parameters are null
            references then null is returned. If either of the two parameters is null, the non-null objects
            value is set as the value of the new object and returned.</returns>
        </member>
        <member name="T:SnmpSharpNet.IpAddress">
            <summary>ASN.1 IPAddress type implementation</summary>
            <remarks>
            You can assign the IP address value to the class using:
            
            A string value representing the IP address in dotted decimal notation:
            <code>
            IpAddress ipaddr = new IpAddress("********");
            </code>
            
            A string value representing the hosts domain name:
            <code>
            IpAddress ipaddr = new IpAddress("this.ismyhost.com");
            </code>
            
            Another IpAddress class:
            <code>
            IpAddress first = new IpAddress("********");
            IpAddress second = new IpAddress(first);
            </code>
            
            Or from the System.Net.IPAddress:
            <code>
            IPAddress addr = IPAddress.Any;
            IpAddress ipaddr = new IpAddress(addr);
            </code>
            
            You can check if the IpAddress class contains a valid value by calling:
            <code>
            IpAddress ipaddr = new IpAddress("********");
            if( ! ipaddr.Valid ) {
              Console.WriteLine("Invalid IP Address value.");
              return;
            }
            </code>
            
            There are other operations you can perform with the IpAddress class. For example, let say
            you retrieved an IP address and subnet mask from an SNMP agent and wish to scan the subnet for
            other hosts that can be managed. You could do this:
            IpAddress host = SomehowRetrieveIPAddress();
            IpAddress mask = SomehowRetrieveSubnetMask();
            IpAddress subnetAddr = host.GetSubnetAddress(mask);
            IpAddress broadcastAddr = host.GetBroadcastAddress(mask);
            IpAddress host = (IpAddress)subnetAddr.Clone();
            while( host.CompareTo(broadcastAddr) != 0 ) {
              host = host.Increment(1); // increment IP address by one
              if( ! host.Equals(broadcastAddr) )
                ScanHostInWhateverWayYouLike(host);
            }
            
            Note: internally, IpAddress class holds the value in a byte array, network ordered format.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.#ctor">
            <summary> Constructs a default object with a 
            length of zero. See the super class
            constructor for more details.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.#ctor(System.Byte[])">
            <summary> Constructs an Application String with the
            passed data. The data is managed by the 
            base class.
            </summary>
            <param name="data">The application string to manage (UTF-8)
            </param>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.#ctor(SnmpSharpNet.IpAddress)">
            <summary> Copy constructor. Constructs a duplicate object 
            based on the passed application string object.
            </summary>
            <param name="second">The object to copy.</param>
            <exception cref="T:System.ArgumentException">IpAddress argument is null</exception>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.#ctor(SnmpSharpNet.OctetString)">
            <summary>Copy constructor.</summary>
            <param name="second">The object to copy</param>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.#ctor(System.Net.IPAddress)">
            <summary>Constructor</summary>
            <remarks>Construct the IpAddress class from supplied IPAddress value.</remarks>
            <param name="inetAddr">IP Address to use to initialize the object.</param>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.#ctor(System.String)">
            <summary>Constructs the class and initializes the value to the supplied IP address. See comments
            in <see cref="M:SnmpSharpNet.IpAddress.Set(System.String)"/> method for details.
            </summary>
            <param name="inetAddr">IP address encoded as a string or host name</param>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.#ctor(System.UInt32)">
            <summary>
            Constructor
            </summary>
            <remarks>Initialize IpAddress class with the IP address value represented as 32-bit unsigned integer
            value</remarks>
            <param name="inetAddr">32-bit unsigned integer representation of an IP Address</param>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.Clone">
            <summary>Clone current object.</summary>
            <returns> Cloned IpAddress object.</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.Set(System.String)">
            <summary>Sets the class value to the IP address parsed from the string parameter.</summary>
            <remarks>Class value will be set with the parsed dotted decimal IP address in the parameter or
            if string parameter does not represent an IP address, DNS resolution
            will be performed.
            
            Note: DNS resolution is performed using <see cref="M:System.Net.Dns.GetHostEntry(System.String)"/> and can result
            in a longer then expected delay in this function. In applications where responsiveness is
            important, name resolution should be performed using async methods available and result passed
            to this method.
            </remarks>
            <param name="value">IP address encoded as a string or host name</param>
            <exception cref="T:System.ArgumentException">Thrown when DNS resolution of the parameter value has failed.</exception>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.Set(System.UInt32)">
            <summary>
            Set class value from 32-bit unsigned integer value representation of the IP address value
            </summary>
            <param name="ipvalue">IP address value as 32-bit unsigned integer value</param>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.Set(System.Net.IPAddress)">
            <summary>
            Set class value from the IPAddress argument
            </summary>
            <param name="ipaddr">Type: <see cref="T:System.Net.IPAddress"/> class instance</param>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.op_Explicit(SnmpSharpNet.IpAddress)~System.Net.IPAddress">
            <summary>Allow explicit cast of the class as System.Net.IPAddress class.</summary>
            <param name="ipaddr">IpAddress class</param>
            <returns>IpAddress class value as System.Net.IPAddress class</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.ToString">
            <summary> Returns the application string as a dotted decimal represented IP address.</summary>
            <returns>String representation of the class value.</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.CompareTo(System.Object)">
            <summary>
            Compare value against IPAddress, byte array, UInt32, IpAddress of OctetString class value.
            </summary>
            <param name="obj">Type: <see cref="T:System.Net.IPAddress"/> or byte <see cref="T:System.Array"/> or <see cref="T:System.UInt32"/> or <see cref="T:SnmpSharpNet.IpAddress"/> or <see cref="T:SnmpSharpNet.OctetString"/></param>
            <returns>0 if class values are the same, -1 if current class value is less then or 1 if greater then the class value
            we are comparing against.</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.Equals(System.Object)">
            <summary>
            Compare 2 IpAddress objects.
            </summary>
            <param name="obj"><see cref="T:SnmpSharpNet.IpAddress"/> object to compare against</param>
            <returns>True if objects are the same, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.GetHashCode">
            <summary>
            Return hash representing the value of this object
            </summary>
            <returns>Integer hash representing the object</returns>
        </member>
        <member name="P:SnmpSharpNet.IpAddress.Valid">
            <summary>
            Returns true if object contains a valid IP address value.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.decode(System.Byte[],System.Int32)">
            <summary>Decode ASN.1 encoded IP address value.</summary>
            <param name="buffer">BER encoded data buffer</param>
            <param name="offset">Offset in the array to start parsing from</param>
            <returns>Buffer position after the decoded value.</returns>
            <exception cref="T:System.OverflowException">Parsed data length is not 4 bytes long</exception>
            <exception cref="T:SnmpSharpNet.SnmpException">Parsed data is not in IpAddress format</exception>
        </member>
        <member name="F:SnmpSharpNet.IpAddress.ClassA">
            <summary>
            Class A IP address
            </summary>
        </member>
        <member name="F:SnmpSharpNet.IpAddress.ClassB">
            <summary>
            Class B IP address
            </summary>
        </member>
        <member name="F:SnmpSharpNet.IpAddress.ClassC">
            <summary>
            Class C IP address
            </summary>
        </member>
        <member name="F:SnmpSharpNet.IpAddress.ClassD">
            <summary>
            Class D IP address
            </summary>
        </member>
        <member name="F:SnmpSharpNet.IpAddress.ClassE">
            <summary>
            Class E IP address
            </summary>
        </member>
        <member name="F:SnmpSharpNet.IpAddress.InvalidClass">
            <summary>
            Invalid IP address class
            </summary>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.GetClass">
            <summary>
            Return network class of the IP address
            </summary>
            <returns>Integer network class. Return values are <see cref="F:SnmpSharpNet.IpAddress.ClassA"/>, <see cref="F:SnmpSharpNet.IpAddress.ClassB"/>, <see cref="F:SnmpSharpNet.IpAddress.ClassC"/>,
            <see cref="F:SnmpSharpNet.IpAddress.ClassD"/>, <see cref="F:SnmpSharpNet.IpAddress.ClassE"/>, or <see cref="F:SnmpSharpNet.IpAddress.InvalidClass"/> on error.</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.ToUInt32">
            <summary>
            Network byte order IP address
            </summary>
            <remarks>
            Convert internal byte array representation of the IP address into a network byte order 
            (most significant byte first) unsigned integer value.
            
            To use the returned value in mathematical operations, you will need to change it to
            host byte order on little endian systems (all i386 compatible systems). To do that,
            call static method IpAddress.ReverseByteOrder().
            </remarks>
            <returns>Unsigned integer value representing the IP address in network byte order</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.GetSubnetAddress(SnmpSharpNet.IpAddress)">
            <summary>
            Return subnet address of the IP address in this object and supplied subnet mask
            </summary>
            <param name="mask">Subnet mask</param>
            <returns>New IpAddress object containing subnet address</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.Invert">
            <summary>
            Inverts IP address value. All 0s are converted to 1s and 1s to 0s
            </summary>
            <example>
            <code lang="cs">
            IpAddress testMask = new IpAddress("***********");
            IpAddress invMask = testMask.Invert();
            Console.WriteLine("original: {0}", testMask.ToString());
            // Output: original: ***********
            Console.WriteLine("inverted: {0}", invMask.ToString());
            // Output: inverted: ***********
            </code>
            </example>
            <returns>Inverted value of the IP address</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.GetBroadcastAddress(SnmpSharpNet.IpAddress)">
            <summary>
            Returns broadcast address for the objects IP and supplied subnet mask.
            
            Subnet mask validity is not confirmed before performing the new value calculation so
            you should make sure parameter is a valid subnet mask by using <see cref="M:SnmpSharpNet.IpAddress.IsValidMask"/>
            method.
            
            Broadcast address is calculated by changing every bit in the subnet mask that is set to
            value 0 into 1 in the address value.
            </summary>
            <param name="mask">Subnet mask to apply to the object</param>
            <returns>New IP address containing broadcast address</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.NetworkMask">
            <summary>
            Returns network mask for the class value
            </summary>
            <remarks>Each IpAddress belongs to one of the 5 classes, A, B, C, D or E. D network class are multicast
            addresses and E is experimental class. Because D and E are not "real" network classes and as such don't
            have a network mask, only classes A, B and C will return a valid subnet mask. Classes D and E will return
            a null reference.</remarks>
            <returns>Network mask or null if address doesn't belong to classes A, B or C</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.IsValidMask">
            <summary>
            Checks if the value of the object is a valid subnet mask.
            
            Subnet mask is a value of consecutive bits with value of 1. If a bit is found with a value of 0
            followed (immediately or anywhere else before the end of the value) by a 1, then subnet
            mask value is not valid.
            </summary>
            <returns>true if valid subnet mask, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.GetMaskBits">
            <summary>
            Returns number of subnet bits in the mask.
            
            Subnet bits are bits set to value 0 in the subnet mask.
            </summary>
            <example>
            	<code lang="cs">
            	IpAddress mask = new IpAddress("*************");
            	Console.WriteLine("Mask bits: {0}", mask.GetMaskBits()); # Print "Mask bits: 24"
            	mask.Set("***************");
            	Console.WriteLine("Mask bits: {0}", mask.GetMaskBits()); # Print "Mask bits: 25"
            	</code>
            </example>
            <returns>Set bits in the subnet mask or 0 if invalid subnet mask</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.BuildMaskFromBits(System.Int32)">
            <summary>
            Build a subnet mask from bit count value
            </summary>
            <example>This example shows how to use IpAddress.BuildMaskFromBits() method:
            <code lang="cs">
            Console.WriteLine("mask 1: {0}", IpAddress.BuildMaskFromBits(23));
            Console.WriteLine("mask 2: {0}", IpAddress.BuildMaskFromBits(24));
            Console.WriteLine("mask 3: {0}", IpAddress.BuildMaskFromBits(25));
            <i>Result:</i>
            mask 1: *************
            mask 2: *************
            mask 3: ***************
            </code>
            </example>
            <param name="bits">Number of subnet mask bits</param>
            <returns>New IPAddress class containing the subnet mask</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.ReverseByteOrder(System.UInt32)">
            <summary>
            Reverse int value byte order. Static function in IPAddress class doesn't work 
            the way I expected it (didn't troubleshoot) so here is another implementation.
            
            Reversing byte order is needed because network encoded IP address is presented in big-endian
            format. Intel based computers encode values in little-endian form so to perform numerical operations
            on received IP address values you will need to convert them from big to little-endian format.
            
            By the same token, if you wish to transmit data using IP address value calculated on the
            local system, you will need to convert it to big-endian prior to transmission.
            </summary>
            <example>
            Here is an example that demonstrates what this method does:
            <code lang="cs">
            IpAddress test = new IpAddress(new byte[] { 10, 20, 30, 40 }); // Example IP: ***********
            UInt32 testValue = test.ToUInt32();
            UInt32 reverseValue = IpAddress.ReverseByteOrder(testValue);
            IpAddress newTest = new IpAddress(reverseValue);
            Console.WriteLine("New IP: {0}", newTest.ToString()); // Output "New IP: ***********"		/// </code>
            </example>
            <param name="val">Integer value to reverse</param>
            <returns>Reversed integer value</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.Increment(System.UInt32)">
            <summary>
            Increment IP address contained in the object by specific number and return the result as a new
            class.
            </summary>
            <example>
            <code lang="cs">
            IpAddress addr = new IpAddress("**********");
            Console.WriteLine("Starting IP: {0}", addr.ToString());
            // Output: Starting IP: **********
            IpAddress incrAddr1 = addr.Increment(1);
            Console.WriteLine("Incremented by 1: {0}", incrAddr1.ToString());
            // Output: Incremented by 1: **********
            IpAddress incrAddr10 = addr.Increment(10);
            Console.WriteLine("Incremented by 10: {0}", incrAddr10.ToString());
            // Output: Incremented by 10: ***********
            </code>
            </example>
            <param name="count">Number to increment IP address with</param>
            <returns>New class representing incremented IP address</returns>
        </member>
        <member name="M:SnmpSharpNet.IpAddress.IsIP(System.String)">
            <summary>
            Check if supplied string contains a valid IP address.
            
            Valid IP address contains 3 full stops (".") and 4 numeric values in range 0 to 255.
            </summary>
            <remarks>
            Written to address slow performance of IPAddress.TryParse
            </remarks>
            <param name="val">String value</param>
            <returns>true if string contains an IP address in dotted decimal format, otherwise false</returns>
        </member>
        <member name="T:SnmpSharpNet.NoSuchInstance">
            <summary>SNMPv2 noSuchInstance error</summary>
            <remarks>
            Returned when requested instance is not present in a table of the SNMP version 2 agent.
            
            This type is returned as value to a requested Oid. When looping through results, check Vb.Value
            member for V2Error returns.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.NoSuchInstance.#ctor">
            <summary>Constructor.</summary>
        </member>
        <member name="M:SnmpSharpNet.NoSuchInstance.#ctor(SnmpSharpNet.NoSuchInstance)">
            <summary>Constructor.</summary>
            <param name="second">The object to copy into self.</param>
        </member>
        <member name="M:SnmpSharpNet.NoSuchInstance.Clone">
            <summary>Returns a duplicate object of self.</summary>
            <returns>A duplicate of self</returns>
        </member>
        <member name="M:SnmpSharpNet.NoSuchInstance.decode(System.Byte[],System.Int32)">
            <summary>Decode BER encoded no-such-instance SNMP version 2 MIB value</summary>
            <param name="buffer">The BER encoded buffer</param>
            <param name="offset">The offset of the first byte of encoded data</param>
            <returns>Buffer position after the decoded value</returns>
            <exception cref="T:SnmpSharpNet.SnmpException">Invalid ASN.1 type found when parsing value header</exception>
            <exception cref="T:SnmpSharpNet.SnmpDecodingException">Invalid data length in ASN.1 header. Only data length 0 is accepted.</exception>
        </member>
        <member name="M:SnmpSharpNet.NoSuchInstance.encode(SnmpSharpNet.MutableByte)">
            <summary>
            ASN.1 encode no-such-instance SNMP version 2 MIB value
            </summary>
            <param name="buffer">MutableByte reference to append encoded variable to</param>
        </member>
        <member name="M:SnmpSharpNet.NoSuchInstance.ToString">
            <summary> Returns the string representation of the object.</summary>
            <returns>String representatio of the class</returns>
        </member>
        <member name="T:SnmpSharpNet.NoSuchObject">
            <summary>SNMPv2 NoSuchObject error</summary>
            <remarks>
            NoSuchObject is returned by the agent in response to a SNMP version 2 request 
            when requested object does not exist in its MIB.
            This value is returned as a <seealso cref="P:SnmpSharpNet.Vb.Value"/> with data of length 0
            
            For example:
            <code lang="cs">
            // [... prepare for a get operation ...]
            Pdu response = target.Request(outpdu, params);
            foreach(Vb vb in response.VbList) {
            	if( vb.Value is NoSuchObject ) {
            		return "Requested MIB variable does not exist on the agent.";
            	}
            }
            </code>
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.NoSuchObject.#ctor">
            <summary>Constructor.</summary>
        </member>
        <member name="M:SnmpSharpNet.NoSuchObject.#ctor(SnmpSharpNet.NoSuchObject)">
            <summary>
            Constructor
            </summary>
            <param name="second">
            Source for data to initialize this instance with.
            
            Irrelevant for this type since no data is stored in the class.
            </param>
        </member>
        <member name="M:SnmpSharpNet.NoSuchObject.Clone">
            <summary> Returns a duplicate object of self.</summary>
            <returns> A duplicate of self</returns>
        </member>
        <member name="M:SnmpSharpNet.NoSuchObject.decode(System.Byte[],System.Int32)">
            <summary>Decode ASN.1 encoded no-such-object SNMP version 2 MIB value</summary>
            <param name="buffer">The encoded buffer</param>
            <param name="offset">The offset of the first byte of encoded data</param>
            <returns>Buffer position after the decoded value</returns>
        </member>
        <member name="M:SnmpSharpNet.NoSuchObject.encode(SnmpSharpNet.MutableByte)">
            <summary>
            ASN.1 encode no-such-object SNMP version 2 MIB value
            </summary>
            <param name="buffer">MutableByte reference to append encoded variable to</param>
        </member>
        <member name="M:SnmpSharpNet.NoSuchObject.ToString">
            <summary> Returns the string representation of the object.</summary>
            <returns>String representation of the class</returns>
        </member>
        <member name="T:SnmpSharpNet.Null">
            <summary>ASN.1 Null value implementation.</summary>
        </member>
        <member name="M:SnmpSharpNet.Null.#ctor">
            <summary>Constructor</summary>
        </member>
        <member name="M:SnmpSharpNet.Null.#ctor(SnmpSharpNet.Null)">
            <summary>Constructor</summary>
            <param name="second">Irrelevant. Nothing to copy</param>
        </member>
        <member name="M:SnmpSharpNet.Null.encode(SnmpSharpNet.MutableByte)">
            <summary> 
            ASN.1 encode Null value
            </summary>
            <param name="buffer"><see cref="T:SnmpSharpNet.MutableByte"/> class to the end of which encoded data is appended
            </param>
        </member>
        <member name="M:SnmpSharpNet.Null.decode(System.Byte[],System.Int32)">
            <summary>
            Decode null value from BER encoded buffer.
            </summary>
            <param name="buffer">BER encoded buffer</param>
            <param name="offset">Offset within the buffer from where to start decoding. On return,
            this argument contains the offset immediately following the decoded value.
            </param>
            <returns>Buffer position after the decoded value</returns>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown when parsed ASN.1 type is not null</exception>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown when length of null value is greater then 0 bytes</exception>
        </member>
        <member name="M:SnmpSharpNet.Null.Clone">
            <summary>Clone current object</summary>
            <returns>Duplicate of the current object.</returns>
        </member>
        <member name="M:SnmpSharpNet.Null.ToString">
            <summary> Returns a string representation of the SNMP NULL object</summary>
            <returns>String representation of the class</returns>
        </member>
        <member name="T:SnmpSharpNet.OctetString">
            <summary>ASN.1 OctetString type implementation</summary>
        </member>
        <member name="F:SnmpSharpNet.OctetString._data">
            <summary>Data buffer</summary>
        </member>
        <member name="M:SnmpSharpNet.OctetString.#ctor">
            <summary>Constructor</summary>
        </member>
        <member name="M:SnmpSharpNet.OctetString.#ctor(System.String)">
            <summary> Constructs an Octet String with the contents of the supplied string.
            </summary>
            <param name="data">String data to convert into OctetString class value.</param>
        </member>
        <member name="M:SnmpSharpNet.OctetString.#ctor(System.Byte[])">
            <summary>Constructs the object and sets the data buffer to the byte array values
            </summary>
            <param name="data">Byte array to copy into the data buffer</param>
        </member>
        <member name="M:SnmpSharpNet.OctetString.#ctor(System.Byte[],System.Boolean)">
            <summary>
            Construct the class and set value. Value can be set by reference (assigned passed array parameter
            to the interal class variable) or by value (copy data in the array into a new buffer).
            </summary>
            <param name="data">Byte array to set class value to</param>
            <param name="useReference">If true, set class value to reference byte array parameter, otherwise copy data into new internal byte array</param>
        </member>
        <member name="M:SnmpSharpNet.OctetString.#ctor(SnmpSharpNet.OctetString)">
            <summary>Constructor creating class from values in the supplied class.</summary>
            <param name="second">OctetString object to copy data from.</param>
        </member>
        <member name="M:SnmpSharpNet.OctetString.#ctor(System.Byte)">
            <summary>
            Constructor. Initialize the class value to a 1 byte array with the supplied value
            </summary>
            <param name="data">Value to initialize the class data to.</param>
        </member>
        <member name="P:SnmpSharpNet.OctetString.Length">
            <summary>Get length of the internal byte array. 0 if byte array is undefined or zero length.</summary>
        </member>
        <member name="M:SnmpSharpNet.OctetString.GetData">
            <summary>
            Internal method to return OctetString byte array. Used for copy operations, comparisons and similar within the
            library. Not available for users of the library
            </summary>
            <returns></returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.Clear">
            <summary>
            Empty data buffer
            </summary>
        </member>
        <member name="M:SnmpSharpNet.OctetString.ToArray">
            <summary>
            Convert the OctetString class to a byte array. Internal class data buffer is *copied* and not passed to the caller.
            </summary>
            <returns>Byte array representing the OctetString class data</returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.Set(System.String)">
            <summary>Set object value to bytes from the supplied string. If argument string length == 0, internal OctetString
            buffer is set to null.</summary>
            <param name="value">String containing new class data</param>
        </member>
        <member name="M:SnmpSharpNet.OctetString.Set(System.Byte[])">
            <summary>
            Set class value from the argument byte array. If byte array argument is null or length == 0,
            internal <see cref="T:SnmpSharpNet.OctetString"/> buffer is set to null.
            </summary>
            <param name="data">Byte array to copy data from.</param>
        </member>
        <member name="M:SnmpSharpNet.OctetString.Set(System.Byte)">
            <summary>
            Set class value to an array 1 byte long and set the value to the supplied argument.
            </summary>
            <param name="data">Byte value to initialize the class value with</param>
        </member>
        <member name="M:SnmpSharpNet.OctetString.Set(System.Int32,System.Byte)">
            <summary>
            Set value at specified position to the supplied value
            </summary>
            <param name="position">Zero based offset from the beginning of the buffer</param>
            <param name="value">Value to set</param>
        </member>
        <member name="M:SnmpSharpNet.OctetString.SetRef(System.Byte[])">
            <summary>
            Set class value to reference of parameter byte array
            </summary>
            <param name="data">Data buffer parameter</param>
        </member>
        <member name="M:SnmpSharpNet.OctetString.Append(System.String)">
            <summary>
            Append string value to the OctetString class. If current class content is length 0, new
            string value is set as the value of this class.
            
            Class assumes that string value is UTF8 encoded.
            </summary>
            <param name="value">UTF8 encoded string value</param>
        </member>
        <member name="M:SnmpSharpNet.OctetString.Append(System.Byte[])">
            <summary>
            Append contents of the byte array to the class value. If class value is length 0, byte array
            content is set as the class value.
            </summary>
            <param name="value">Byte array</param>
        </member>
        <member name="P:SnmpSharpNet.OctetString.Item(System.Int32)">
            <summary>
            Indexed access to the OctetString class data members.
            <code>
            OctetString os = new OctetString("test");
            for(int i = 0;i &lt; os.Length;i++) {
             Console.WriteLine("{0}",os[i]);
            }
            </code>
            </summary>
            <param name="index">Index position of the data value to access</param>
            <returns>Byte value at the index position. 0 if index is out of range</returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.Clone">
            <summary>Creates a duplicate copy of the object and returns it to the caller.</summary>
            <returns> A newly constructed copy of self</returns>
        </member>
        <member name="P:SnmpSharpNet.OctetString.IsHex">
            <summary>
            Return true if OctetString contains non-printable characters, otherwise return false.
            </summary>
            <remarks>Values recognized as hex are byte values less then decimal 32 that are not decimal
            10 or 13 and byte values that are greater then 127 decimal. One exception is byte value 0x00
            when it is at the end of the byte array is not considered a hex value but a c-like string
            termination character.</remarks>
        </member>
        <member name="M:SnmpSharpNet.OctetString.ToMACAddressString">
            <summary>Utility function to print a MAC address (binary string of 6 byte length.</summary>
            <returns>If data is of the correct length (6 bytes), string representing hex mac address in the
            format xxxx.xxxx.xxxx. If data is not of the correct length, empty string is returned.</returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.ToString">
            <summary>Return string representation of the OctetStrig object. If non-printable characters have been
            found in the object, output is a hex representation of the string.
            </summary>
            <returns>String representation of the object.</returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.ToHexString">
            <summary>
            Return string formatted hexadecimal representation of the objects value.
            </summary>
            <returns>String representation of hexadecimal formatted class value.</returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.Equals(System.Object)">
            <summary>
            Compare against another object. Acceptable object types are <see cref="T:SnmpSharpNet.OctetString"/> and
            <see cref="T:System.String"/>.
            </summary>
            <param name="obj">Object of type <see cref="T:SnmpSharpNet.OctetString"/> or <see cref="T:System.String"/> to compare against</param>
            <returns>true if object content is the same, false if different or if incompatible object type</returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.GetHashCode">
            <summary>
            Dummy override to prevent compiler warning messages.
            </summary>
            <returns>Nothing of interest</returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.op_Equality(SnmpSharpNet.OctetString,SnmpSharpNet.OctetString)">
            <summary>
            Overloading equality operator
            </summary>
            <param name="str1">Source (this) string</param>
            <param name="str2">String to compare with</param>
            <returns>True if equal, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.op_Inequality(SnmpSharpNet.OctetString,SnmpSharpNet.OctetString)">
            <summary>
            Negative equality operator
            </summary>
            <param name="str1">Source (this) string</param>
            <param name="str2">String to compare with</param>
            <returns>True if not equal, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.CompareTo(System.Byte[])">
            <summary>
            IComparable interface implementation. Compare class contents with contents of the byte array.
            </summary>
            <param name="other">Byte array to compare against</param>
            <returns>-1 if class value is greater (longer or higher value), 1 if byte array is greater or 0 if the same</returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.CompareTo(SnmpSharpNet.OctetString)">
            <summary>
            IComparable interface implementation. Compare class contents against another class.
            </summary>
            <param name="other">OctetString class to compare against.</param>
            <returns>-1 if class value is greater (longer or higher value), 1 if byte array is greater or 0 if the same</returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.op_Implicit(SnmpSharpNet.OctetString)~System.Byte[]">
            <summary>
            Implicit operator allowing cast of OctetString objects to byte[] array
            </summary>
            <param name="oStr">OctetString to cast as byte array</param>
            <returns>Byte array value of the supplied OctetString</returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.Reset">
            <summary>
            Reset internal buffer to null.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.OctetString.encode(SnmpSharpNet.MutableByte)">
            <summary>BER encode OctetString variable.</summary>
            <param name="buffer"><see cref="T:SnmpSharpNet.MutableByte"/> encoding destination.</param>
        </member>
        <member name="M:SnmpSharpNet.OctetString.decode(System.Byte[],System.Int32)">
            <summary>
            Decode OctetString from the BER format. 
            </summary>
            <param name="buffer">BER encoded buffer</param>
            <param name="offset">Offset in the <see cref="T:SnmpSharpNet.MutableByte"/> to start the decoding from</param>
            <returns>Buffer position after the decoded value</returns>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown if parsed data type is invalid.</exception>
        </member>
        <member name="M:SnmpSharpNet.OctetString.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the OctetString byte collection
            </summary>
            <returns>An IEnumerator  object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:SnmpSharpNet.OctetString.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the OctetString byte collection
            </summary>
            <returns>An IEnumerator  object that can be used to iterate through the collection.</returns>
        </member>
        <member name="T:SnmpSharpNet.Oid">
            <summary>
            SMI Object Identifier type implementation.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.Oid._data">
            <summary>Internal buffer</summary>
        </member>
        <member name="M:SnmpSharpNet.Oid.#ctor">
            <summary> Creates a default empty object identifier.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Oid.#ctor(System.UInt32[])">
            <summary>Constructor. Initialize ObjectId value to the unsigned integer array</summary>
            <param name="data">Integer array representing objectId</param>
        </member>
        <member name="M:SnmpSharpNet.Oid.#ctor(System.Int32[])">
            <summary>Constructor. Initialize ObjectId value to integer array</summary>
            <param name="data">Integer array representing objectId</param>
        </member>
        <member name="M:SnmpSharpNet.Oid.#ctor(SnmpSharpNet.Oid)">
            <summary>Constructor. Duplicate objectId value from argument.</summary>
            <param name="second">objectId whose value is used to initilize this class value</param>
        </member>
        <member name="M:SnmpSharpNet.Oid.#ctor(System.String)">
            <summary>Constructor. Initialize objectId value from string argument.</summary>
            <param name="value">String value representing objectId</param>
        </member>
        <member name="M:SnmpSharpNet.Oid.Set(System.Int32[])">
            <summary>
            Set Oid value from integer array. If integer array is null or length == 0, internal buffer is set to null.
            </summary>
            <param name="value">Integer array</param>
            <exception cref="T:System.ArgumentNullException">Parameter is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Parameter contains less then 2 integer values</exception>
            <exception cref="T:System.OverflowException">Paramater contains a value that is less then zero. This is an invalid instance value</exception>
        </member>
        <member name="M:SnmpSharpNet.Oid.Set(System.UInt32[])">
            <summary>
            Set Oid value from integer array. If integer array is null or length == 0, internal buffer is set to null.
            </summary>
            <param name="value">Integer array</param>
            <exception cref="T:System.ArgumentNullException">Parameter is null</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Parameter contains less then 2 integer values</exception>
        </member>
        <member name="M:SnmpSharpNet.Oid.Set(SnmpSharpNet.Oid)">
            <summary>
            Set class value from another Oid class.
            </summary>
            <param name="value">Oid class</param>
            <exception cref="T:System.ArgumentNullException">Thrown when parameter is null</exception>
        </member>
        <member name="M:SnmpSharpNet.Oid.Set(System.String)">
            <summary> Sets the object to the passed dotted decimal 
            object identifier string.
            </summary>
            <param name="value">The dotted decimal object identifier.
            </param>
        </member>
        <member name="P:SnmpSharpNet.Oid.Length">
            <summary> Gets the number of object identifiers
            in the object.
            </summary>
            <returns> Returns the number of object identifiers
            </returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.Add(System.Int32[])">
            <summary> Add an array of identifiers to the current object.</summary>
            <param name="ids">The array Int32 identifiers to append to the object</param>
            <exception cref="T:System.OverflowException">Thrown when one of the instance IDs to add are less then zero</exception>
        </member>
        <member name="M:SnmpSharpNet.Oid.Add(System.UInt32[])">
            <summary> Add UInt32 identifiers to the current object.</summary>
            <param name="ids">The array of identifiers to append</param>
            <exception cref="T:System.OverflowException">Thrown when one of the instance IDs to add are less then zero</exception>
        </member>
        <member name="M:SnmpSharpNet.Oid.Add(System.UInt32)">
            <summary>Add a single UInt32 id to the end of the object</summary>
            <param name="id">Id to add to the oid</param>
        </member>
        <member name="M:SnmpSharpNet.Oid.Add(System.Int32)">
            <summary>Add a single Int32 id to the end of the object</summary>
            <param name="id">Id to add to the oid</param>
            <exception cref="T:System.OverflowException">Thrown when id value is less then zero</exception>
        </member>
        <member name="M:SnmpSharpNet.Oid.Add(System.String)">
            <summary> Converts the passed string to an object identifier
            and appends them to the current object.
            </summary>
            <param name="strOids">The dotted decimal identifiers to Append
            </param>
        </member>
        <member name="M:SnmpSharpNet.Oid.Add(SnmpSharpNet.Oid)">
            <summary> Appends the passed Oid object to 
            self.
            </summary>
            <param name="second">The object to Append to self
            </param>
        </member>
        <member name="M:SnmpSharpNet.Oid.Compare(System.UInt32[])">
            <summary>Compare Oid value with array of UInt32 integers</summary>
            <param name="ids">Array of integers</param>
            <returns>-1 if class is less then, 0 if the same or 1 if greater then the integer array value</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.Compare(System.UInt32[],System.Int32)">
            <summary>Compare class value with the contents of the array. Compare up to dist number of Oid values
            to determine equality.</summary>
            <param name="ids">Unsigned integer array to Compare with</param>
            <param name="dist">Number of oid instance values to compare</param>
            <returns>0 if equal, -1 if less then and 1 if greater then.</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.CompareExact(SnmpSharpNet.Oid)">
            <summary>
            Exact comparison of two Oid values
            </summary>
            <param name="oid">Oid to compare against</param>
            <returns>1 if class is greater then argument, -1 if class value is less then argument, 0 if the same</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.CompareExact(System.UInt32[])">
            <summary>
            Exact comparison of two Oid values
            </summary>
            <remarks>
            This method is required for cases when exact comparison is required and not lexographical comparison.
            
            This method will compare the lengths first and, if not the same, make a comparison determination based
            on it before looking into the data.
            </remarks>
            <param name="ids">Array of unsigned integers to compare against</param>
            <returns>1 if class is greater then argument, -1 if class value is less then argument, 0 if the same</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.Compare(SnmpSharpNet.Oid)">
            <summary>Compare objectId values</summary>
            <param name="cmp">ObjectId to Compare with</param>
            <returns>0 if equal, -1 if less then and 1 if greater then.</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.Equals(System.Object)">
            <summary> Test for equality. Returns true if 'o' is an instance of an Oid and is equal to self.</summary>
            <param name="obj">The object to be tested for equality.</param>
            <returns> True if the object is an Oid and is equal to self. False otherwise.</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.IsRootOf(SnmpSharpNet.Oid)">
            <summary>Compares the passed object identifier against self
            to determine if self is the root of the passed object.
            If the passed object is in the same root tree as self
            then a true value is returned. Otherwise a false value
            is returned from the object.
            </summary>
            <param name="leaf">The object to be tested
            </param>
            <returns> True if leaf is in the tree.
            </returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.CompareTo(System.Object)">
            <summary>
            IComparable interface implementation. Internally uses <see cref="M:SnmpSharpNet.Oid.CompareExact(SnmpSharpNet.Oid)"/> method to perform comparisons.
            </summary>
            <param name="obj"></param>
            <returns>1 if class is greater then argument, -1 if class value is less then argument, 0 if the same</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.GetData">
            <summary>
            Return internal integer array. This is required by static members of the class and other methods in
            this library so internal attribute is applied to it.
            </summary>
            <returns>Internal unsigned integer array buffer.</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.Reset">
            <summary>
            Reset class value to null
            </summary>
        </member>
        <member name="P:SnmpSharpNet.Oid.IsNull">
            <summary>
            Is Oid a null value or Oid equivalent (0.0)
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Oid.ToArray">
            <summary>
            Convert the Oid class to a integer array. Internal class data buffer is *copied* and not passed to the caller.
            </summary>
            <returns>Unsigned integer array representing the Oid class IDs</returns>
        </member>
        <member name="P:SnmpSharpNet.Oid.Item(System.Int32)">
            <summary>
            Access individual Oid values.
            </summary>
            <param name="index">Index of the Oid value to access (0 based)</param>
            <returns>Oid instance at index value</returns>
            <exception cref="T:System.OverflowException">Requested instance is outside the bounds of the Oid array</exception>
        </member>
        <member name="M:SnmpSharpNet.Oid.GetChildIdentifiers(SnmpSharpNet.Oid,SnmpSharpNet.Oid)">
            <summary>
            Return child components of the leaf OID.
            </summary>
            <param name="root">Root Oid</param>
            <param name="leaf">Leaf Oid</param>
            <returns>Returns int array of child OIDs, if there was an error or no child IDs are present, returns null.</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.ToString(System.Int32[])">
            <summary>
            Return a string formatted as OID value of the passed integer array
            </summary>
            <param name="vals">Array of integers</param>
            <returns>String formatted OID</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.ToString(System.Int32[],System.Int32)">
            <summary>
            Return a string formatted as OID value of the passed integer array starting at array item startpos.
            </summary>
            <param name="vals">Array of integers</param>
            <param name="startpos">Start position in the array. 0 based.</param>
            <returns>String formatted OID</returns>
            <exception cref="T:System.IndexOutOfRangeException">Thrown when start position is outside of the bounds of the available data.</exception>
        </member>
        <member name="M:SnmpSharpNet.Oid.op_Addition(SnmpSharpNet.Oid,System.UInt32[])">
            <summary>
            Add Oid class value and oid values in the integer array into a new class instance.
            </summary>
            <param name="oid">Oid class</param>
            <param name="ids">Unsigned integer array to add to the Oid</param>
            <returns>New Oid class with the two values added together</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.op_Addition(SnmpSharpNet.Oid,System.String)">
            <summary>
            Add Oid class value and oid represented as a string into a new Oid class instance
            </summary>
            <param name="oid">Oid class</param>
            <param name="strOids">string value representing an Oid</param>
            <returns>New Oid class with the new oid value.</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.op_Addition(SnmpSharpNet.Oid,SnmpSharpNet.Oid)">
            <summary>
            Add two Oid values and return the new class
            </summary>
            <param name="oid1">First Oid</param>
            <param name="oid2">Second Oid</param>
            <returns>New class with two Oid values added.</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.op_Addition(SnmpSharpNet.Oid,System.UInt32)">
            <summary>
            Add integer id to the Oid class
            </summary>
            <param name="oid1">Oid class to add id to</param>
            <param name="id">Id value to add to the oid</param>
            <returns>New Oid class with id added to the Oid class.</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.op_Explicit(SnmpSharpNet.Oid)~System.UInt32[]">
            <summary>
            Operator allowing explicit conversion from Oid class to integer array int[]
            </summary>
            <param name="oid">Oid to present as integer array int[]</param>
            <returns>Integer array representing the Oid class value</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.op_Equality(SnmpSharpNet.Oid,SnmpSharpNet.Oid)">
            <summary>
            Comparison of two Oid class values.
            </summary>
            <param name="oid1">First Oid class</param>
            <param name="oid2">Second Oid class</param>
            <returns>true if class values are same, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.op_Inequality(SnmpSharpNet.Oid,SnmpSharpNet.Oid)">
            <summary>
            Negative comparison of two Oid class values.
            </summary>
            <param name="oid1">First Oid class</param>
            <param name="oid2">Second Oid class</param>
            <returns>true if class values are not the same, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.op_GreaterThan(SnmpSharpNet.Oid,SnmpSharpNet.Oid)">
            <summary>
            Greater then operator.
            </summary>
            <remarks>Compare first oid with second and if first OID is greater return true</remarks>
            <param name="oid1">First oid</param>
            <param name="oid2">Second oid</param>
            <returns>True if first oid is greater then second, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.op_LessThan(SnmpSharpNet.Oid,SnmpSharpNet.Oid)">
            <summary>
            Less then operator.
            </summary>
            <remarks>Compare first oid with second and if first OID is less return true</remarks>
            <param name="oid1">First oid</param>
            <param name="oid2">Second oid</param>
            <returns>True if first oid is less then second, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.ToString">
            <summary> Converts the object identifier to a dotted decimal
            string representation.
            </summary>
            <returns> Returns the dotted decimal object id string.
            </returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.GetHashCode">
            <summary>Hash value for OID value
            </summary>
            <returns> The hash code for the object.
            </returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.Clone">
            <summary>Duplicate current object.</summary>
            <returns> Returns a new Oid copy of self cast as Object.</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.encode(SnmpSharpNet.MutableByte)">
            <summary>
            Encodes ASN.1 object identifier and append it to the end of the passed buffer.
            </summary>
            <param name="buffer">
            Buffer to append the encoded information to.
            </param>
        </member>
        <member name="M:SnmpSharpNet.Oid.encodeInstance(System.UInt32)">
            <summary>
            Encode single OID instance value
            </summary>
            <param name="number">Instance value</param>
            <returns>Encoded instance value</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.decode(System.Byte[],System.Int32)">
            <summary>Decode BER encoded Oid value.</summary>
            <param name="buffer">BER encoded buffer</param>
            <param name="offset">The offset location to begin decoding</param>
            <returns>Buffer position after the decoded value</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.Parse(System.String)">
            <summary>Parse string formatted oid value into an array of integers</summary>
            <param name="oidStr">string formatted oid</param>
            <returns>Integer array representing the oid or null if invalid object id was passed</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the Oid integer collection
            </summary>
            <returns>An IEnumerator  object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the Oid integer collection
            </summary>
            <returns>An IEnumerator  object that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:SnmpSharpNet.Oid.NullOid">
            <summary>
            Return instance of Oid class set to null value {0,0}
            </summary>
            <returns>Oid class instance set to null Oid value</returns>
        </member>
        <member name="T:SnmpSharpNet.Opaque">
            <summary>Opaque type is an application-wide type supports the capability to pass arbitrary
            ASN.1 syntax</summary>
            <remarks>SMIv2 defines Opaque type as provided solely for backward-compatibility, and
            shall not be used for newly-defined object types</remarks>
        </member>
        <member name="M:SnmpSharpNet.Opaque.#ctor">
            <summary>Constructor</summary>
        </member>
        <member name="M:SnmpSharpNet.Opaque.#ctor(System.Byte[])">
            <summary>Constructor</summary>
            <param name="data">Data</param>
        </member>
        <member name="M:SnmpSharpNet.Opaque.#ctor(SnmpSharpNet.Opaque)">
            <summary>Copy constructor</summary>
            <param name="second">The object to be duplicated.</param>
        </member>
        <member name="M:SnmpSharpNet.Opaque.#ctor(SnmpSharpNet.OctetString)">
            <summary>Constructor</summary>
            <param name="second">The object to be duplicated.</param>
        </member>
        <member name="M:SnmpSharpNet.Opaque.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="value">Initializer value</param>
        </member>
        <member name="M:SnmpSharpNet.Opaque.Clone">
            <summary>Returns a duplicate of the current object.</summary>
            <returns>Copy of the current object cast as Object</returns>
        </member>
        <member name="T:SnmpSharpNet.Sequence">
            <summary>
            Represents SNMP sequence
            </summary>
        </member>
        <member name="F:SnmpSharpNet.Sequence._data">
            <summary>
            data buffer
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Sequence.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Sequence.#ctor(System.Byte[])">
            <summary>
            Constructor.
            </summary>
            <param name="value">Sequence data</param>
        </member>
        <member name="M:SnmpSharpNet.Sequence.Set(System.Byte[])">
            <summary>
            Set sequence data
            </summary>
            <param name="value">Byte array containing BER encoded sequence data</param>
        </member>
        <member name="M:SnmpSharpNet.Sequence.encode(SnmpSharpNet.MutableByte)">
            <summary>
            BER encode sequence
            </summary>
            <param name="buffer">Target buffer</param>
        </member>
        <member name="M:SnmpSharpNet.Sequence.decode(System.Byte[],System.Int32)">
            <summary>
            Decode sequence from the byte array. Returned offset value is advanced by the size of the sequence header.
            </summary>
            <param name="buffer">Source data buffer</param>
            <param name="offset">Offset within the buffer to start parsing from</param>
            <returns>Returns offset position after the sequence header</returns>
        </member>
        <member name="P:SnmpSharpNet.Sequence.Value">
            <summary>
            Get sequence data
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Sequence.Clone">
            <summary>
            Clone sequence
            </summary>
            <returns>Cloned sequence cast as object</returns>
        </member>
        <member name="T:SnmpSharpNet.TimeTicks">
            <summary>SMI TimeTicks class</summary>
            <remarks>
            TimeTicks value is stored as an unsigned
            32-bit integer representing device uptime in 1/100s of a second time periods.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.TimeTicks.#ctor">
            <summary>Constructor</summary>
        </member>
        <member name="M:SnmpSharpNet.TimeTicks.#ctor(SnmpSharpNet.TimeTicks)">
            <summary>Constructor</summary>
            <param name="second">Initialize class with value from this class.</param>
        </member>
        <member name="M:SnmpSharpNet.TimeTicks.#ctor(SnmpSharpNet.UInteger32)">
            <summary>Constructor</summary>
            <param name="uint32">The UInteger32 value to initialize the class with.</param>
        </member>
        <member name="M:SnmpSharpNet.TimeTicks.#ctor(System.UInt32)">
            <summary>
            Constructor.
            </summary>
            <param name="value">Initialize the TimeTicks class to this unsigned integer value.</param>
        </member>
        <member name="M:SnmpSharpNet.TimeTicks.#ctor(System.String)">
            <summary>
            Constructor
            </summary>
            <param name="val">String holding 32-bit unsigned integer value to initialize the class with
            </param>
        </member>
        <member name="M:SnmpSharpNet.TimeTicks.Clone">
            <summary>Duplicate object</summary>
            <returns>Object cloned copy cast to Object</returns>
        </member>
        <member name="M:SnmpSharpNet.TimeTicks.op_Explicit(SnmpSharpNet.TimeTicks)~System.TimeSpan">
            <summary>
            Operator to allow explicit conversion of TimeTicks class to a TimeSpan class.
            </summary>
            <param name="value">TimeTicks class to convert to TimeSpan</param>
            <returns>TimeSpan value representing the value of TimeTicks class.</returns>
        </member>
        <member name="P:SnmpSharpNet.TimeTicks.Milliseconds">
            <summary>
            Get TimeTicks value as milliseconds value.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.TimeTicks.ToString">
            <summary>
            Returns the string representation of the object.
            </summary>
            <returns>String representation of the class value.</returns>
        </member>
        <member name="M:SnmpSharpNet.TimeTicks.GetHashCode">
            <summary>
            Return hash code for the class.
            </summary>
            <returns>Hash code is the hash code of the class value.</returns>
        </member>
        <member name="T:SnmpSharpNet.UInteger32">
            <summary>SMI unsigned 32-bit integer value class.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UInteger32._value">
            <summary>Internal unsigned integer 32-bit value
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.#ctor">
            <summary>Constructor. Class value is set to 0.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.#ctor(System.UInt32)">
            <summary>Constructor. SET the class value to the supplied 32-bit value.
            </summary>
            <param name="val">Value to initialize the class with.
            </param>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.#ctor(SnmpSharpNet.UInteger32)">
            <summary>Constructor. Initializes the class to the same value as the argument.
            </summary>
            <param name="second">Object whose value is used to initialize this class.
            </param>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.#ctor(System.String)">
            <summary>Constructor. Initialize the class with the unsigned integer 32-bit value
            stored as a string value in the argument.
            </summary>
            <param name="val">Unsigned integer value encoded as a string
            </param>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.Set(System.String)">
            <summary>
            Parse an UInteger32 value from a string.
            </summary>
            <param name="value">String containing an UInteger32 value</param>
            <exception cref="T:System.ArgumentException">Argument string is length == 0</exception>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.Set(SnmpSharpNet.AsnType)">
            <summary>
            SET class value from another UInteger32 or Integer32 class cast as <seealso cref="T:SnmpSharpNet.AsnType"/>.
            </summary>
            <param name="value">UInteger32 class cast as <seealso cref="T:SnmpSharpNet.AsnType"/></param>
            <exception cref="T:System.ArgumentNullException">Parameter is null.</exception>
        </member>
        <member name="P:SnmpSharpNet.UInteger32.Value">
            <summary>
            Value of the object. Returns 32 bit unsigned integer
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.ToString">
            <summary> 
            Returns the string representation of the object.
            </summary>
            <returns>String representation of the of the class value.</returns>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.Clone">
            <summary>
            Returns a duplicate of the current object
            </summary>
            <returns>
            A duplicate copy of the current object
            </returns>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.op_Implicit(SnmpSharpNet.UInteger32)~System.UInt32">
            <summary>
            Implicit casting of the object value as UInt32 value
            </summary>
            <param name="value">UInteger32 class</param>
            <returns>UInt32 value stored by the UInteger32 class</returns>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.encode(SnmpSharpNet.MutableByte)">
            <summary>BER encode class value.</summary>
            <param name="buffer">Target buffer. Value is appended to the end of it.</param>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.decode(System.Byte[],System.Int32)">
            <summary>Decode BER encoded value</summary>
            <remarks>
            Used to decode the integer value from the ASN.1 buffer.
            The passed encoder is used to decode the ASN.1 information
            and the integer value is stored in the internal object.
            </remarks>
            <param name="buffer">BER encoded buffer</param>
            <param name="offset">The offset of the first byte of data to decode. This variable will hold the offset of the first byte
            immediately after the value we decoded.</param>
            <returns>Buffer position after the decoded value</returns>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.CompareTo(SnmpSharpNet.UInteger32)">
            <summary>
            Compare implementation that will compare this class value with the value of another <see cref="T:SnmpSharpNet.UInteger32"/> class.
            </summary>
            <param name="other">UInteger32 value to compare class value with.</param>
            <returns>True if values are the same, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.CompareTo(System.UInt32)">
            <summary>
            Compare implementation that will compare this class value with argument UInt32 value.
            </summary>
            <param name="other">UInt32 value to compare class value with.</param>
            <returns>True if values are the same, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.Equals(System.Object)">
            <summary>
            Compare class value against the object argument. Supported argument types are 
            <see cref="T:SnmpSharpNet.UInteger32"/> and Int32.
            </summary>
            <param name="obj">Object to compare values with</param>
            <returns>True if object value is the same as this class, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.op_Equality(SnmpSharpNet.UInteger32,SnmpSharpNet.UInteger32)">
            <summary>
            Comparison operator
            </summary>
            <param name="first">First <see cref="T:SnmpSharpNet.UInteger32"/> class value to compare</param>
            <param name="second">Second <see cref="T:SnmpSharpNet.UInteger32"/> class value to compare</param>
            <returns>True if class values match, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.op_Inequality(SnmpSharpNet.UInteger32,SnmpSharpNet.UInteger32)">
            <summary>
            Negative comparison operator
            </summary>
            <param name="first">First <see cref="T:SnmpSharpNet.UInteger32"/> class value to compare</param>
            <param name="second">Second <see cref="T:SnmpSharpNet.UInteger32"/> class value to compare</param>
            <returns>True if class values do NOT match, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.op_GreaterThan(SnmpSharpNet.UInteger32,SnmpSharpNet.UInteger32)">
            <summary>
            Greater then operator
            </summary>
            <remarks>Compare two UInteger32 class values and return true if first class value is greater then second.</remarks>
            <param name="first">First class</param>
            <param name="second">Second class</param>
            <returns>True if first class value is greater then second class value, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.op_LessThan(SnmpSharpNet.UInteger32,SnmpSharpNet.UInteger32)">
            <summary>
            Less then operator
            </summary>
            <remarks>Compare two UInteger32 class values and return true if first class value is less then second.</remarks>
            <param name="first">First class</param>
            <param name="second">Second class</param>
            <returns>True if first class value is less then second class value, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.GetHashCode">
            <summary>
            Returns hashed class value.
            </summary>
            <returns>Int32 hash value</returns>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.op_Addition(SnmpSharpNet.UInteger32,SnmpSharpNet.UInteger32)">
            <summary>
            Addition operator.
            </summary>
            <remarks>
            Add two UInteger32 object values. Values of the two objects are added and
            a new class is instantiated with the result. Original values of the two parameter classes
            are preserved.
            </remarks>
            <param name="first">First UInteger32 object</param>
            <param name="second">Second UInteger32 object</param>
            <returns>New object with values of the 2 parameter objects added. If both parameters are null
            references then null is returned. If either of the two parameters is null, the non-null objects
            value is set as the value of the new object and returned.</returns>
        </member>
        <member name="M:SnmpSharpNet.UInteger32.op_Subtraction(SnmpSharpNet.UInteger32,SnmpSharpNet.UInteger32)">
            <summary>
            Subtraction operator
            </summary>
            <remarks>
            Subtract the value of the second UInteger32 class value from the first UInteger32 class value. 
            Values of the two objects are subtracted and a new class is instantiated with the result. 
            Original values of the two parameter classes are preserved.
            </remarks>
            <param name="first">First UInteger32 object</param>
            <param name="second">Second UInteger32 object</param>
            <returns>New object with subtracted values of the 2 parameter objects. If both parameters are null
            references then null is returned. If either of the two parameters is null, the non-null objects
            value is set as the value of the new object and returned.</returns>
        </member>
        <member name="T:SnmpSharpNet.V2Error">
            <summary>Base class for SNMP version 2 error types.</summary>
            <remarks>
            For details see <see cref="T:SnmpSharpNet.NoSuchInstance"/>, <see cref="T:SnmpSharpNet.NoSuchObject"/> and <see cref="T:SnmpSharpNet.EndOfMibView"/>.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.V2Error.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:SnmpSharpNet.V2Error.#ctor(SnmpSharpNet.V2Error)">
            <summary>Constructor.</summary>
            <remarks>
            Since this class doesn't hold any meaningful information, constructor
            does nothing with the argument.
            </remarks>
            <param name="second">Second object</param>
        </member>
        <member name="M:SnmpSharpNet.V2Error.encode(SnmpSharpNet.MutableByte)">
            <summary>BER encode SNMP version 2 error.</summary>
            <param name="buffer">Buffer to append encoded value to the end of</param>
        </member>
        <member name="M:SnmpSharpNet.V2Error.decode(System.Byte[],System.Int32)">
            <summary>Decode BER encoded SNMP version 2 error.</summary>
            <param name="buffer">BER encoded buffer</param>
            <param name="offset">Offset within the buffer to start decoding the value from. This argument will
            receive the new offset to the byte immediately following the decoded value.</param>
            <returns>Buffer position after the decoded value</returns>
        </member>
        <member name="M:SnmpSharpNet.V2Error.Clone">
            <summary>Returns a duplicate of the object.
            </summary>
            <returns>A new copy of the current object cast as System.Object.
            </returns>
        </member>
        <member name="T:SnmpSharpNet.V2PartyClock">
            <summary>Defines an SNMPv2 Party Clock.</summary>
            <remarks>
            The Party Clock is currently
            Obsolete, but included for backwards compatibility. Obsoleted in RFC 1902.
            </remarks>
        </member>
        <member name="M:SnmpSharpNet.V2PartyClock.#ctor">
            <summary>Constructor</summary>
        </member>
        <member name="M:SnmpSharpNet.V2PartyClock.#ctor(SnmpSharpNet.V2PartyClock)">
            <summary>Constructor</summary>
            <param name="second">Class to duplicate</param>
        </member>
        <member name="M:SnmpSharpNet.V2PartyClock.#ctor(SnmpSharpNet.UInteger32)">
            <summary>Constructor</summary>
            <param name="uint32">Value to initialize the class with.</param>
        </member>
        <member name="M:SnmpSharpNet.V2PartyClock.Clone">
            <summary>Clone class</summary>
            <returns>Cloned class cast as object</returns>
        </member>
        <member name="M:SnmpSharpNet.V2PartyClock.ToString">
            <summary>Returns the string representation of the object.</summary>
            <returns>String representation of the class value.</returns>
        </member>
        <member name="T:SnmpSharpNet.SnmpAsyncResponse">
            <summary>
            Callback used to pass result of an async SNMP operation back to the caller.
            </summary>
            <param name="result">Result code of the operation.</param>
            <param name="packet">SNMP packet received</param>
        </member>
        <member name="T:SnmpSharpNet.UdpTarget">
            <summary>Transport class for IPv4 using UDP</summary>
            <remarks>
            InternetProtocol version 4 User Datagram Protocol (IP/UDP) transport protocol
            implementation for use with SNMP versions 1, 2 and 3.
            </remarks>
        </member>
        <member name="F:SnmpSharpNet.UdpTarget._address">
            <summary>
            SNMP request target host IP address
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UdpTarget._retry">
            <summary>
            Maximum number of retries. Value of 0 (zero) will result in a single request without
            retries.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UdpTarget._port">
            <summary>
            SNMP target UDP port number
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UdpTarget._timeout">
            <summary>
            SNMP request timeout value in milliseconds
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UdpTarget.Address">
            <summary>
            Get/Set Udp agent IP address
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UdpTarget.Port">
            <summary>
            Get/Set Udp agent port number
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UdpTarget.Timeout">
            <summary>
            Get/Set Udp agent timeout value in milliseconds
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UdpTarget.Retry">
            <summary>
            Get/Set Udp agent maximum retry value. Value of 0 (zero) will result in a single request
            being sent without further retry attempts.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UdpTarget.#ctor(System.Net.IPAddress,System.Int32,System.Int32,System.Int32)">
            <summary>
            Constructor.
            </summary>
            <param name="peer">SNMP peer IP address</param>
            <param name="port">SNMP peer UDP port number</param>
            <param name="timeout">SNMP peer timeout in milliseconds</param>
            <param name="retry">SNMP peer maximum retires setting. Value of 0 will result in a single request with no retries.</param>
        </member>
        <member name="M:SnmpSharpNet.UdpTarget.#ctor(System.Net.IPAddress)">
            <summary>
            Constructor
            </summary>
            <remarks>
            Initializes the class with defaults for timeout (2000ms = 2 seconds), retry (two) and agent UDP port
            number (161).
            </remarks>
            <param name="peer">Agent IP address</param>
        </member>
        <member name="M:SnmpSharpNet.UdpTarget.Request(SnmpSharpNet.Pdu,SnmpSharpNet.IAgentParameters)">
            <summary>Make SNMP Request</summary>
            <remarks>
            Make SNMP request. With this method you can make blocked SNMP version 1, 2 and 3 requests of type GET,
            GET-NEXT, GET-BULK, SET and REPORT (request types have to compatible with the SNMP protocol version you
            are using).
            
            This method will pass through any exceptions thrown by parsing classes/methods so see individual packet
            classes, ASN.1 type classes, authentication, privacy, etc. classes for exceptions thrown.
            </remarks>
            <param name="pdu">Pdu class (do not pass ScopedPdu)</param>
            <param name="agentParameters">Security information for the request. Use <see cref="T:SnmpSharpNet.AgentParameters"/>
            for SNMP versions 1 and 2 requests. Use <see cref="T:SnmpSharpNet.SecureAgentParameters"/> for SNMP version 3
            requests.</param>
            <returns>Appropriate SNMP packet class for the reply received (<see cref="T:SnmpSharpNet.SnmpV1Packet"/>, 
            <see cref="T:SnmpSharpNet.SnmpV2Packet"/>, or <see cref="T:SnmpSharpNet.SnmpV3Packet"/>. Null value if there was an error
            with the request.</returns>
            <exception cref="T:SnmpSharpNet.SnmpAuthenticationException">Thrown on SNMPv3 requests when authentication password
            is not specified on authNoPriv or authPriv requests in SecureAgentParameters or if incoming packet 
            authentication check failed.
            
            With SNMP ver1 and ver2c, authentication check fails when invalid community name is parsed in the reply.</exception>
            <exception cref="T:SnmpSharpNet.SnmpPrivacyException">Thrown on SNMPv3 requests when privacy password is not
            specified in SecureAgentParameters on authPriv requests.</exception>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown in following cases:
            
            * IAgentParameters.Valid() returned false. SnmpException.ErrorCode is set to SnmpException.InvalidIAgentParameters
            * No data received on request. SnmpException.ErrorCode is set to SnmpException.NoDataReceived
            * Invalid RequestId in reply. SnmpException.ErrorCode is set to SnmpException.InvalidRequestId
            </exception>
        </member>
        <member name="E:SnmpSharpNet.UdpTarget._response">
            <summary>
            Internal event to send result of the async request to.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UdpTarget._agentParameters">
            <summary>
            Internal storage of the agent parameters information passed to the async request member function.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UdpTarget.RequestAsync(SnmpSharpNet.Pdu,SnmpSharpNet.IAgentParameters,SnmpSharpNet.SnmpAsyncResponse)">
            <summary>
            Make SNMP request. With this method you can make blocked SNMP version 1, 2 and 3 requests of type GET,
            GET-NEXT, GET-BULK, SET and REPORT (request types have to compatible with the SNMP protocol version you
            are using).
            
            This method will pass through any exceptions thrown by parsing classes/methods so see individual packet
            classes, ASN.1 type classes, authentication, privacy, etc. classes for exceptions thrown.
            </summary>
            <param name="pdu">Pdu class (do not pass ScopedPdu)</param>
            <param name="agentParameters">Security information for the request. Use <see cref="T:SnmpSharpNet.AgentParameters"/>
            for SNMP versions 1 and 2 requests. Use <see cref="T:SnmpSharpNet.SecureAgentParameters"/> for SNMP version 3
            requests.</param>
            <param name="responseCallback">Callback that receives the result of the async operation.</param>
            <returns>True if async request was successfully initiated, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.UdpTarget.Discovery(SnmpSharpNet.SecureAgentParameters)">
            <summary>
            Perform SNMP version 3 discovery operation. This is the first operation that needs to be
            performed on a newly accessed agent to retrieve agentId, agentBoots and agentTime values, critical
            for further authentication and privacy operations.
            </summary>
            <param name="param"><see cref="T:SnmpSharpNet.SecureAgentParameters"/> class instance that will be updated
            with discovered agent values. This class with be reset to its defaults prior to agent
            discovered values so do not store any critical information in it prior to calling the
            discovery method</param>
            <returns>True if discovery operation was a success, otherwise false</returns>
        </member>
        <member name="M:SnmpSharpNet.UdpTarget.DiscoveryAsync(SnmpSharpNet.SecureAgentParameters,SnmpSharpNet.SnmpAsyncResponse)">
            <summary>
            Make an async discovery request for protocol version 3.
            </summary>
            <param name="param">Agent parameters</param>
            <param name="callback">Callback method</param>
            <returns>True if operation was correctly initiated, otherwise false.</returns>
        </member>
        <member name="T:SnmpSharpNet.SnmpAsyncCallback">
            <summary>
            Async delegate called on completion of Async SNMP request
            </summary>
            <param name="status">SNMP request status. If status is NoError then pdu will contain valid information</param>
            <param name="peer">Peer IP and port number. This value is only valid if status is NoError</param>
            <param name="buffer">Returned data. This value is only valid if status is NoError</param>
            <param name="length">Length of the returned data. This value is only valid if status is NoError.</param>
        </member>
        <member name="T:SnmpSharpNet.UdpTransport">
            <summary>
            IP/UDP transport class.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UdpTransport._socket">
            <summary>
            Socket
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UdpTransport._isIPv6">
            <summary>
            Flag showing if class is using IPv6 or IPv4
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UdpTransport._noSourceCheck">
            <summary>
            Internal variable used to disable host IP address/port number check on received SNMP reply packets. If this option is disabled (default)
            only replies from the IP address/port number combination to which the request was sent will be accepted as valid packets.
            
            This value is set in the AgentParameters class and is only valid for SNMP v1 and v2c requests.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UdpTransport.#ctor(System.Boolean)">
            <summary>
            Constructor. Initializes and binds the Socket class
            </summary>
            <param name="useV6">Set to true if you wish to initialize the transport for IPv6</param>
        </member>
        <member name="M:SnmpSharpNet.UdpTransport.Finalize">
            <summary>
            Destructor
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UdpTransport.IsIPv6">
            <summary>
            Flag used to determine if class is using IP version 6 (true) or IP version 4 (false)
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UdpTransport.initSocket(System.Boolean)">
            <summary>
            Initialize class socket
            </summary>
            <param name="useV6">Should socket be initialized for IPv6 (true) of IPv4 (false)</param>
        </member>
        <member name="M:SnmpSharpNet.UdpTransport.Request(System.Net.IPAddress,System.Int32,System.Byte[],System.Int32,System.Int32,System.Int32)">
            <summary>
            Make sync request using IP/UDP with request timeouts and retries.
            </summary>
            <param name="peer">SNMP agent IP address</param>
            <param name="port">SNMP agent port number</param>
            <param name="buffer">Data to send to the agent</param>
            <param name="bufferLength">Data length in the buffer</param>
            <param name="timeout">Timeout in milliseconds</param>
            <param name="retries">Maximum number of retries. 0 = make a single request with no retry attempts</param>
            <returns>Byte array returned by the agent. Null on error</returns>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown on request timed out. SnmpException.ErrorCode is set to
            SnmpException.RequestTimedOut constant.</exception>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown when IPv4 address is passed to the v6 socket or vice versa</exception>
        </member>
        <member name="E:SnmpSharpNet.UdpTransport._asyncCallback">
            <summary>
            SNMP request internal callback
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UdpTransport._busy">
            <summary>
            Internal flag used to mark the class busy or available. Any request made when this
            flag is set to true will fail with OperationInProgress error.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.UdpTransport.IsBusy">
            <summary>
            Is class busy. This property is true when class is servicing another request, false if
            ready to process a new request.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UdpTransport._requestState">
            <summary>
            Async request state information.
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UdpTransport._inBuffer">
            <summary>
            Incoming data buffer
            </summary>
        </member>
        <member name="F:SnmpSharpNet.UdpTransport._receivePeer">
            <summary>
            Receiver IP end point
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UdpTransport.RequestAsync(System.Net.IPAddress,System.Int32,System.Byte[],System.Int32,System.Int32,System.Int32,SnmpSharpNet.SnmpAsyncCallback)">
            <summary>
            Begin an async SNMP request
            </summary>
            <param name="peer">Pdu to send to the agent</param>
            <param name="port">Callback to receive response from the agent</param>
            <param name="buffer">Buffer containing data to send to the peer</param>
            <param name="bufferLength">Length of data in the buffer</param>
            <param name="timeout">Request timeout in milliseconds</param>
            <param name="retries">Maximum retry count. 0 = single request no further retries.</param>
            <param name="asyncCallback">Callback that will receive the status and result of the operation</param>
            <returns>Returns false if another request is already in progress or if socket used by the class
            has been closed using Dispose() member, otherwise true</returns>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown when IPv4 address is passed to the v6 socket or vice versa</exception>
        </member>
        <member name="M:SnmpSharpNet.UdpTransport.SendToBegin">
            <summary>
            Calls async version of the SendTo socket function.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UdpTransport.SendToCallback(System.IAsyncResult)">
            <summary>
            Callback member called on completion of BeginSendTo send data operation.
            </summary>
            <param name="ar">Async result</param>
        </member>
        <member name="M:SnmpSharpNet.UdpTransport.ReceiveBegin">
            <summary>
            Begin async version of ReceiveFrom member of the socket class.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UdpTransport.RetryAsyncRequest">
            <summary>
            Internal retry function. Checks if request has reached maximum number of retries and either resends the request if not reached,
            or sends request timed-out notification to the caller if maximum retry count has been reached and request has failed.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UdpTransport.ReceiveFromCallback(System.IAsyncResult)">
            <summary>
            Internal callback called as part of Socket.BeginReceiveFrom. Process incoming packets and notify caller
            of results.
            </summary>
            <param name="ar">Async call result used by <seealso cref="M:System.Net.Sockets.Socket.EndReceiveFrom(System.IAsyncResult,System.Net.EndPoint@)"/></param>
        </member>
        <member name="M:SnmpSharpNet.UdpTransport.AsyncRequestTimerCallback(System.Object)">
            <summary>
            Internal timer callback. Called by _asyncTimer when SNMP request timeout has expired
            </summary>
            <param name="stateInfo">State info. Always null</param>
        </member>
        <member name="M:SnmpSharpNet.UdpTransport.Dispose">
            <summary>
            Dispose of the class.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.UdpTransport.Close">
            <summary>
            Close network socket
            </summary>
        </member>
        <member name="T:SnmpSharpNet.Vb">
            <summary>
            Vb item. Stores Oid => value pair for each value
            </summary>
        </member>
        <member name="F:SnmpSharpNet.Vb._oid">
            <summary>
            OID of the object
            </summary>
        </member>
        <member name="F:SnmpSharpNet.Vb._value">
            <summary>
            Value of the object
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Vb.#ctor">
            <summary>
            Standard constructor. Initializes values to null.
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Vb.#ctor(SnmpSharpNet.Oid)">
            <summary>
            Construct Vb with the supplied OID and Null value
            </summary>
            <param name="oid">OID</param>
        </member>
        <member name="M:SnmpSharpNet.Vb.#ctor(SnmpSharpNet.Oid,SnmpSharpNet.AsnType)">
            <summary>
            Construct Vb with the OID and value
            </summary>
            <param name="oid">OID</param>
            <param name="value">Value</param>
        </member>
        <member name="M:SnmpSharpNet.Vb.#ctor(System.String)">
            <summary>
            Construct Vb with the oid value and <seealso cref="T:SnmpSharpNet.Null"/> value.
            </summary>
            <param name="oid">String representing OID value to set</param>
        </member>
        <member name="M:SnmpSharpNet.Vb.#ctor(SnmpSharpNet.Vb)">
            <summary>
            Copy constructor. Initialize class with cloned values from second class.
            </summary>
            <param name="second">Vb class to clone data from.</param>
        </member>
        <member name="M:SnmpSharpNet.Vb.Set(SnmpSharpNet.Vb)">
            <summary>
            SET class value from supplied Vb class
            </summary>
            <param name="value">Vb class to clone data from</param>
        </member>
        <member name="P:SnmpSharpNet.Vb.Value">
            <summary>
            SET/Get AsnType value of the Vb
            </summary>
        </member>
        <member name="P:SnmpSharpNet.Vb.Oid">
            <summary>
            Get/SET OID of the Vb
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Vb.ResetValue">
            <summary>
            Reset Vb value to Null
            </summary>
        </member>
        <member name="M:SnmpSharpNet.Vb.Clone">
            <summary>
            Clone Vb object
            </summary>
            <returns>Cloned Vb object cast to System.Object</returns>
        </member>
        <member name="M:SnmpSharpNet.Vb.ToString">
            <summary>
            Return printable string in the format oid: value
            </summary>
            <returns>Format Vb string</returns>
        </member>
        <member name="M:SnmpSharpNet.Vb.encode(SnmpSharpNet.MutableByte)">
            <summary>BER encode the variable binding
            </summary>
            <param name="buffer"><see cref="T:SnmpSharpNet.MutableByte"/> class to the end of which encoded variable
            binding values will be added.
            </param>
        </member>
        <member name="M:SnmpSharpNet.Vb.decode(System.Byte[],System.Int32)">
            <summary>Decode BER encoded variable binding.
            </summary>
            <param name="buffer">BER encoded buffer
            </param>
            <param name="offset">Offset in the data buffer from where to start decoding. Offset is
            passed by reference and will contain the offset of the byte immediately after the parsed
            variable binding.
            </param>
            <returns>Buffer position after the decoded value</returns>
        </member>
        <member name="T:SnmpSharpNet.VbCollection">
            <summary>
            Variable Binding collection
            </summary>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.#ctor">
            <summary>
            Standard constructor
            </summary>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.#ctor(System.Collections.Generic.IEnumerable{SnmpSharpNet.Vb})">
            <summary>
            Copy constructor
            </summary>
        </member>
        <member name="P:SnmpSharpNet.VbCollection.Count">
            <summary>
            Get number of VarBind entries in the collection
            </summary>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.Clear">
            <summary>
            Reset the VarBind collection.
            </summary>
        </member>
        <member name="P:SnmpSharpNet.VbCollection.Item(System.Int32)">
            <summary>
            Indexed access to VarBind collection.
            </summary>
            <param name="index">Index position of the VarBind entry</param>
            <returns>VarBind entry at the specified index</returns>
            <exception cref="T:System.IndexOutOfRangeException">Thrown when index is outside the bounds of the collection</exception>
        </member>
        <member name="P:SnmpSharpNet.VbCollection.Item(SnmpSharpNet.Oid)">
            <summary>
            Access variable bindings using Vb Oid value
            </summary>
            <param name="oid">Required Oid value</param>
            <returns>Variable binding with the Oid matching the parameter, otherwise null</returns>
        </member>
        <member name="P:SnmpSharpNet.VbCollection.Item(System.String)">
            <summary>
            Access variable bindings using Vb Oid value in the string format
            </summary>
            <param name="oid">Oid value in string representation</param>
            <returns>Variable binding with the Oid matching the parameter, otherwise null</returns>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.RemoveAt(System.Int32)">
            <summary>
            Remove VarBind entry for the specified position in the collection.
            </summary>
            <param name="pos">Position of the entry to remove (zero based)</param>
            <exception cref="T:System.IndexOutOfRangeException">Thrown when position is outside the bounds of the collection</exception>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.Insert(System.Int32,SnmpSharpNet.Vb)">
            <summary>
            Insert VarBind item at specific location
            </summary>
            <param name="pos">Position (zero based)</param>
            <param name="item">VarBind item to insert</param>
            <exception cref="T:System.IndexOutOfRangeException">Thrown when position is outside the bounds of the collection</exception>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.Add(SnmpSharpNet.Vb)">
            <summary>
            Add variable binding to the collection
            </summary>
            <param name="vb">VarBind item to add to the collection</param>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.Add(System.String)">
            <summary>
            Create a new Variable Binding with the supplied Oid and SnmpNull value and add it to the end of Vb collection
            </summary>
            <param name="oid">Oid value in dotted decimal format</param>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.Add(SnmpSharpNet.Oid)">
            <summary>
            Add Vb with the supplied OID and value of SnmpNull to the end of the Vb collection
            </summary>
            <param name="oid">OID to assign to the new Vb</param>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.Add(SnmpSharpNet.Oid,SnmpSharpNet.AsnType)">
            <summary>
            Create a new Variable Binding with the supplied OID and value and add to the end of the Vb collection
            </summary>
            <param name="oid">OID to assign to the new Vb</param>
            <param name="value">SNMP value to assign to the new Vb</param>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.Add(System.Collections.Generic.IEnumerable{SnmpSharpNet.Vb})">
            <summary>
            Add content of the enumerable collection of Variable Bindings to the end of the Vb collection.
            </summary>
            <param name="varList">Variable Binding collection.</param>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.Add(System.Collections.Generic.IEnumerable{SnmpSharpNet.Oid})">
            <summary>
            Construct Variable Bindings from the enumerable collections of OIDs, each with the value of SnmpNull and add to the end of the Vb collection
            </summary>
            <param name="oidList">Enumerable collection of OIDs</param>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.ContainsOid(SnmpSharpNet.Oid)">
            <summary>
            Does the collection contain variable binding with Oid matching the parameter
            </summary>
            <param name="oid">Oid to search for</param>
            <returns>True if collection contains a variable binding with the Oid match the parameter, otherwise false.</returns>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.OidArray">
            <summary>
            Get array of Oid keys stored in collection
            </summary>
            <returns>Array of clone Oid keys</returns>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.GetEnumerator">
            <summary>
            Get enumerator.
            </summary>
            <returns>Enumerator</returns>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Get enumerator.
            </summary>
            <returns>Enumerator</returns>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.Clone">
            <summary>
            Duplicate Vbs object
            </summary>
            <returns>Duplicate of the Vbs object</returns>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.encode(SnmpSharpNet.MutableByte)">
            <summary>
            Encode VarBind collection sequence
            </summary>
            <param name="buffer">Target buffer. Encoded VarBind collection is appended.</param>
        </member>
        <member name="M:SnmpSharpNet.VbCollection.decode(System.Byte[],System.Int32)">
            <summary>
            Decode VarBind collection sequence.
            </summary>
            <param name="buffer">Buffer containing BER encoded VarBind collection</param>
            <param name="offset">Offset to start decoding from</param>
            <returns>New offset of the position following the VarBind collection</returns>
            <exception cref="T:SnmpSharpNet.SnmpException">Thrown when parsed ASN.1 type is not a VarBind collection sequence type</exception>
        </member>
    </members>
</doc>
