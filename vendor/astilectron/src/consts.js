'use strict';

module.exports = {
    eventNames: {
        appEventReady: "app.event.ready",
        displayEventAdded: "display.event.added",
        displayEventMetricsChanged: "display.event.metrics.changed",
        displayEventRemoved: "display.event.removed",
        ipcWindowMessage: "ipc.window.message",
        menuCmdCreate: "menu.cmd.create",
        menuEventCreated: "menu.event.created",
        menuItemCmdSetChecked: "menu.item.cmd.set.checked",
        menuItemCmdSetEnabled: "menu.item.cmd.set.enabled",
        menuItemCmdSetLabel: "menu.item.cmd.set.label",
        menuItemCmdSetVisible: "menu.item.cmd.set.visible",
        menuItemEventCheckedSet: "menu.item.event.checked.set",
        menuItemEventClicked: "menu.item.event.clicked",
        menuItemEventEnabledSet: "menu.item.event.enabled.set",
        menuItemEventLabelSet: "menu.item.event.label.set",
        menuItemEventVisibleSet: "menu.item.event.visible.set",
        subMenuCmdAppend: "sub.menu.cmd.append",
        subMenuCmdClosePopup: "sub.menu.cmd.close.popup",
        subMenuCmdInsert: "sub.menu.cmd.insert",
        subMenuCmdPopup: "sub.menu.cmd.popup",
        subMenuEventAppended: "sub.menu.event.appended",
        subMenuEventClosedPopup: "sub.menu.event.closed.popup",
        subMenuEventInserted: "sub.menu.event.inserted",
        subMenuEventPoppedUp: "sub.menu.event.popped.up",
        windowCmdBlur: "window.cmd.blur",
        windowCmdCenter: "window.cmd.center",
        windowCmdClose: "window.cmd.close",
        windowCmdCreate: "window.cmd.create",
        windowCmdDestroy: "window.cmd.destroy",
        windowCmdFocus: "window.cmd.focus",
        windowCmdHide: "window.cmd.hide",
        windowCmdMaximize: "window.cmd.maximize",
        windowCmdMessage: "window.cmd.message",
        windowCmdMinimize: "window.cmd.minimize",
        windowCmdMove: "window.cmd.move",
        windowCmdResize: "window.cmd.resize",
        windowCmdRestore: "window.cmd.restore",
        windowCmdShow: "window.cmd.show",
        windowCmdUnmaximize: "window.cmd.unmaximize",
        windowCmdWebContentsCloseDevTools: "window.cmd.web.contents.close.dev.tools",
        windowCmdWebContentsOpenDevTools: "window.cmd.web.contents.open.dev.tools",
        windowEventBlur: "window.event.blur",
        windowEventClosed: "window.event.closed",
        windowEventDidFinishLoad: "window.event.did.finish.load",
        windowEventFocus: "window.event.focus",
        windowEventHide: "window.event.hide",
        windowEventMaximize: "window.event.maximize",
        windowEventMessage: "window.event.message",
        windowEventMinimize: "window.event.minimize",
        windowEventMove: "window.event.move",
        windowEventReadyToShow: "window.event.ready.to.show",
        windowEventResize: "window.event.resize",
        windowEventRestore: "window.event.restore",
        windowEventShow: "window.event.show",
        windowEventUnmaximize: "window.event.unmaximize",
        windowEventUnresponsive: "window.event.unresponsive",
    },
    mainTargetID: 'main'
};
